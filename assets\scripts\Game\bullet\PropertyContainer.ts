import { _decorator, Component } from "cc";
const { ccclass, property } = _decorator;

// Expression wrapper
interface Expression {
    raw: string;             // the original expression text
    compiled?: Function;     // compiled JS function
}

export interface IProperty {
    get isDirty(): boolean;
    notify(force?: boolean): void;
}

export class Property<T> implements IProperty {
    private _value: T;
    private _isDirty: boolean = false;
    // listeners
    private _listeners: Array<(value: T) => void> = [];

    public constructor(value: T) {
        this._value = value;
        this._isDirty = false;
    }

    get isDirty(): boolean {
        return this._isDirty;
    }

    setDirty(value: boolean): void {
        this._isDirty = value;
    }

    get value(): T {
        return this._value;
    }

    set value(newValue: T) {
        if (this._value === newValue) return;

        this._value = newValue;
        this.setDirty(true);
    }

    public on(listener: (value: T) => void): void {
        this._listeners.push(listener);
    }

    public off(listener: (value: T) => void): void {
        this._listeners = this._listeners.filter(l => l !== listener);
    }

    public notify(force: boolean = false): void {
        if (force || this.isDirty) {
            this._listeners.forEach(listener => listener(this._value));
            this.setDirty(false);
        }
    }
}

export class PropertyContainer<K> {
    private _properties: Map<K, IProperty> = new Map();

    public addProperty<T>(key: K, value: T): Property<T> {
        let property = this._properties.get(key) as Property<T> | undefined;
        if (property) {
            property.value = value;
        } else {
            property = new Property<T>(value);
            this._properties.set(key, property);
        }

        return property;
    }

    public removeProperty<T>(key: K): void {
        this._properties.delete(key);
    }

    public getProperty<T>(key: K): Property<T> | undefined {
        return this._properties.get(key) as Property<T> | undefined;
    }

    public getPropertyValue<T>(key: K): T | undefined {
        // get property as PropertyValue<T>
        const property = this._properties.get(key) as Property<T> | undefined;
        return property?.value; 
    }

    public setProperty<T>(key: K, value: T): void {
        const property = this._properties.get(key) as Property<T> | undefined;
        if (property) {
            property.value = value;
        }
    }

    public notifyAll(force: boolean = false): void {
        this._properties.forEach(property => property.notify(force));
    }

    public clear() {
        this._properties.clear();
    }
}

// Use this one to simplify class hierarchy
export class PropertyContainerComponent<K> extends Component {
    private _propertyContainer: PropertyContainer<K> = new PropertyContainer<K>();

    public addProperty<T>(key: K, value: T): Property<T> {
        return this._propertyContainer.addProperty(key, value);
    }

    public removeProperty<T>(key: K): void {
        this._propertyContainer.removeProperty(key);
    }

    public getProperty<T>(key: K): Property<T> | undefined {
        return this._propertyContainer.getProperty(key);
    }

    public getPropertyValue<T>(key: K): T | undefined {
        return this._propertyContainer.getPropertyValue(key);
    }

    public setProperty<T>(key: K, value: T): void {
        this._propertyContainer.setProperty(key, value);
    }

    public notifyAll(force: boolean = false): void {
        this._propertyContainer.notifyAll(force);
    }
    
    public clear() {
        this._propertyContainer.clear();
    }
}