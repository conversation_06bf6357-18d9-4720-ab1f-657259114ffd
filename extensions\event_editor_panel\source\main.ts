// @ts-ignore
import packageJSON from '../package.json';
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    openPanel() {
        Editor.Panel.open(packageJSON.name);
    },

    /**
     * Open panel and select a specific event group
     */
    openEventGroup(eventGroupName: string) {
        // First open the panel
        Editor.Panel.open(packageJSON.name);
        // Send message to the panel to select the event group
        setTimeout(() => {
            Editor.Message.send(packageJSON.name, 'select-event-group', eventGroupName);
        }, 500);
    },

    /**
     * Select a specific event group in the already open panel
     */
    selectEventGroup(eventGroupName: string) {
        Editor.Message.send(packageJSON.name, 'select-event-group', eventGroupName);
    },
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() { }

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() { }
