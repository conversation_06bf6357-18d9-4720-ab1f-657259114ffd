import { _decorator, find, Vec3, Node, resources, JsonAsset, path } from "cc";
import { BulletController } from "./BulletController";
import { Emitter } from "./Emitter";
import { EventGroup, eEventGroupStatus } from "./EventGroup";
import { EventGroupData } from "../data/bullet/EventGroupData";
const { ccclass } = _decorator;
const { join } = path;

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class BulletSystem {
    public static readonly bulletParentPath: string = 'Canvas/GameUI/bullet_root';
    public static readonly emitterEventGroupPath: string = 'emitter/events/Emitter';
    public static readonly bulletEventGroupPath: string = 'emitter/events/Bullet';

    /**
     * All active bullets
     */
    public static allBullets: BulletController[] = [];

    /**
     * All active emitters
     */
    public static allEmitters: Emitter[] = [];

    /**
     * All active action groups
     */
    public static allEventGroups: EventGroup[] = [];

    // public static isEmitterEnabled: boolean = true;
    // public static isBulletEnabled: boolean = true;
    public static bulletParent: Node;

    /**
     * Main update loop
     */
    public static tick(dt: number) {
        this.tickEmitters(dt);
        this.tickBullets(dt);
        this.tickEventGroups(dt);
    }

    public static tickEmitters(dt:number) {
        for (const emitter of this.allEmitters) {
            emitter.tick(dt);
        }
    }

    public static tickBullets(dt:number) {
        for (const bullet of this.allBullets) {
            bullet.tick(dt);
        }
    }

    public static tickEventGroups(dt: number) {
        for (let i = this.allEventGroups.length - 1; i >= 0; i--) {
            const group = this.allEventGroups[i];
            group.tick(dt);
            if (group.status === eEventGroupStatus.Stopped) {
                this.allEventGroups.splice(i, 1);
            }
        }
    }

    public static onCreateEmitter(emitter:Emitter) {
        for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
                return;
            }
        }

        this.allEmitters.push(emitter);

        if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
                const foundNode = find(this.bulletParentPath);
                if (foundNode) {
                    this.bulletParent = foundNode;
                } else {
                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                    this.bulletParent = emitter.node;
                }
            }
        }
    }

    public static onDestroyEmitter(emitter:Emitter) {
        this.allEmitters = this.allEmitters.filter(e => e !== emitter);
    }

    public static onCreateBullet(emitter: Emitter, bullet: BulletController) {
        // 这个检查是否会比较冗余
        // for (let i = 0; i < this.allBullets.length; i++) {
        //     if (this.allBullets[i] === bullet) {
        //         return;
        //     }
        // }

        bullet.onCreate(emitter);
        this.allBullets.push(bullet);
        bullet.node.setParent(this.bulletParent, true);
    }

    public static onDestroyBullet(bullet: BulletController) {
        bullet.destroySelf();
        this.allBullets = this.allBullets.filter(b => b !== bullet);
    }

    public static destroyAllBullets() {
        for (const bullet of this.allBullets) {
            bullet.destroySelf();
        }
        this.allBullets = [];
    }

    public static loadEmitterEventGroup(name: string): EventGroupData {
        // the name is the json file name
        let finalPath = join(this.emitterEventGroupPath, name + '.json');
        let json = resources.load(finalPath, JsonAsset);
        let data = EventGroupData.fromJSON(json);
        return data;
    }

    public static loadBulletEventGroup(name: string): EventGroupData {
        // the name is the json file name
        let finalPath = join(this.bulletEventGroupPath, name + '.json');
        let json = resources.load(finalPath, JsonAsset);
        let data = EventGroupData.fromJSON(json);
        return data;
    }

    /**
     * Called when a new event group is created or turn active
     */
    public static onCreateEventGroup(eventGroup: EventGroup) {
        this.allEventGroups.push(eventGroup);
    }

    public static onDestroyEventGroup(eventGroup: EventGroup) {
        this.allEventGroups = this.allEventGroups.filter(g => g !== eventGroup);
    }
}