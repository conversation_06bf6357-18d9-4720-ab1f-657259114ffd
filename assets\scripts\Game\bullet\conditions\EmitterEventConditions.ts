import { IEventCondition } from "./IEventCondition";
import { EventGroupContext, Comparer } from "../EventGroup";
import { EventConditionData, eCompareOp, eConditionOp } from "../../data/bullet/EventGroupData";

export class EmitterConditionBase implements IEventCondition {
    readonly data: EventConditionData;

    constructor(data: EventConditionData) {
        this.data = data;
    }

    public evaluate(context: EventGroupContext): boolean {
        return true;
    }
}

// 发射器是否启用
export class EmitterCondition_Active extends EmitterConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // Custom evaluation logic for active condition
        return context.emitter.isActive.value;
    }
}

// 发射器初始延迟时间
export class EmitterCondition_InitialDelay extends EmitterConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.emitter.initialDelay.value, this.data.targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_Prewarm extends EmitterConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter.isPreWarm.value === (this.data.targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

// 发射器持续时间
export class EmitterCondition_Duration extends EmitterConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.emitter.emitDuration.value, this.data.targetValue, this.data.compareOp);
    }
}

// 发射器已运行时间
export class EmitterCondition_ElapsedTime extends EmitterConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.emitter.totalElapsedTime.value, this.data.targetValue, this.data.compareOp);
    }
}