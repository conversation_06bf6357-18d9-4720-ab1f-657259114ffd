"use strict";
/* eslint-disable vue/one-component-per-file */
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = require("fs");
const path_1 = require("path");
// @ts-ignore
const vue_1 = require("vue");
const EventGroupDataManager_1 = require("../../utils/EventGroupDataManager");
const panelDataMap = new WeakMap();
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('Event Editor Panel shown'); },
        hide() { console.log('Event Editor Panel hidden'); },
    },
    messages: {
        'select-event-group'(eventGroupName) {
            // Call the selectEventGroup method defined in methods section
            const panel = this;
            if (panel.methods && panel.methods.selectEventGroup) {
                panel.methods.selectEventGroup.call(panel, eventGroupName);
            }
        },
    },
    template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * Open and select a specific event group
         */
        selectEventGroup(eventGroupName) {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                // Send message to Vue app to select the event group
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.selectEventGroup) {
                    vueInstance.selectEventGroup(eventGroupName);
                }
            }
        },
        /**
         * Reload all event group data
         */
        reloadEventGroups() {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.reloadEventGroups) {
                    vueInstance.reloadEventGroups();
                }
            }
        }
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // Main Event Editor Component
            app.component('EventEditor', (0, vue_1.defineComponent)({
                setup() {
                    const manager = EventGroupDataManager_1.EventGroupDataManager.getInstance();
                    // Reactive state
                    const state = (0, vue_1.reactive)({
                        selectedCategory: EventGroupDataManager_1.EventGroupCategory.Emitter,
                        selectedEventGroup: null,
                        eventGroups: {
                            [EventGroupDataManager_1.EventGroupCategory.Emitter]: [],
                            [EventGroupDataManager_1.EventGroupCategory.Bullet]: []
                        },
                        searchQuery: '',
                        undoStack: [],
                        redoStack: []
                    });
                    // Computed properties
                    const filteredEventGroups = (0, vue_1.computed)(() => {
                        const groups = state.eventGroups[state.selectedCategory];
                        if (!state.searchQuery)
                            return groups;
                        return groups.filter((group) => group.name.toLowerCase().includes(state.searchQuery.toLowerCase()));
                    });
                    const categories = (0, vue_1.computed)(() => Object.values(EventGroupDataManager_1.EventGroupCategory));
                    // Computed properties for dirty state checking
                    const hasAnyDirtyInCategory = (0, vue_1.computed)(() => {
                        return state.eventGroups[state.selectedCategory].some(group => group.isDirty);
                    });
                    const isSelectedEventGroupDirty = (0, vue_1.computed)(() => {
                        var _a;
                        return ((_a = state.selectedEventGroup) === null || _a === void 0 ? void 0 : _a.isDirty) || false;
                    });
                    // Computed definitions based on selected category
                    const EventConditionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ConditionDefByCategory[state.selectedCategory];
                    });
                    const EventActionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ActionDefByCategory[state.selectedCategory];
                    });
                    // Methods
                    const loadEventGroups = () => {
                        for (const category of Object.values(EventGroupDataManager_1.EventGroupCategory)) {
                            state.eventGroups[category] = manager.loadEventGroupsByCategory(category);
                        }
                    };
                    const selectEventGroup = (eventGroup) => {
                        if (isSelectedEventGroupDirty.value) {
                            // TODO: Show confirmation dialog
                        }
                        // Save current state to undo stack
                        if (state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.redoStack = []; // Clear redo stack
                        }
                        state.selectedEventGroup = Object.assign({}, eventGroup);
                        // Clear dirty flag when selecting (since we're making a copy)
                        if (state.selectedEventGroup) {
                            state.selectedEventGroup.isDirty = false;
                        }
                    };
                    const selectEventGroupByName = (name) => {
                        const found = manager.findEventGroup(name);
                        if (found) {
                            state.selectedCategory = found.category;
                            selectEventGroup(found.data);
                        }
                    };
                    const saveEventGroups = () => {
                        if (!hasAnyDirtyInCategory.value)
                            return false;
                        const group = state.eventGroups[state.selectedCategory];
                        // Only save dirty event groups
                        group.filter(eventGroup => eventGroup.isDirty).forEach(eventGroup => {
                            manager.saveEventGroup(state.selectedCategory, eventGroup);
                        });
                        loadEventGroups(); // Reload to reflect changes
                        return true;
                    };
                    const createNewEventGroup = () => {
                        const newName = manager.generateUniqueName(state.selectedCategory);
                        const existedEventGroup = state.eventGroups[state.selectedCategory].find(eg => eg.name === newName);
                        if (existedEventGroup) {
                            // If the event group already exists, select it
                            // But this also means we get a duplicate name
                            selectEventGroup(existedEventGroup);
                        }
                        else {
                            const newEventGroup = manager.createNewEventGroup(state.selectedCategory);
                            state.eventGroups[state.selectedCategory].push(newEventGroup);
                            // The new event group is already marked as dirty in the manager
                            selectEventGroup(newEventGroup);
                        }
                    };
                    const duplicateEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return;
                        const duplicate = manager.duplicateEventGroup(state.selectedCategory, state.selectedEventGroup.name);
                        if (duplicate) {
                            state.eventGroups[state.selectedCategory].push(duplicate);
                            // The duplicate is already marked as dirty in the manager
                            selectEventGroup(duplicate);
                        }
                    };
                    const deleteEventGroup = (eventGroup) => {
                        var _a;
                        if (confirm(`Are you sure you want to delete "${eventGroup.name}"?`)) {
                            manager.deleteEventGroup(state.selectedCategory, eventGroup.name);
                            loadEventGroups();
                            if (((_a = state.selectedEventGroup) === null || _a === void 0 ? void 0 : _a.name) === eventGroup.name) {
                                state.selectedEventGroup = null;
                            }
                        }
                    };
                    const markDirty = () => {
                        if (state.selectedEventGroup) {
                            state.selectedEventGroup.isDirty = true;
                        }
                    };
                    const undo = () => {
                        if (state.undoStack.length > 0 && state.selectedEventGroup) {
                            state.redoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.undoStack.pop();
                            state.selectedEventGroup.isDirty = true;
                        }
                    };
                    const redo = () => {
                        if (state.redoStack.length > 0 && state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.redoStack.pop();
                            state.selectedEventGroup.isDirty = true;
                        }
                    };
                    const reloadEventGroups = () => {
                        loadEventGroups();
                        state.selectedEventGroup = null;
                    };
                    const addCondition = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.push({
                            op: 0, // And
                            type: 0,
                            compareOp: 0, // Equal
                            targetValue: 0
                        });
                        markDirty();
                    };
                    const removeCondition = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.splice(index, 1);
                        markDirty();
                    };
                    const addAction = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.push({
                            type: 0,
                            duration: 0,
                            targetValue: 0,
                            easing: 0 // Linear
                        });
                        markDirty();
                    };
                    const removeAction = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.splice(index, 1);
                        markDirty();
                    };
                    // Helper methods to get definition objects for type-aware inputs
                    const getConditionDef = (enumValue) => {
                        return EventConditionDef.value.find(def => def.enum === enumValue);
                    };
                    const getActionDef = (enumValue) => {
                        return EventActionDef.value.find(def => def.enum === enumValue);
                    };
                    // Lifecycle
                    (0, vue_1.onMounted)(() => {
                        loadEventGroups();
                    });
                    // Expose methods for external access
                    return {
                        state,
                        filteredEventGroups,
                        categories,
                        hasAnyDirtyInCategory,
                        isSelectedEventGroupDirty,
                        selectEventGroup,
                        selectEventGroupByName,
                        saveEventGroups,
                        createNewEventGroup,
                        duplicateEventGroup,
                        deleteEventGroup,
                        markDirty,
                        undo,
                        redo,
                        reloadEventGroups,
                        addCondition,
                        removeCondition,
                        addAction,
                        removeAction,
                        // Enums for dropdowns
                        ConditionOpEnum: EventGroupDataManager_1.ConditionOpEnum,
                        CompareOpEnum: EventGroupDataManager_1.CompareOpEnum,
                        EasingEnum: EventGroupDataManager_1.EasingEnum,
                        // New definition-based arrays
                        EventConditionDef,
                        EventActionDef,
                        // Helper methods for type-aware inputs
                        getConditionDef,
                        getActionDef
                    };
                },
                template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/event-editor.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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