System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, Comparer, eCompareOp, BulletConditionBase, BulletCondition_Duration, BulletCondition_ElapsedTime, BulletCondition_PosX, BulletCondition_PosY, BulletCondition_Damage, BulletCondition_Speed, BulletCondition_SpeedAngle, BulletCondition_Acceleration, BulletCondition_AccelerationAngle, BulletCondition_Scale, BulletCondition_ColorR, BulletCondition_ColorG, BulletCondition_ColorB, BulletCondition_FacingMoveDir, BulletCondition_Destructive, BulletCondition_DestructiveOnHit, _crd;

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "./IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfComparer(extras) {
    _reporterNs.report("Comparer", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventConditionData(extras) {
    _reporterNs.report("EventConditionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export({
    BulletConditionBase: void 0,
    BulletCondition_Duration: void 0,
    BulletCondition_ElapsedTime: void 0,
    BulletCondition_PosX: void 0,
    BulletCondition_PosY: void 0,
    BulletCondition_Damage: void 0,
    BulletCondition_Speed: void 0,
    BulletCondition_SpeedAngle: void 0,
    BulletCondition_Acceleration: void 0,
    BulletCondition_AccelerationAngle: void 0,
    BulletCondition_Scale: void 0,
    BulletCondition_ColorR: void 0,
    BulletCondition_ColorG: void 0,
    BulletCondition_ColorB: void 0,
    BulletCondition_FacingMoveDir: void 0,
    BulletCondition_Destructive: void 0,
    BulletCondition_DestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      Comparer = _unresolved_2.Comparer;
    }, function (_unresolved_3) {
      eCompareOp = _unresolved_3.eCompareOp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1bd34SWE81FuaZVCz1w6Esh", "BulletEventConditions", undefined);

      _export("BulletConditionBase", BulletConditionBase = class BulletConditionBase {
        constructor(data) {
          this.data = void 0;
          this.data = data;
        }

        evaluate(context) {
          return true;
        }

      });

      _export("BulletCondition_Duration", BulletCondition_Duration = class BulletCondition_Duration extends BulletConditionBase {
        evaluate(context) {
          // Custom evaluation logic for active condition
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.duration.value, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ElapsedTime", BulletCondition_ElapsedTime = class BulletCondition_ElapsedTime extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.elapsedTime, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_PosX", BulletCondition_PosX = class BulletCondition_PosX extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.position.x, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_PosY", BulletCondition_PosY = class BulletCondition_PosY extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.position.y, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Damage", BulletCondition_Damage = class BulletCondition_Damage extends BulletConditionBase {
        evaluate(context) {
          // return Comparer.compare(context.bullet!.damage.value, this.data.targetValue, this.data.compareOp);
          return false;
        }

      });

      _export("BulletCondition_Speed", BulletCondition_Speed = class BulletCondition_Speed extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.speed.value, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_SpeedAngle", BulletCondition_SpeedAngle = class BulletCondition_SpeedAngle extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.speedAngle.value, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Acceleration", BulletCondition_Acceleration = class BulletCondition_Acceleration extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.acceleration.value, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_AccelerationAngle", BulletCondition_AccelerationAngle = class BulletCondition_AccelerationAngle extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.accelerationAngle.value, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_Scale", BulletCondition_Scale = class BulletCondition_Scale extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.node.scale.x, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorR", BulletCondition_ColorR = class BulletCondition_ColorR extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.bulletSprite.color.r, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorG", BulletCondition_ColorG = class BulletCondition_ColorG extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.bulletSprite.color.g, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_ColorB", BulletCondition_ColorB = class BulletCondition_ColorB extends BulletConditionBase {
        evaluate(context) {
          return (_crd && Comparer === void 0 ? (_reportPossibleCrUseOfComparer({
            error: Error()
          }), Comparer) : Comparer).compare(context.bullet.bulletSprite.color.b, this.data.targetValue, this.data.compareOp);
        }

      });

      _export("BulletCondition_FacingMoveDir", BulletCondition_FacingMoveDir = class BulletCondition_FacingMoveDir extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return context.bullet.isFacingMoveDir.value === (this.data.targetValue === 1) ? true : false;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return context.bullet.isFacingMoveDir.value !== (this.data.targetValue === 1) ? true : false;

            default:
              return false;
          }
        }

      });

      _export("BulletCondition_Destructive", BulletCondition_Destructive = class BulletCondition_Destructive extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            // case eCompareOp.Equal:
            //     return context.bullet!.isDestructive.value === (this.data.targetValue === 1) ? true : false;
            // case eCompareOp.NotEqual:
            //     return context.bullet!.isDestructive.value !== (this.data.targetValue === 1) ? true : false;
            default:
              return false;
          }
        }

      });

      _export("BulletCondition_DestructiveOnHit", BulletCondition_DestructiveOnHit = class BulletCondition_DestructiveOnHit extends BulletConditionBase {
        evaluate(context) {
          switch (this.data.compareOp) {
            // case eCompareOp.Equal:
            //     return context.bullet!.isPreWarm.value === (this.data.targetValue === 1) ? true : false;
            // case eCompareOp.NotEqual:
            //     return context.bullet!.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;
            default:
              return false;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=314a639eb5ccd582bf67a6fd9668a4463b930929.js.map