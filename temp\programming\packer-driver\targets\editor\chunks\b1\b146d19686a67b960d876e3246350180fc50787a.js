System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _crd, ccclass, property, eEmitterActionType, eBulletActionType;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2b8ffdjcPFL5LEEU6ckSLaw", "EventActionType", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * ActionType对应要修改的属性
       * 以下是发射器的行为
       */

      _export("eEmitterActionType", eEmitterActionType = /*#__PURE__*/function (eEmitterActionType) {
        eEmitterActionType[eEmitterActionType["Emitter_Active"] = 1] = "Emitter_Active";
        eEmitterActionType[eEmitterActionType["Emitter_InitialDelay"] = 2] = "Emitter_InitialDelay";
        eEmitterActionType[eEmitterActionType["Emitter_Prewarm"] = 3] = "Emitter_Prewarm";
        eEmitterActionType[eEmitterActionType["Emitter_PrewarmDuration"] = 4] = "Emitter_PrewarmDuration";
        eEmitterActionType[eEmitterActionType["Emitter_Duration"] = 5] = "Emitter_Duration";
        eEmitterActionType[eEmitterActionType["Emitter_ElapsedTime"] = 6] = "Emitter_ElapsedTime";
        eEmitterActionType[eEmitterActionType["Emitter_Loop"] = 7] = "Emitter_Loop";
        eEmitterActionType[eEmitterActionType["Emitter_LoopInterval"] = 8] = "Emitter_LoopInterval";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitCount"] = 9] = "Emitter_PerEmitCount";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitInterval"] = 10] = "Emitter_PerEmitInterval";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitOffsetX"] = 11] = "Emitter_PerEmitOffsetX";
        eEmitterActionType[eEmitterActionType["Emitter_Angle"] = 12] = "Emitter_Angle";
        eEmitterActionType[eEmitterActionType["Emitter_Count"] = 13] = "Emitter_Count";
        eEmitterActionType[eEmitterActionType["Bullet_Duration"] = 14] = "Bullet_Duration";
        eEmitterActionType[eEmitterActionType["Bullet_ElapsedTime"] = 15] = "Bullet_ElapsedTime";
        eEmitterActionType[eEmitterActionType["Bullet_PosX"] = 16] = "Bullet_PosX";
        eEmitterActionType[eEmitterActionType["Bullet_PosY"] = 17] = "Bullet_PosY";
        eEmitterActionType[eEmitterActionType["Bullet_Damage"] = 18] = "Bullet_Damage";
        eEmitterActionType[eEmitterActionType["Bullet_Speed"] = 19] = "Bullet_Speed";
        eEmitterActionType[eEmitterActionType["Bullet_SpeedAngle"] = 20] = "Bullet_SpeedAngle";
        eEmitterActionType[eEmitterActionType["Bullet_Acceleration"] = 21] = "Bullet_Acceleration";
        eEmitterActionType[eEmitterActionType["Bullet_AccelerationAngle"] = 22] = "Bullet_AccelerationAngle";
        eEmitterActionType[eEmitterActionType["Bullet_Scale"] = 23] = "Bullet_Scale";
        eEmitterActionType[eEmitterActionType["Bullet_ColorR"] = 24] = "Bullet_ColorR";
        eEmitterActionType[eEmitterActionType["Bullet_ColorG"] = 25] = "Bullet_ColorG";
        eEmitterActionType[eEmitterActionType["Bullet_ColorB"] = 26] = "Bullet_ColorB";
        eEmitterActionType[eEmitterActionType["Bullet_ColorA"] = 27] = "Bullet_ColorA";
        eEmitterActionType[eEmitterActionType["Bullet_FaceMovingDir"] = 28] = "Bullet_FaceMovingDir";
        eEmitterActionType[eEmitterActionType["Bullet_TrackingTarget"] = 29] = "Bullet_TrackingTarget";
        eEmitterActionType[eEmitterActionType["Bullet_Destructive"] = 30] = "Bullet_Destructive";
        eEmitterActionType[eEmitterActionType["Bullet_DestructiveOnHit"] = 31] = "Bullet_DestructiveOnHit";
        eEmitterActionType[eEmitterActionType["Unit_Life"] = 32] = "Unit_Life";
        eEmitterActionType[eEmitterActionType["Unit_LifePercent"] = 33] = "Unit_LifePercent";
        eEmitterActionType[eEmitterActionType["Unit_PosX"] = 34] = "Unit_PosX";
        eEmitterActionType[eEmitterActionType["Unit_PosY"] = 35] = "Unit_PosY";
        eEmitterActionType[eEmitterActionType["Unit_Speed"] = 36] = "Unit_Speed";
        eEmitterActionType[eEmitterActionType["Unit_SpeedAngle"] = 37] = "Unit_SpeedAngle";
        eEmitterActionType[eEmitterActionType["Unit_Acceleration"] = 38] = "Unit_Acceleration";
        eEmitterActionType[eEmitterActionType["Unit_AccelerationAngle"] = 39] = "Unit_AccelerationAngle";
        return eEmitterActionType;
      }({}));
      /**
       * ActionType对应要修改的属性
       * 以下是子弹的行为
       */


      _export("eBulletActionType", eBulletActionType = /*#__PURE__*/function (eBulletActionType) {
        eBulletActionType[eBulletActionType["Bullet_Duration"] = 100] = "Bullet_Duration";
        eBulletActionType[eBulletActionType["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
        eBulletActionType[eBulletActionType["Bullet_PosX"] = 102] = "Bullet_PosX";
        eBulletActionType[eBulletActionType["Bullet_PosY"] = 103] = "Bullet_PosY";
        eBulletActionType[eBulletActionType["Bullet_Damage"] = 104] = "Bullet_Damage";
        eBulletActionType[eBulletActionType["Bullet_Speed"] = 105] = "Bullet_Speed";
        eBulletActionType[eBulletActionType["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
        eBulletActionType[eBulletActionType["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
        eBulletActionType[eBulletActionType["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
        eBulletActionType[eBulletActionType["Bullet_Scale"] = 109] = "Bullet_Scale";
        eBulletActionType[eBulletActionType["Bullet_ColorR"] = 110] = "Bullet_ColorR";
        eBulletActionType[eBulletActionType["Bullet_ColorG"] = 111] = "Bullet_ColorG";
        eBulletActionType[eBulletActionType["Bullet_ColorB"] = 112] = "Bullet_ColorB";
        eBulletActionType[eBulletActionType["Bullet_ColorA"] = 113] = "Bullet_ColorA";
        eBulletActionType[eBulletActionType["Bullet_FaceMovingDir"] = 114] = "Bullet_FaceMovingDir";
        eBulletActionType[eBulletActionType["Bullet_TrackingTarget"] = 115] = "Bullet_TrackingTarget";
        eBulletActionType[eBulletActionType["Bullet_Destructive"] = 116] = "Bullet_Destructive";
        eBulletActionType[eBulletActionType["Bullet_DestructiveOnHit"] = 117] = "Bullet_DestructiveOnHit";
        return eBulletActionType;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b146d19686a67b960d876e3246350180fc50787a.js.map