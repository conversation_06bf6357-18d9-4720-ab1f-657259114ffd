System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _crd, ccclass, property, EmitterData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "dcf25LTxTFNQaXpir0cm4bk", "EmitterData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 发射器数据
       * 所有时间相关的，单位都是秒(s)
       */

      _export("EmitterData", EmitterData = (_dec = ccclass("EmitterData"), _dec2 = property({
        displayName: '是否仅在屏幕内发射'
      }), _dec3 = property({
        displayName: '是否预热',
        tooltip: '预热xxx'
      }), _dec4 = property({
        displayName: '是否循环'
      }), _dec5 = property({
        displayName: '初始延迟'
      }), _dec6 = property({
        displayName: '预热持续时长'
      }), _dec7 = property({
        type: Prefab,
        displayName: '预热特效'
      }), _dec8 = property({
        displayName: '发射器持续时间'
      }), _dec9 = property({
        displayName: '发射间隔'
      }), _dec10 = property({
        displayName: '发射速度'
      }), _dec11 = property({
        displayName: '循环间隔'
      }), _dec12 = property({
        displayName: '单次发射数量'
      }), _dec13 = property({
        displayName: '单次发射多个子弹时的间隔'
      }), _dec14 = property({
        displayName: '单次发射多个子弹时的x偏移'
      }), _dec15 = property({
        displayName: '发射角度'
      }), _dec16 = property({
        displayName: '发射条数'
      }), _dec17 = property({
        displayName: '发射范围'
      }), _dec18 = property({
        displayName: '发射半径'
      }), _dec19 = property({
        type: Prefab,
        displayName: '发射特效'
      }), _dec20 = property({
        type: String,
        displayName: '事件组'
      }), _dec(_class = (_class2 = class EmitterData {
        constructor() {
          // @property({displayName: '发射器名称'})
          // name : string = '';                // uid 
          _initializerDefineProperty(this, "isOnlyInScreen", _descriptor, this);

          // 仅在屏幕内才发射
          _initializerDefineProperty(this, "isPreWarm", _descriptor2, this);

          // 是否预热
          _initializerDefineProperty(this, "isLoop", _descriptor3, this);

          // 是否循环
          _initializerDefineProperty(this, "initialDelay", _descriptor4, this);

          // 初始延迟
          _initializerDefineProperty(this, "preWarmDuration", _descriptor5, this);

          // 预热持续时长
          _initializerDefineProperty(this, "preWarmEffect", _descriptor6, this);

          // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)
          // @property({displayName: '预热音效'})
          // preWarmSound : string;             // 预热音效
          _initializerDefineProperty(this, "emitDuration", _descriptor7, this);

          // 发射器持续时间
          _initializerDefineProperty(this, "emitInterval", _descriptor8, this);

          // 发射间隔
          _initializerDefineProperty(this, "emitPower", _descriptor9, this);

          // 用来修改子弹初始速度的乘数(备用)
          _initializerDefineProperty(this, "loopInterval", _descriptor10, this);

          // 循环间隔
          _initializerDefineProperty(this, "perEmitCount", _descriptor11, this);

          // 单次发射数量
          _initializerDefineProperty(this, "perEmitInterval", _descriptor12, this);

          // 单次发射多个子弹时的间隔
          _initializerDefineProperty(this, "perEmitOffsetX", _descriptor13, this);

          // 单次发射多个子弹时的x偏移
          _initializerDefineProperty(this, "angle", _descriptor14, this);

          // 发射角度: -90朝下
          _initializerDefineProperty(this, "count", _descriptor15, this);

          // 发射条数(弹道数量)
          _initializerDefineProperty(this, "arc", _descriptor16, this);

          // 发射范围(弧度范围)
          _initializerDefineProperty(this, "radius", _descriptor17, this);

          // 发射半径
          // TODO: 参数随机
          _initializerDefineProperty(this, "emitEffect", _descriptor18, this);

          // 发射特效(多个的话建议做到prefab上?) 包含音效?
          _initializerDefineProperty(this, "eventGroupData", _descriptor19, this);
        }

        static fromJSON(json) {
          const data = new EmitterData();

          if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map(id => id);
          }

          return data;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isOnlyInScreen", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isPreWarm", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isLoop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "initialDelay", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "preWarmDuration", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "preWarmEffect", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "emitDuration", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "emitInterval", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "emitPower", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "loopInterval", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "perEmitCount", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "perEmitInterval", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.0;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "perEmitOffsetX", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.0;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "angle", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return -90;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "count", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class2.prototype, "arc", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 60;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class2.prototype, "radius", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class2.prototype, "emitEffect", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor19 = _applyDecoratedDescriptor(_class2.prototype, "eventGroupData", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c143b4c6324cab462d3902c82f81f86c3061f66a.js.map