"use strict";
/* eslint-disable vue/one-component-per-file */
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = require("fs");
const path_1 = require("path");
// @ts-ignore
const vue_1 = require("vue");
const EventGroupDataManager_1 = require("../../utils/EventGroupDataManager");
const panelDataMap = new WeakMap();
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('Event Editor Panel shown'); },
        hide() { console.log('Event Editor Panel hidden'); },
    },
    messages: {
        'select-event-group'(eventGroupName) {
            // Call the selectEventGroup method defined in methods section
            const panel = this;
            if (panel.methods && panel.methods.selectEventGroup) {
                panel.methods.selectEventGroup.call(panel, eventGroupName);
            }
        },
    },
    template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * Open and select a specific event group
         */
        selectEventGroup(eventGroupName) {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                // Send message to Vue app to select the event group
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.selectEventGroup) {
                    vueInstance.selectEventGroup(eventGroupName);
                }
            }
        },
        /**
         * Reload all event group data
         */
        reloadEventGroups() {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.reloadEventGroups) {
                    vueInstance.reloadEventGroups();
                }
            }
        }
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // Main Event Editor Component
            app.component('EventEditor', (0, vue_1.defineComponent)({
                setup() {
                    const manager = EventGroupDataManager_1.EventGroupDataManager.getInstance();
                    // Reactive state
                    const state = (0, vue_1.reactive)({
                        selectedCategory: EventGroupDataManager_1.EventGroupCategory.Emitter,
                        selectedEventGroup: null,
                        eventGroups: {
                            [EventGroupDataManager_1.EventGroupCategory.Emitter]: [],
                            [EventGroupDataManager_1.EventGroupCategory.Bullet]: []
                        },
                        searchQuery: '',
                        isDirty: false,
                        undoStack: [],
                        redoStack: []
                    });
                    // Computed properties
                    const filteredEventGroups = (0, vue_1.computed)(() => {
                        const groups = state.eventGroups[state.selectedCategory];
                        if (!state.searchQuery)
                            return groups;
                        return groups.filter((group) => group.name.toLowerCase().includes(state.searchQuery.toLowerCase()));
                    });
                    const categories = (0, vue_1.computed)(() => Object.values(EventGroupDataManager_1.EventGroupCategory));
                    // Computed definitions based on selected category
                    const EventConditionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ConditionDefByCategory[state.selectedCategory];
                    });
                    const EventActionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ActionDefByCategory[state.selectedCategory];
                    });
                    // Computed enums based on selected category (for backward compatibility)
                    const EventConditionTypeEnum = (0, vue_1.computed)(() => {
                        return state.selectedCategory === EventGroupDataManager_1.EventGroupCategory.Emitter
                            ? EventGroupDataManager_1.EmitterConditionTypeEnum
                            : EventGroupDataManager_1.BulletConditionTypeEnum;
                    });
                    const EventActionTypeEnum = (0, vue_1.computed)(() => {
                        return state.selectedCategory === EventGroupDataManager_1.EventGroupCategory.Emitter
                            ? EventGroupDataManager_1.EmitterActionTypeEnum
                            : EventGroupDataManager_1.BulletActionTypeEnum;
                    });
                    // Methods
                    const loadEventGroups = () => {
                        for (const category of Object.values(EventGroupDataManager_1.EventGroupCategory)) {
                            state.eventGroups[category] = manager.loadEventGroupsByCategory(category);
                        }
                    };
                    const selectEventGroup = (eventGroup) => {
                        if (state.isDirty) {
                            // TODO: Show confirmation dialog
                        }
                        // Save current state to undo stack
                        if (state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.redoStack = []; // Clear redo stack
                        }
                        state.selectedEventGroup = Object.assign({}, eventGroup);
                        state.isDirty = false;
                    };
                    const selectEventGroupByName = (name) => {
                        const found = manager.findEventGroup(name);
                        if (found) {
                            state.selectedCategory = found.category;
                            selectEventGroup(found.data);
                        }
                    };
                    const saveCurrentEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return false;
                        const success = manager.saveEventGroup(state.selectedCategory, state.selectedEventGroup);
                        if (success) {
                            state.isDirty = false;
                            loadEventGroups(); // Reload to reflect changes
                        }
                        return success;
                    };
                    const createNewEventGroup = () => {
                        const newName = manager.generateUniqueName(state.selectedCategory);
                        const existedEventGroup = state.eventGroups[state.selectedCategory].find(eg => eg.name === newName);
                        if (existedEventGroup) {
                            // If the event group already exists, select it
                            // But this also means we get a duplicate name
                            selectEventGroup(existedEventGroup);
                        }
                        else {
                            const newEventGroup = manager.createNewEventGroup(state.selectedCategory);
                            state.eventGroups[state.selectedCategory].push(newEventGroup);
                            selectEventGroup(newEventGroup);
                        }
                    };
                    const duplicateEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return;
                        const duplicate = manager.duplicateEventGroup(state.selectedCategory, state.selectedEventGroup.name);
                        if (duplicate) {
                            state.eventGroups[state.selectedCategory].push(duplicate);
                            selectEventGroup(duplicate);
                        }
                    };
                    const deleteEventGroup = (eventGroup) => {
                        var _a;
                        if (confirm(`Are you sure you want to delete "${eventGroup.name}"?`)) {
                            manager.deleteEventGroup(state.selectedCategory, eventGroup.name);
                            loadEventGroups();
                            if (((_a = state.selectedEventGroup) === null || _a === void 0 ? void 0 : _a.name) === eventGroup.name) {
                                state.selectedEventGroup = null;
                                state.isDirty = false;
                            }
                        }
                    };
                    const markDirty = () => {
                        state.isDirty = true;
                    };
                    const undo = () => {
                        if (state.undoStack.length > 0 && state.selectedEventGroup) {
                            state.redoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.undoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const redo = () => {
                        if (state.redoStack.length > 0 && state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.redoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const reloadEventGroups = () => {
                        loadEventGroups();
                        state.selectedEventGroup = null;
                        state.isDirty = false;
                    };
                    const addCondition = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.push({
                            op: 0, // And
                            type: 0,
                            compareOp: 0, // Equal
                            targetValue: 0
                        });
                        markDirty();
                    };
                    const removeCondition = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.splice(index, 1);
                        markDirty();
                    };
                    const addAction = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.push({
                            type: 0,
                            duration: 0,
                            targetValue: 0,
                            easing: 0 // Linear
                        });
                        markDirty();
                    };
                    const removeAction = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.splice(index, 1);
                        markDirty();
                    };
                    // Helper methods to get definition objects for type-aware inputs
                    const getConditionDef = (enumValue) => {
                        return EventConditionDef.value.find(def => def.enum === enumValue);
                    };
                    const getActionDef = (enumValue) => {
                        return EventActionDef.value.find(def => def.enum === enumValue);
                    };
                    // Lifecycle
                    (0, vue_1.onMounted)(() => {
                        loadEventGroups();
                    });
                    // Expose methods for external access
                    return {
                        state,
                        filteredEventGroups,
                        categories,
                        selectEventGroup,
                        selectEventGroupByName,
                        saveCurrentEventGroup,
                        createNewEventGroup,
                        duplicateEventGroup,
                        deleteEventGroup,
                        markDirty,
                        undo,
                        redo,
                        reloadEventGroups,
                        addCondition,
                        removeCondition,
                        addAction,
                        removeAction,
                        // Enums for dropdowns
                        ConditionOpEnum: EventGroupDataManager_1.ConditionOpEnum,
                        CompareOpEnum: EventGroupDataManager_1.CompareOpEnum,
                        EventConditionTypeEnum,
                        EventActionTypeEnum,
                        EasingEnum: EventGroupDataManager_1.EasingEnum,
                        // New definition-based arrays
                        EventConditionDef,
                        EventActionDef,
                        // Helper methods for type-aware inputs
                        getConditionDef,
                        getActionDef
                    };
                },
                template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/event-editor.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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