"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventGroupDataManager = exports.ActionTypeByCategory = exports.ConditionTypeByCategory = exports.EventGroupCategory = exports.EasingEnum = exports.BulletActionTypeEnum = exports.EmitterActionTypeEnum = exports.BulletConditionTypeEnum = exports.EmitterConditionTypeEnum = exports.CompareOpEnum = exports.ConditionOpEnum = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
// Enum definitions for UI display
exports.ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};
exports.CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};
// These should match your actual game enums defined in EventConditionType.ts
exports.EmitterConditionTypeEnum = {
    0: 'Level_Duration',
    1: 'Level_Distance',
    2: 'Level_InfLevel',
    3: 'Level_ChallengeLevel',
    4: 'Player_ActLevel',
    5: 'Player_PosX',
    6: 'Player_PosY',
    7: 'Player_LifePercent',
    8: 'Player_GainBuff',
    9: 'Unit_Life',
    10: 'Unit_LifePercent',
    11: 'Unit_Duration',
    12: 'Unit_PosX',
    13: 'Unit_PosY',
    14: 'Unit_Speed',
    15: 'Unit_SpeedAngle',
    16: 'Unit_Acceleration',
    17: 'Unit_AccelerationAngle',
    18: 'Unit_DistanceToPlayer',
    19: 'Unit_AngleToPlayer',
    20: 'Emitter_Active',
    21: 'Emitter_InitialDelay',
    22: 'Emitter_Prewarm',
    23: 'Emitter_PrewarmDuration',
    24: 'Emitter_Duration',
    25: 'Emitter_ElapsedTime',
    26: 'Emitter_Loop',
    27: 'Emitter_LoopInterval',
    28: 'Emitter_EmitInterval',
    29: 'Emitter_EmitCount',
    30: 'Emitter_EmitOffsetX',
    31: 'Emitter_Angle',
    32: 'Emitter_Count',
    33: 'Bullet_Sprite',
    34: 'Bullet_Scale',
    35: 'Bullet_ColorR',
    36: 'Bullet_ColorG',
    37: 'Bullet_ColorB',
    38: 'Bullet_Duration',
    39: 'Bullet_ElapsedTime',
    40: 'Bullet_Speed',
    41: 'Bullet_Acceleration',
    42: 'Bullet_AccelerationAngle',
    43: 'Bullet_FacingMoveDir',
    44: 'Bullet_Destructive',
    45: 'Bullet_DestructiveOnHit',
};
exports.BulletConditionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_FacingMoveDir',
    14: 'Bullet_Destructive',
    15: 'Bullet_DestructiveOnHit',
};
exports.EmitterActionTypeEnum = {
    0: 'Emitter_Active',
    1: 'Emitter_InitialDelay',
    2: 'Emitter_Prewarm',
    3: 'Emitter_PrewarmDuration',
    4: 'Emitter_Duration',
    5: 'Emitter_ElapsedTime',
    6: 'Emitter_Loop',
    7: 'Emitter_LoopInterval',
    8: 'Emitter_PerEmitInterval',
    9: 'Emitter_PerEmitCount',
    10: 'Emitter_PerEmitOffsetX',
    11: 'Emitter_Angle',
    12: 'Emitter_Count',
    13: 'Bullet_Duration',
    14: 'Bullet_ElapsedTime',
    15: 'Bullet_PosX',
    16: 'Bullet_PosY',
    17: 'Bullet_Damage',
    18: 'Bullet_Speed',
    19: 'Bullet_SpeedAngle',
    20: 'Bullet_Acceleration',
    21: 'Bullet_AccelerationAngle',
    22: 'Bullet_Scale',
    23: 'Bullet_ColorR',
    24: 'Bullet_ColorG',
    25: 'Bullet_ColorB',
    26: 'Bullet_ColorA',
    27: 'Bullet_FaceMovingDir',
    28: 'Bullet_TrackingTarget',
    29: 'Bullet_Destructive',
    30: 'Bullet_DestructiveOnHit',
    31: 'Unit_Life',
    32: 'Unit_LifePercent',
    33: 'Unit_PosX',
    34: 'Unit_PosY',
    35: 'Unit_Speed',
    36: 'Unit_SpeedAngle',
    37: 'Unit_Acceleration',
    38: 'Unit_AccelerationAngle',
};
exports.BulletActionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_ColorA',
    14: 'Bullet_FaceMovingDir',
    15: 'Bullet_TrackingTarget',
    16: 'Bullet_Destructive',
    17: 'Bullet_DestructiveOnHit',
};
exports.EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};
var EventGroupCategory;
(function (EventGroupCategory) {
    EventGroupCategory["Emitter"] = "Emitter";
    EventGroupCategory["Bullet"] = "Bullet";
})(EventGroupCategory || (exports.EventGroupCategory = EventGroupCategory = {}));
exports.ConditionTypeByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterConditionTypeEnum,
    [EventGroupCategory.Bullet]: exports.BulletConditionTypeEnum,
};
exports.ActionTypeByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterActionTypeEnum,
    [EventGroupCategory.Bullet]: exports.BulletActionTypeEnum,
};
class EventGroupDataManager {
    constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = (0, path_1.join)(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }
    static getInstance() {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }
    /**
     * Get the full path for a category folder
     */
    getCategoryPath(category) {
        return (0, path_1.join)(this.eventsBasePath, category);
    }
    /**
     * Get the full file path for an EventGroupData
     */
    getFilePath(category, name) {
        return (0, path_1.join)(this.getCategoryPath(category), `${name}.json`);
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(dirPath) {
        if (!(0, fs_1.existsSync)(dirPath)) {
            (0, fs_1.mkdirSync)(dirPath, { recursive: true });
        }
    }
    /**
     * Load all EventGroupData files from a category
     */
    loadEventGroupsByCategory(category) {
        const categoryPath = this.getCategoryPath(category);
        if (!(0, fs_1.existsSync)(categoryPath)) {
            return [];
        }
        const files = (0, fs_1.readdirSync)(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups = [];
        for (const file of files) {
            try {
                const filePath = (0, path_1.join)(categoryPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
                const data = JSON.parse(content);
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            }
            catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }
        return eventGroups;
    }
    /**
     * Load a specific EventGroupData by name and category
     */
    loadEventGroup(category, name) {
        const filePath = this.getFilePath(category, name);
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        try {
            const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
            const data = JSON.parse(content);
            data.name = name; // Ensure name is correct
            return data;
        }
        catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }
    /**
     * Save an EventGroupData to file
     */
    saveEventGroup(category, eventGroup) {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
            return true;
        }
        catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }
    /**
     * Delete an EventGroupData file
     */
    deleteEventGroup(category, name) {
        try {
            const filePath = this.getFilePath(category, name);
            if ((0, fs_1.existsSync)(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }
    /**
     * Check if an EventGroupData file exists
     */
    eventGroupExists(category, name) {
        const filePath = this.getFilePath(category, name);
        return (0, fs_1.existsSync)(filePath);
    }
    /**
     * Find an EventGroupData by name across all categories
     */
    findEventGroup(name) {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }
    /**
     * Generate a unique name for a new EventGroupData
     */
    generateUniqueName(category, baseName = 'EventGroup') {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        return name;
    }
    /**
     * Create a new EventGroupData with default values
     */
    createNewEventGroup(category, name) {
        const finalName = name || this.generateUniqueName(category);
        var eventGroup = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };
        return eventGroup;
    }
    /**
     * Duplicate an existing EventGroupData
     */
    duplicateEventGroup(category, originalName, newName) {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }
        const finalName = newName || this.generateUniqueName(category, originalName);
        // Deep copy the original data
        const duplicate = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => (Object.assign({}, condition))),
            actions: original.actions.map(action => (Object.assign({}, action)))
        };
        return duplicate;
    }
    /**
     * Get all EventGroupData names from all categories
     */
    getAllEventGroupNames() {
        const result = {};
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        return result;
    }
}
exports.EventGroupDataManager = EventGroupDataManager;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRXZlbnRHcm91cERhdGFNYW5hZ2VyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc291cmNlL3V0aWxzL0V2ZW50R3JvdXBEYXRhTWFuYWdlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSwyQkFBcUY7QUFDckYsK0JBQXFDO0FBRXJDLGtDQUFrQztBQUNyQixRQUFBLGVBQWUsR0FBRztJQUMzQixDQUFDLEVBQUUsS0FBSztJQUNSLENBQUMsRUFBRSxJQUFJO0NBQ1YsQ0FBQztBQUVXLFFBQUEsYUFBYSxHQUFHO0lBQ3pCLENBQUMsRUFBRSxJQUFJO0lBQ1AsQ0FBQyxFQUFFLElBQUk7SUFDUCxDQUFDLEVBQUUsR0FBRztJQUNOLENBQUMsRUFBRSxHQUFHO0lBQ04sQ0FBQyxFQUFFLElBQUk7SUFDUCxDQUFDLEVBQUUsSUFBSTtDQUNWLENBQUM7QUFFRiw2RUFBNkU7QUFDaEUsUUFBQSx3QkFBd0IsR0FBRztJQUNwQyxDQUFDLEVBQUUsZ0JBQWdCO0lBQ25CLENBQUMsRUFBRSxnQkFBZ0I7SUFDbkIsQ0FBQyxFQUFFLGdCQUFnQjtJQUNuQixDQUFDLEVBQUUsc0JBQXNCO0lBQ3pCLENBQUMsRUFBRSxpQkFBaUI7SUFDcEIsQ0FBQyxFQUFFLGFBQWE7SUFDaEIsQ0FBQyxFQUFFLGFBQWE7SUFDaEIsQ0FBQyxFQUFFLG9CQUFvQjtJQUN2QixDQUFDLEVBQUUsaUJBQWlCO0lBQ3BCLENBQUMsRUFBRSxXQUFXO0lBQ2QsRUFBRSxFQUFFLGtCQUFrQjtJQUN0QixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsV0FBVztJQUNmLEVBQUUsRUFBRSxXQUFXO0lBQ2YsRUFBRSxFQUFFLFlBQVk7SUFDaEIsRUFBRSxFQUFFLGlCQUFpQjtJQUNyQixFQUFFLEVBQUUsbUJBQW1CO0lBQ3ZCLEVBQUUsRUFBRSx3QkFBd0I7SUFDNUIsRUFBRSxFQUFFLHVCQUF1QjtJQUMzQixFQUFFLEVBQUUsb0JBQW9CO0lBQ3hCLEVBQUUsRUFBRSxnQkFBZ0I7SUFDcEIsRUFBRSxFQUFFLHNCQUFzQjtJQUMxQixFQUFFLEVBQUUsaUJBQWlCO0lBQ3JCLEVBQUUsRUFBRSx5QkFBeUI7SUFDN0IsRUFBRSxFQUFFLGtCQUFrQjtJQUN0QixFQUFFLEVBQUUscUJBQXFCO0lBQ3pCLEVBQUUsRUFBRSxjQUFjO0lBQ2xCLEVBQUUsRUFBRSxzQkFBc0I7SUFDMUIsRUFBRSxFQUFFLHNCQUFzQjtJQUMxQixFQUFFLEVBQUUsbUJBQW1CO0lBQ3ZCLEVBQUUsRUFBRSxxQkFBcUI7SUFDekIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGNBQWM7SUFDbEIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGlCQUFpQjtJQUNyQixFQUFFLEVBQUUsb0JBQW9CO0lBQ3hCLEVBQUUsRUFBRSxjQUFjO0lBQ2xCLEVBQUUsRUFBRSxxQkFBcUI7SUFDekIsRUFBRSxFQUFFLDBCQUEwQjtJQUM5QixFQUFFLEVBQUUsc0JBQXNCO0lBQzFCLEVBQUUsRUFBRSxvQkFBb0I7SUFDeEIsRUFBRSxFQUFFLHlCQUF5QjtDQUNoQyxDQUFDO0FBRVcsUUFBQSx1QkFBdUIsR0FBRztJQUNuQyxDQUFDLEVBQUUsaUJBQWlCO0lBQ3BCLENBQUMsRUFBRSxvQkFBb0I7SUFDdkIsQ0FBQyxFQUFFLGFBQWE7SUFDaEIsQ0FBQyxFQUFFLGFBQWE7SUFDaEIsQ0FBQyxFQUFFLGVBQWU7SUFDbEIsQ0FBQyxFQUFFLGNBQWM7SUFDakIsQ0FBQyxFQUFFLG1CQUFtQjtJQUN0QixDQUFDLEVBQUUscUJBQXFCO0lBQ3hCLENBQUMsRUFBRSwwQkFBMEI7SUFDN0IsQ0FBQyxFQUFFLGNBQWM7SUFDakIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLHNCQUFzQjtJQUMxQixFQUFFLEVBQUUsb0JBQW9CO0lBQ3hCLEVBQUUsRUFBRSx5QkFBeUI7Q0FDaEMsQ0FBQTtBQUVZLFFBQUEscUJBQXFCLEdBQUc7SUFDakMsQ0FBQyxFQUFFLGdCQUFnQjtJQUNuQixDQUFDLEVBQUUsc0JBQXNCO0lBQ3pCLENBQUMsRUFBRSxpQkFBaUI7SUFDcEIsQ0FBQyxFQUFFLHlCQUF5QjtJQUM1QixDQUFDLEVBQUUsa0JBQWtCO0lBQ3JCLENBQUMsRUFBRSxxQkFBcUI7SUFDeEIsQ0FBQyxFQUFFLGNBQWM7SUFDakIsQ0FBQyxFQUFFLHNCQUFzQjtJQUN6QixDQUFDLEVBQUUseUJBQXlCO0lBQzVCLENBQUMsRUFBRSxzQkFBc0I7SUFDekIsRUFBRSxFQUFFLHdCQUF3QjtJQUM1QixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsaUJBQWlCO0lBQ3JCLEVBQUUsRUFBRSxvQkFBb0I7SUFDeEIsRUFBRSxFQUFFLGFBQWE7SUFDakIsRUFBRSxFQUFFLGFBQWE7SUFDakIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGNBQWM7SUFDbEIsRUFBRSxFQUFFLG1CQUFtQjtJQUN2QixFQUFFLEVBQUUscUJBQXFCO0lBQ3pCLEVBQUUsRUFBRSwwQkFBMEI7SUFDOUIsRUFBRSxFQUFFLGNBQWM7SUFDbEIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLGVBQWU7SUFDbkIsRUFBRSxFQUFFLHNCQUFzQjtJQUMxQixFQUFFLEVBQUUsdUJBQXVCO0lBQzNCLEVBQUUsRUFBRSxvQkFBb0I7SUFDeEIsRUFBRSxFQUFFLHlCQUF5QjtJQUM3QixFQUFFLEVBQUUsV0FBVztJQUNmLEVBQUUsRUFBRSxrQkFBa0I7SUFDdEIsRUFBRSxFQUFFLFdBQVc7SUFDZixFQUFFLEVBQUUsV0FBVztJQUNmLEVBQUUsRUFBRSxZQUFZO0lBQ2hCLEVBQUUsRUFBRSxpQkFBaUI7SUFDckIsRUFBRSxFQUFFLG1CQUFtQjtJQUN2QixFQUFFLEVBQUUsd0JBQXdCO0NBQy9CLENBQUM7QUFFVyxRQUFBLG9CQUFvQixHQUFHO0lBQ2hDLENBQUMsRUFBRSxpQkFBaUI7SUFDcEIsQ0FBQyxFQUFFLG9CQUFvQjtJQUN2QixDQUFDLEVBQUUsYUFBYTtJQUNoQixDQUFDLEVBQUUsYUFBYTtJQUNoQixDQUFDLEVBQUUsZUFBZTtJQUNsQixDQUFDLEVBQUUsY0FBYztJQUNqQixDQUFDLEVBQUUsbUJBQW1CO0lBQ3RCLENBQUMsRUFBRSxxQkFBcUI7SUFDeEIsQ0FBQyxFQUFFLDBCQUEwQjtJQUM3QixDQUFDLEVBQUUsY0FBYztJQUNqQixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsZUFBZTtJQUNuQixFQUFFLEVBQUUsc0JBQXNCO0lBQzFCLEVBQUUsRUFBRSx1QkFBdUI7SUFDM0IsRUFBRSxFQUFFLG9CQUFvQjtJQUN4QixFQUFFLEVBQUUseUJBQXlCO0NBQ2hDLENBQUM7QUFFVyxRQUFBLFVBQVUsR0FBRztJQUN0QixDQUFDLEVBQUUsUUFBUTtJQUNYLENBQUMsRUFBRSxRQUFRO0lBQ1gsQ0FBQyxFQUFFLFNBQVM7SUFDWixDQUFDLEVBQUUsV0FBVztJQUNkLENBQUMsRUFBRSxRQUFRO0NBQ2QsQ0FBQztBQUVGLElBQVksa0JBR1g7QUFIRCxXQUFZLGtCQUFrQjtJQUMxQix5Q0FBbUIsQ0FBQTtJQUNuQix1Q0FBaUIsQ0FBQTtBQUNyQixDQUFDLEVBSFcsa0JBQWtCLGtDQUFsQixrQkFBa0IsUUFHN0I7QUFFWSxRQUFBLHVCQUF1QixHQUFHO0lBQ25DLENBQUMsa0JBQWtCLENBQUMsT0FBTyxDQUFDLEVBQUUsZ0NBQXdCO0lBQ3RELENBQUMsa0JBQWtCLENBQUMsTUFBTSxDQUFDLEVBQUUsK0JBQXVCO0NBQ3ZELENBQUM7QUFFVyxRQUFBLG9CQUFvQixHQUFHO0lBQ2hDLENBQUMsa0JBQWtCLENBQUMsT0FBTyxDQUFDLEVBQUUsNkJBQXFCO0lBQ25ELENBQUMsa0JBQWtCLENBQUMsTUFBTSxDQUFDLEVBQUUsNEJBQW9CO0NBQ3BELENBQUM7QUF3QkYsTUFBYSxxQkFBcUI7SUFLOUI7UUFDSSxJQUFJLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDO1FBQ3ZDLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBQSxXQUFJLEVBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxRQUFRLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsUUFBUSxDQUFDLENBQUM7SUFDckcsQ0FBQztJQUVNLE1BQU0sQ0FBQyxXQUFXO1FBQ3JCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUNsQyxxQkFBcUIsQ0FBQyxRQUFRLEdBQUcsSUFBSSxxQkFBcUIsRUFBRSxDQUFDO1FBQ2pFLENBQUM7UUFDRCxPQUFPLHFCQUFxQixDQUFDLFFBQVEsQ0FBQztJQUMxQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxlQUFlLENBQUMsUUFBNEI7UUFDaEQsT0FBTyxJQUFBLFdBQUksRUFBQyxJQUFJLENBQUMsY0FBYyxFQUFFLFFBQVEsQ0FBQyxDQUFDO0lBQy9DLENBQUM7SUFFRDs7T0FFRztJQUNLLFdBQVcsQ0FBQyxRQUE0QixFQUFFLElBQVk7UUFDMUQsT0FBTyxJQUFBLFdBQUksRUFBQyxJQUFJLENBQUMsZUFBZSxDQUFDLFFBQVEsQ0FBQyxFQUFFLEdBQUcsSUFBSSxPQUFPLENBQUMsQ0FBQztJQUNoRSxDQUFDO0lBRUQ7O09BRUc7SUFDSyxxQkFBcUIsQ0FBQyxPQUFlO1FBQ3pDLElBQUksQ0FBQyxJQUFBLGVBQVUsRUFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ3ZCLElBQUEsY0FBUyxFQUFDLE9BQU8sRUFBRSxFQUFFLFNBQVMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQzVDLENBQUM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSSx5QkFBeUIsQ0FBQyxRQUE0QjtRQUN6RCxNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRXBELElBQUksQ0FBQyxJQUFBLGVBQVUsRUFBQyxZQUFZLENBQUMsRUFBRSxDQUFDO1lBQzVCLE9BQU8sRUFBRSxDQUFDO1FBQ2QsQ0FBQztRQUVELE1BQU0sS0FBSyxHQUFHLElBQUEsZ0JBQVcsRUFBQyxZQUFZLENBQUMsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDL0UsTUFBTSxXQUFXLEdBQXFCLEVBQUUsQ0FBQztRQUV6QyxLQUFLLE1BQU0sSUFBSSxJQUFJLEtBQUssRUFBRSxDQUFDO1lBQ3ZCLElBQUksQ0FBQztnQkFDRCxNQUFNLFFBQVEsR0FBRyxJQUFBLFdBQUksRUFBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBQzFDLE1BQU0sT0FBTyxHQUFHLElBQUEsaUJBQVksRUFBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLENBQUM7Z0JBQ2hELE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFtQixDQUFDO2dCQUVuRCx1Q0FBdUM7Z0JBQ3ZDLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDLENBQUM7Z0JBQ3RDLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDM0IsQ0FBQztZQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7Z0JBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQ0FBc0MsSUFBSSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDeEUsQ0FBQztRQUNMLENBQUM7UUFFRCxPQUFPLFdBQVcsQ0FBQztJQUN2QixDQUFDO0lBRUQ7O09BRUc7SUFDSSxjQUFjLENBQUMsUUFBNEIsRUFBRSxJQUFZO1FBQzVELE1BQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxDQUFDO1FBRWxELElBQUksQ0FBQyxJQUFBLGVBQVUsRUFBQyxRQUFRLENBQUMsRUFBRSxDQUFDO1lBQ3hCLE9BQU8sSUFBSSxDQUFDO1FBQ2hCLENBQUM7UUFFRCxJQUFJLENBQUM7WUFDRCxNQUFNLE9BQU8sR0FBRyxJQUFBLGlCQUFZLEVBQUMsUUFBUSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1lBQ2hELE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFtQixDQUFDO1lBQ25ELElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLENBQUMseUJBQXlCO1lBQzNDLE9BQU8sSUFBSSxDQUFDO1FBQ2hCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxpQ0FBaUMsSUFBSSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDL0QsT0FBTyxJQUFJLENBQUM7UUFDaEIsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLGNBQWMsQ0FBQyxRQUE0QixFQUFFLFVBQTBCO1FBQzFFLElBQUksQ0FBQztZQUNELE1BQU0sWUFBWSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDcEQsSUFBSSxDQUFDLHFCQUFxQixDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRXpDLE1BQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxFQUFFLFVBQVUsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUM3RCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFVBQVUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFFcEQsSUFBQSxrQkFBYSxFQUFDLFFBQVEsRUFBRSxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUM7WUFDMUMsT0FBTyxJQUFJLENBQUM7UUFDaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGlDQUFpQyxVQUFVLENBQUMsSUFBSSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDMUUsT0FBTyxLQUFLLENBQUM7UUFDakIsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLGdCQUFnQixDQUFDLFFBQTRCLEVBQUUsSUFBWTtRQUM5RCxJQUFJLENBQUM7WUFDRCxNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsQ0FBQztZQUVsRCxJQUFJLElBQUEsZUFBVSxFQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7Z0JBQ3ZCLE1BQU0sRUFBRSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDekIsRUFBRSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDeEIsT0FBTyxJQUFJLENBQUM7WUFDaEIsQ0FBQztZQUNELE9BQU8sS0FBSyxDQUFDO1FBQ2pCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQ0FBbUMsSUFBSSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDakUsT0FBTyxLQUFLLENBQUM7UUFDakIsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLGdCQUFnQixDQUFDLFFBQTRCLEVBQUUsSUFBWTtRQUM5RCxNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNsRCxPQUFPLElBQUEsZUFBVSxFQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2hDLENBQUM7SUFFRDs7T0FFRztJQUNJLGNBQWMsQ0FBQyxJQUFZO1FBQzlCLEtBQUssTUFBTSxRQUFRLElBQUksTUFBTSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFLENBQUM7WUFDdkQsTUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLENBQUM7WUFDakQsSUFBSSxJQUFJLEVBQUUsQ0FBQztnQkFDUCxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO1lBQzlCLENBQUM7UUFDTCxDQUFDO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQztJQUVEOztPQUVHO0lBQ0ksa0JBQWtCLENBQUMsUUFBNEIsRUFBRSxXQUFtQixZQUFZO1FBQ25GLElBQUksT0FBTyxHQUFHLENBQUMsQ0FBQztRQUNoQixJQUFJLElBQUksR0FBRyxHQUFHLFFBQVEsSUFBSSxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBRWhFLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDO1lBQzNDLE9BQU8sRUFBRSxDQUFDO1lBQ1YsSUFBSSxHQUFHLEdBQUcsUUFBUSxJQUFJLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDaEUsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFFRDs7T0FFRztJQUNJLG1CQUFtQixDQUFDLFFBQTRCLEVBQUUsSUFBYTtRQUNsRSxNQUFNLFNBQVMsR0FBRyxJQUFJLElBQUksSUFBSSxDQUFDLGtCQUFrQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRTVELElBQUksVUFBVSxHQUFtQjtZQUM3QixJQUFJLEVBQUUsU0FBUztZQUNmLFlBQVksRUFBRSxDQUFDO1lBQ2YsVUFBVSxFQUFFLEVBQUU7WUFDZCxPQUFPLEVBQUUsRUFBRTtTQUNkLENBQUM7UUFFRixPQUFPLFVBQVUsQ0FBQztJQUN0QixDQUFDO0lBRUQ7O09BRUc7SUFDSSxtQkFBbUIsQ0FBQyxRQUE0QixFQUFFLFlBQW9CLEVBQUUsT0FBZ0I7UUFDM0YsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsWUFBWSxDQUFDLENBQUM7UUFDN0QsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxJQUFJLENBQUM7UUFDaEIsQ0FBQztRQUVELE1BQU0sU0FBUyxHQUFHLE9BQU8sSUFBSSxJQUFJLENBQUMsa0JBQWtCLENBQUMsUUFBUSxFQUFFLFlBQVksQ0FBQyxDQUFDO1FBRTdFLDhCQUE4QjtRQUM5QixNQUFNLFNBQVMsR0FBbUI7WUFDOUIsSUFBSSxFQUFFLFNBQVM7WUFDZixZQUFZLEVBQUUsUUFBUSxDQUFDLFlBQVk7WUFDbkMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsbUJBQU0sU0FBUyxFQUFHLENBQUM7WUFDcEUsT0FBTyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsbUJBQU0sTUFBTSxFQUFHLENBQUM7U0FDM0QsQ0FBQztRQUVGLE9BQU8sU0FBUyxDQUFDO0lBQ3JCLENBQUM7SUFFRDs7T0FFRztJQUNJLHFCQUFxQjtRQUN4QixNQUFNLE1BQU0sR0FBcUMsRUFBRSxDQUFDO1FBRXBELEtBQUssTUFBTSxRQUFRLElBQUksTUFBTSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFLENBQUM7WUFDdkQsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLHlCQUF5QixDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQzdELE1BQU0sQ0FBQyxRQUFRLENBQUMsR0FBRyxXQUFXLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3RELENBQUM7UUFFRCxPQUFPLE1BQU0sQ0FBQztJQUNsQixDQUFDO0NBQ0o7QUF4TkQsc0RBd05DIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZXhpc3RzU3luYywgcmVhZEZpbGVTeW5jLCB3cml0ZUZpbGVTeW5jLCBta2RpclN5bmMsIHJlYWRkaXJTeW5jIH0gZnJvbSAnZnMnO1xuaW1wb3J0IHsgam9pbiwgZGlybmFtZSB9IGZyb20gJ3BhdGgnO1xuXG4vLyBFbnVtIGRlZmluaXRpb25zIGZvciBVSSBkaXNwbGF5XG5leHBvcnQgY29uc3QgQ29uZGl0aW9uT3BFbnVtID0ge1xuICAgIDA6ICdBbmQnLFxuICAgIDE6ICdPcidcbn07XG5cbmV4cG9ydCBjb25zdCBDb21wYXJlT3BFbnVtID0ge1xuICAgIDA6ICc9PScsXG4gICAgMTogJyE9JyxcbiAgICAyOiAnPicsXG4gICAgMzogJzwnLFxuICAgIDQ6ICc+PScsXG4gICAgNTogJzw9J1xufTtcblxuLy8gVGhlc2Ugc2hvdWxkIG1hdGNoIHlvdXIgYWN0dWFsIGdhbWUgZW51bXMgZGVmaW5lZCBpbiBFdmVudENvbmRpdGlvblR5cGUudHNcbmV4cG9ydCBjb25zdCBFbWl0dGVyQ29uZGl0aW9uVHlwZUVudW0gPSB7XG4gICAgMDogJ0xldmVsX0R1cmF0aW9uJyxcbiAgICAxOiAnTGV2ZWxfRGlzdGFuY2UnLFxuICAgIDI6ICdMZXZlbF9JbmZMZXZlbCcsXG4gICAgMzogJ0xldmVsX0NoYWxsZW5nZUxldmVsJyxcbiAgICA0OiAnUGxheWVyX0FjdExldmVsJyxcbiAgICA1OiAnUGxheWVyX1Bvc1gnLFxuICAgIDY6ICdQbGF5ZXJfUG9zWScsXG4gICAgNzogJ1BsYXllcl9MaWZlUGVyY2VudCcsXG4gICAgODogJ1BsYXllcl9HYWluQnVmZicsXG4gICAgOTogJ1VuaXRfTGlmZScsXG4gICAgMTA6ICdVbml0X0xpZmVQZXJjZW50JyxcbiAgICAxMTogJ1VuaXRfRHVyYXRpb24nLFxuICAgIDEyOiAnVW5pdF9Qb3NYJyxcbiAgICAxMzogJ1VuaXRfUG9zWScsXG4gICAgMTQ6ICdVbml0X1NwZWVkJyxcbiAgICAxNTogJ1VuaXRfU3BlZWRBbmdsZScsXG4gICAgMTY6ICdVbml0X0FjY2VsZXJhdGlvbicsXG4gICAgMTc6ICdVbml0X0FjY2VsZXJhdGlvbkFuZ2xlJyxcbiAgICAxODogJ1VuaXRfRGlzdGFuY2VUb1BsYXllcicsXG4gICAgMTk6ICdVbml0X0FuZ2xlVG9QbGF5ZXInLFxuICAgIDIwOiAnRW1pdHRlcl9BY3RpdmUnLFxuICAgIDIxOiAnRW1pdHRlcl9Jbml0aWFsRGVsYXknLFxuICAgIDIyOiAnRW1pdHRlcl9QcmV3YXJtJyxcbiAgICAyMzogJ0VtaXR0ZXJfUHJld2FybUR1cmF0aW9uJyxcbiAgICAyNDogJ0VtaXR0ZXJfRHVyYXRpb24nLFxuICAgIDI1OiAnRW1pdHRlcl9FbGFwc2VkVGltZScsXG4gICAgMjY6ICdFbWl0dGVyX0xvb3AnLFxuICAgIDI3OiAnRW1pdHRlcl9Mb29wSW50ZXJ2YWwnLFxuICAgIDI4OiAnRW1pdHRlcl9FbWl0SW50ZXJ2YWwnLFxuICAgIDI5OiAnRW1pdHRlcl9FbWl0Q291bnQnLFxuICAgIDMwOiAnRW1pdHRlcl9FbWl0T2Zmc2V0WCcsXG4gICAgMzE6ICdFbWl0dGVyX0FuZ2xlJyxcbiAgICAzMjogJ0VtaXR0ZXJfQ291bnQnLFxuICAgIDMzOiAnQnVsbGV0X1Nwcml0ZScsXG4gICAgMzQ6ICdCdWxsZXRfU2NhbGUnLFxuICAgIDM1OiAnQnVsbGV0X0NvbG9yUicsXG4gICAgMzY6ICdCdWxsZXRfQ29sb3JHJyxcbiAgICAzNzogJ0J1bGxldF9Db2xvckInLFxuICAgIDM4OiAnQnVsbGV0X0R1cmF0aW9uJyxcbiAgICAzOTogJ0J1bGxldF9FbGFwc2VkVGltZScsXG4gICAgNDA6ICdCdWxsZXRfU3BlZWQnLFxuICAgIDQxOiAnQnVsbGV0X0FjY2VsZXJhdGlvbicsXG4gICAgNDI6ICdCdWxsZXRfQWNjZWxlcmF0aW9uQW5nbGUnLFxuICAgIDQzOiAnQnVsbGV0X0ZhY2luZ01vdmVEaXInLFxuICAgIDQ0OiAnQnVsbGV0X0Rlc3RydWN0aXZlJyxcbiAgICA0NTogJ0J1bGxldF9EZXN0cnVjdGl2ZU9uSGl0Jyxcbn07XG5cbmV4cG9ydCBjb25zdCBCdWxsZXRDb25kaXRpb25UeXBlRW51bSA9IHtcbiAgICAwOiAnQnVsbGV0X0R1cmF0aW9uJyxcbiAgICAxOiAnQnVsbGV0X0VsYXBzZWRUaW1lJyxcbiAgICAyOiAnQnVsbGV0X1Bvc1gnLFxuICAgIDM6ICdCdWxsZXRfUG9zWScsXG4gICAgNDogJ0J1bGxldF9EYW1hZ2UnLFxuICAgIDU6ICdCdWxsZXRfU3BlZWQnLFxuICAgIDY6ICdCdWxsZXRfU3BlZWRBbmdsZScsXG4gICAgNzogJ0J1bGxldF9BY2NlbGVyYXRpb24nLFxuICAgIDg6ICdCdWxsZXRfQWNjZWxlcmF0aW9uQW5nbGUnLFxuICAgIDk6ICdCdWxsZXRfU2NhbGUnLFxuICAgIDEwOiAnQnVsbGV0X0NvbG9yUicsXG4gICAgMTE6ICdCdWxsZXRfQ29sb3JHJyxcbiAgICAxMjogJ0J1bGxldF9Db2xvckInLFxuICAgIDEzOiAnQnVsbGV0X0ZhY2luZ01vdmVEaXInLFxuICAgIDE0OiAnQnVsbGV0X0Rlc3RydWN0aXZlJyxcbiAgICAxNTogJ0J1bGxldF9EZXN0cnVjdGl2ZU9uSGl0Jyxcbn1cblxuZXhwb3J0IGNvbnN0IEVtaXR0ZXJBY3Rpb25UeXBlRW51bSA9IHtcbiAgICAwOiAnRW1pdHRlcl9BY3RpdmUnLFxuICAgIDE6ICdFbWl0dGVyX0luaXRpYWxEZWxheScsXG4gICAgMjogJ0VtaXR0ZXJfUHJld2FybScsXG4gICAgMzogJ0VtaXR0ZXJfUHJld2FybUR1cmF0aW9uJyxcbiAgICA0OiAnRW1pdHRlcl9EdXJhdGlvbicsXG4gICAgNTogJ0VtaXR0ZXJfRWxhcHNlZFRpbWUnLFxuICAgIDY6ICdFbWl0dGVyX0xvb3AnLFxuICAgIDc6ICdFbWl0dGVyX0xvb3BJbnRlcnZhbCcsXG4gICAgODogJ0VtaXR0ZXJfUGVyRW1pdEludGVydmFsJyxcbiAgICA5OiAnRW1pdHRlcl9QZXJFbWl0Q291bnQnLFxuICAgIDEwOiAnRW1pdHRlcl9QZXJFbWl0T2Zmc2V0WCcsXG4gICAgMTE6ICdFbWl0dGVyX0FuZ2xlJyxcbiAgICAxMjogJ0VtaXR0ZXJfQ291bnQnLFxuICAgIDEzOiAnQnVsbGV0X0R1cmF0aW9uJyxcbiAgICAxNDogJ0J1bGxldF9FbGFwc2VkVGltZScsXG4gICAgMTU6ICdCdWxsZXRfUG9zWCcsXG4gICAgMTY6ICdCdWxsZXRfUG9zWScsXG4gICAgMTc6ICdCdWxsZXRfRGFtYWdlJyxcbiAgICAxODogJ0J1bGxldF9TcGVlZCcsXG4gICAgMTk6ICdCdWxsZXRfU3BlZWRBbmdsZScsXG4gICAgMjA6ICdCdWxsZXRfQWNjZWxlcmF0aW9uJyxcbiAgICAyMTogJ0J1bGxldF9BY2NlbGVyYXRpb25BbmdsZScsXG4gICAgMjI6ICdCdWxsZXRfU2NhbGUnLFxuICAgIDIzOiAnQnVsbGV0X0NvbG9yUicsXG4gICAgMjQ6ICdCdWxsZXRfQ29sb3JHJyxcbiAgICAyNTogJ0J1bGxldF9Db2xvckInLFxuICAgIDI2OiAnQnVsbGV0X0NvbG9yQScsXG4gICAgMjc6ICdCdWxsZXRfRmFjZU1vdmluZ0RpcicsXG4gICAgMjg6ICdCdWxsZXRfVHJhY2tpbmdUYXJnZXQnLFxuICAgIDI5OiAnQnVsbGV0X0Rlc3RydWN0aXZlJyxcbiAgICAzMDogJ0J1bGxldF9EZXN0cnVjdGl2ZU9uSGl0JyxcbiAgICAzMTogJ1VuaXRfTGlmZScsXG4gICAgMzI6ICdVbml0X0xpZmVQZXJjZW50JyxcbiAgICAzMzogJ1VuaXRfUG9zWCcsXG4gICAgMzQ6ICdVbml0X1Bvc1knLFxuICAgIDM1OiAnVW5pdF9TcGVlZCcsXG4gICAgMzY6ICdVbml0X1NwZWVkQW5nbGUnLFxuICAgIDM3OiAnVW5pdF9BY2NlbGVyYXRpb24nLFxuICAgIDM4OiAnVW5pdF9BY2NlbGVyYXRpb25BbmdsZScsXG59O1xuXG5leHBvcnQgY29uc3QgQnVsbGV0QWN0aW9uVHlwZUVudW0gPSB7XG4gICAgMDogJ0J1bGxldF9EdXJhdGlvbicsXG4gICAgMTogJ0J1bGxldF9FbGFwc2VkVGltZScsXG4gICAgMjogJ0J1bGxldF9Qb3NYJyxcbiAgICAzOiAnQnVsbGV0X1Bvc1knLFxuICAgIDQ6ICdCdWxsZXRfRGFtYWdlJyxcbiAgICA1OiAnQnVsbGV0X1NwZWVkJyxcbiAgICA2OiAnQnVsbGV0X1NwZWVkQW5nbGUnLFxuICAgIDc6ICdCdWxsZXRfQWNjZWxlcmF0aW9uJyxcbiAgICA4OiAnQnVsbGV0X0FjY2VsZXJhdGlvbkFuZ2xlJyxcbiAgICA5OiAnQnVsbGV0X1NjYWxlJyxcbiAgICAxMDogJ0J1bGxldF9Db2xvclInLFxuICAgIDExOiAnQnVsbGV0X0NvbG9yRycsXG4gICAgMTI6ICdCdWxsZXRfQ29sb3JCJyxcbiAgICAxMzogJ0J1bGxldF9Db2xvckEnLFxuICAgIDE0OiAnQnVsbGV0X0ZhY2VNb3ZpbmdEaXInLFxuICAgIDE1OiAnQnVsbGV0X1RyYWNraW5nVGFyZ2V0JyxcbiAgICAxNjogJ0J1bGxldF9EZXN0cnVjdGl2ZScsXG4gICAgMTc6ICdCdWxsZXRfRGVzdHJ1Y3RpdmVPbkhpdCcsXG59O1xuXG5leHBvcnQgY29uc3QgRWFzaW5nRW51bSA9IHtcbiAgICAwOiAnTGluZWFyJyxcbiAgICAxOiAnRWFzZUluJyxcbiAgICAyOiAnRWFzZU91dCcsXG4gICAgMzogJ0Vhc2VJbk91dCcsXG4gICAgNDogJ0JvdW5jZSdcbn07XG5cbmV4cG9ydCBlbnVtIEV2ZW50R3JvdXBDYXRlZ29yeSB7XG4gICAgRW1pdHRlciA9ICdFbWl0dGVyJyxcbiAgICBCdWxsZXQgPSAnQnVsbGV0Jyxcbn1cblxuZXhwb3J0IGNvbnN0IENvbmRpdGlvblR5cGVCeUNhdGVnb3J5ID0ge1xuICAgIFtFdmVudEdyb3VwQ2F0ZWdvcnkuRW1pdHRlcl06IEVtaXR0ZXJDb25kaXRpb25UeXBlRW51bSxcbiAgICBbRXZlbnRHcm91cENhdGVnb3J5LkJ1bGxldF06IEJ1bGxldENvbmRpdGlvblR5cGVFbnVtLFxufTtcblxuZXhwb3J0IGNvbnN0IEFjdGlvblR5cGVCeUNhdGVnb3J5ID0ge1xuICAgIFtFdmVudEdyb3VwQ2F0ZWdvcnkuRW1pdHRlcl06IEVtaXR0ZXJBY3Rpb25UeXBlRW51bSxcbiAgICBbRXZlbnRHcm91cENhdGVnb3J5LkJ1bGxldF06IEJ1bGxldEFjdGlvblR5cGVFbnVtLFxufTtcblxuLy8gRXZlbnRHcm91cERhdGEgaW50ZXJmYWNlcyAobWF0Y2hpbmcgdGhlIGdhbWUgY29kZSBzdHJ1Y3R1cmUpXG5leHBvcnQgaW50ZXJmYWNlIEV2ZW50Q29uZGl0aW9uRGF0YSB7XG4gICAgb3A6IG51bWJlcjsgLy8gZUNvbmRpdGlvbk9wXG4gICAgdHlwZTogbnVtYmVyOyAvLyBlRXZlbnRDb25kaXRpb25UeXBlXG4gICAgY29tcGFyZU9wOiBudW1iZXI7IC8vIGVDb21wYXJlT3BcbiAgICB0YXJnZXRWYWx1ZTogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEV2ZW50QWN0aW9uRGF0YSB7XG4gICAgdHlwZTogbnVtYmVyOyAvLyBlRXZlbnRBY3Rpb25UeXBlXG4gICAgZHVyYXRpb246IG51bWJlcjtcbiAgICB0YXJnZXRWYWx1ZTogbnVtYmVyO1xuICAgIGVhc2luZzogbnVtYmVyOyAvLyBlRWFzaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRXZlbnRHcm91cERhdGEge1xuICAgIG5hbWU6IHN0cmluZztcbiAgICB0cmlnZ2VyQ291bnQ6IG51bWJlcjtcbiAgICBjb25kaXRpb25zOiBFdmVudENvbmRpdGlvbkRhdGFbXTtcbiAgICBhY3Rpb25zOiBFdmVudEFjdGlvbkRhdGFbXTtcbn1cblxuZXhwb3J0IGNsYXNzIEV2ZW50R3JvdXBEYXRhTWFuYWdlciB7XG4gICAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IEV2ZW50R3JvdXBEYXRhTWFuYWdlcjtcbiAgICBwcml2YXRlIHByb2plY3RQYXRoOiBzdHJpbmc7XG4gICAgcHJpdmF0ZSBldmVudHNCYXNlUGF0aDogc3RyaW5nO1xuXG4gICAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5wcm9qZWN0UGF0aCA9IEVkaXRvci5Qcm9qZWN0LnBhdGg7XG4gICAgICAgIHRoaXMuZXZlbnRzQmFzZVBhdGggPSBqb2luKHRoaXMucHJvamVjdFBhdGgsICdhc3NldHMnLCAncmVzb3VyY2VzJywgJ0dhbWUnLCAnZW1pdHRlcicsICdldmVudHMnKTtcbiAgICB9XG5cbiAgICBwdWJsaWMgc3RhdGljIGdldEluc3RhbmNlKCk6IEV2ZW50R3JvdXBEYXRhTWFuYWdlciB7XG4gICAgICAgIGlmICghRXZlbnRHcm91cERhdGFNYW5hZ2VyLmluc3RhbmNlKSB7XG4gICAgICAgICAgICBFdmVudEdyb3VwRGF0YU1hbmFnZXIuaW5zdGFuY2UgPSBuZXcgRXZlbnRHcm91cERhdGFNYW5hZ2VyKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEV2ZW50R3JvdXBEYXRhTWFuYWdlci5pbnN0YW5jZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGZ1bGwgcGF0aCBmb3IgYSBjYXRlZ29yeSBmb2xkZXJcbiAgICAgKi9cbiAgICBwcml2YXRlIGdldENhdGVnb3J5UGF0aChjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5KTogc3RyaW5nIHtcbiAgICAgICAgcmV0dXJuIGpvaW4odGhpcy5ldmVudHNCYXNlUGF0aCwgY2F0ZWdvcnkpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgZnVsbCBmaWxlIHBhdGggZm9yIGFuIEV2ZW50R3JvdXBEYXRhXG4gICAgICovXG4gICAgcHJpdmF0ZSBnZXRGaWxlUGF0aChjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5LCBuYW1lOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgICAgICByZXR1cm4gam9pbih0aGlzLmdldENhdGVnb3J5UGF0aChjYXRlZ29yeSksIGAke25hbWV9Lmpzb25gKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBFbnN1cmUgZGlyZWN0b3J5IGV4aXN0c1xuICAgICAqL1xuICAgIHByaXZhdGUgZW5zdXJlRGlyZWN0b3J5RXhpc3RzKGRpclBhdGg6IHN0cmluZyk6IHZvaWQge1xuICAgICAgICBpZiAoIWV4aXN0c1N5bmMoZGlyUGF0aCkpIHtcbiAgICAgICAgICAgIG1rZGlyU3luYyhkaXJQYXRoLCB7IHJlY3Vyc2l2ZTogdHJ1ZSB9KTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIExvYWQgYWxsIEV2ZW50R3JvdXBEYXRhIGZpbGVzIGZyb20gYSBjYXRlZ29yeVxuICAgICAqL1xuICAgIHB1YmxpYyBsb2FkRXZlbnRHcm91cHNCeUNhdGVnb3J5KGNhdGVnb3J5OiBFdmVudEdyb3VwQ2F0ZWdvcnkpOiBFdmVudEdyb3VwRGF0YVtdIHtcbiAgICAgICAgY29uc3QgY2F0ZWdvcnlQYXRoID0gdGhpcy5nZXRDYXRlZ29yeVBhdGgoY2F0ZWdvcnkpO1xuICAgICAgICBcbiAgICAgICAgaWYgKCFleGlzdHNTeW5jKGNhdGVnb3J5UGF0aCkpIHtcbiAgICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGZpbGVzID0gcmVhZGRpclN5bmMoY2F0ZWdvcnlQYXRoKS5maWx0ZXIoZmlsZSA9PiBmaWxlLmVuZHNXaXRoKCcuanNvbicpKTtcbiAgICAgICAgY29uc3QgZXZlbnRHcm91cHM6IEV2ZW50R3JvdXBEYXRhW10gPSBbXTtcblxuICAgICAgICBmb3IgKGNvbnN0IGZpbGUgb2YgZmlsZXMpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmlsZVBhdGggPSBqb2luKGNhdGVnb3J5UGF0aCwgZmlsZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgY29udGVudCA9IHJlYWRGaWxlU3luYyhmaWxlUGF0aCwgJ3V0Zi04Jyk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IEpTT04ucGFyc2UoY29udGVudCkgYXMgRXZlbnRHcm91cERhdGE7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLy8gRW5zdXJlIHRoZSBuYW1lIG1hdGNoZXMgdGhlIGZpbGVuYW1lXG4gICAgICAgICAgICAgICAgZGF0YS5uYW1lID0gZmlsZS5yZXBsYWNlKCcuanNvbicsICcnKTtcbiAgICAgICAgICAgICAgICBldmVudEdyb3Vwcy5wdXNoKGRhdGEpO1xuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gbG9hZCBFdmVudEdyb3VwRGF0YSBmcm9tICR7ZmlsZX06YCwgZXJyb3IpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGV2ZW50R3JvdXBzO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIExvYWQgYSBzcGVjaWZpYyBFdmVudEdyb3VwRGF0YSBieSBuYW1lIGFuZCBjYXRlZ29yeVxuICAgICAqL1xuICAgIHB1YmxpYyBsb2FkRXZlbnRHcm91cChjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5LCBuYW1lOiBzdHJpbmcpOiBFdmVudEdyb3VwRGF0YSB8IG51bGwge1xuICAgICAgICBjb25zdCBmaWxlUGF0aCA9IHRoaXMuZ2V0RmlsZVBhdGgoY2F0ZWdvcnksIG5hbWUpO1xuICAgICAgICBcbiAgICAgICAgaWYgKCFleGlzdHNTeW5jKGZpbGVQYXRoKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgY29udGVudCA9IHJlYWRGaWxlU3luYyhmaWxlUGF0aCwgJ3V0Zi04Jyk7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShjb250ZW50KSBhcyBFdmVudEdyb3VwRGF0YTtcbiAgICAgICAgICAgIGRhdGEubmFtZSA9IG5hbWU7IC8vIEVuc3VyZSBuYW1lIGlzIGNvcnJlY3RcbiAgICAgICAgICAgIHJldHVybiBkYXRhO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGxvYWQgRXZlbnRHcm91cERhdGEgJHtuYW1lfTpgLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIFNhdmUgYW4gRXZlbnRHcm91cERhdGEgdG8gZmlsZVxuICAgICAqL1xuICAgIHB1YmxpYyBzYXZlRXZlbnRHcm91cChjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5LCBldmVudEdyb3VwOiBFdmVudEdyb3VwRGF0YSk6IGJvb2xlYW4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgY2F0ZWdvcnlQYXRoID0gdGhpcy5nZXRDYXRlZ29yeVBhdGgoY2F0ZWdvcnkpO1xuICAgICAgICAgICAgdGhpcy5lbnN1cmVEaXJlY3RvcnlFeGlzdHMoY2F0ZWdvcnlQYXRoKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3QgZmlsZVBhdGggPSB0aGlzLmdldEZpbGVQYXRoKGNhdGVnb3J5LCBldmVudEdyb3VwLm5hbWUpO1xuICAgICAgICAgICAgY29uc3QgY29udGVudCA9IEpTT04uc3RyaW5naWZ5KGV2ZW50R3JvdXAsIG51bGwsIDIpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICB3cml0ZUZpbGVTeW5jKGZpbGVQYXRoLCBjb250ZW50LCAndXRmLTgnKTtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIHNhdmUgRXZlbnRHcm91cERhdGEgJHtldmVudEdyb3VwLm5hbWV9OmAsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhbiBFdmVudEdyb3VwRGF0YSBmaWxlXG4gICAgICovXG4gICAgcHVibGljIGRlbGV0ZUV2ZW50R3JvdXAoY2F0ZWdvcnk6IEV2ZW50R3JvdXBDYXRlZ29yeSwgbmFtZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBmaWxlUGF0aCA9IHRoaXMuZ2V0RmlsZVBhdGgoY2F0ZWdvcnksIG5hbWUpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBpZiAoZXhpc3RzU3luYyhmaWxlUGF0aCkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJyk7XG4gICAgICAgICAgICAgICAgZnMudW5saW5rU3luYyhmaWxlUGF0aCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gZGVsZXRlIEV2ZW50R3JvdXBEYXRhICR7bmFtZX06YCwgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQ2hlY2sgaWYgYW4gRXZlbnRHcm91cERhdGEgZmlsZSBleGlzdHNcbiAgICAgKi9cbiAgICBwdWJsaWMgZXZlbnRHcm91cEV4aXN0cyhjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5LCBuYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICAgICAgY29uc3QgZmlsZVBhdGggPSB0aGlzLmdldEZpbGVQYXRoKGNhdGVnb3J5LCBuYW1lKTtcbiAgICAgICAgcmV0dXJuIGV4aXN0c1N5bmMoZmlsZVBhdGgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEZpbmQgYW4gRXZlbnRHcm91cERhdGEgYnkgbmFtZSBhY3Jvc3MgYWxsIGNhdGVnb3JpZXNcbiAgICAgKi9cbiAgICBwdWJsaWMgZmluZEV2ZW50R3JvdXAobmFtZTogc3RyaW5nKTogeyBjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5OyBkYXRhOiBFdmVudEdyb3VwRGF0YSB9IHwgbnVsbCB7XG4gICAgICAgIGZvciAoY29uc3QgY2F0ZWdvcnkgb2YgT2JqZWN0LnZhbHVlcyhFdmVudEdyb3VwQ2F0ZWdvcnkpKSB7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5sb2FkRXZlbnRHcm91cChjYXRlZ29yeSwgbmFtZSk7XG4gICAgICAgICAgICBpZiAoZGF0YSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7IGNhdGVnb3J5LCBkYXRhIH07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogR2VuZXJhdGUgYSB1bmlxdWUgbmFtZSBmb3IgYSBuZXcgRXZlbnRHcm91cERhdGFcbiAgICAgKi9cbiAgICBwdWJsaWMgZ2VuZXJhdGVVbmlxdWVOYW1lKGNhdGVnb3J5OiBFdmVudEdyb3VwQ2F0ZWdvcnksIGJhc2VOYW1lOiBzdHJpbmcgPSAnRXZlbnRHcm91cCcpOiBzdHJpbmcge1xuICAgICAgICBsZXQgY291bnRlciA9IDE7XG4gICAgICAgIGxldCBuYW1lID0gYCR7YmFzZU5hbWV9XyR7Y291bnRlci50b1N0cmluZygpLnBhZFN0YXJ0KDMsICcwJyl9YDtcbiAgICAgICAgXG4gICAgICAgIHdoaWxlICh0aGlzLmV2ZW50R3JvdXBFeGlzdHMoY2F0ZWdvcnksIG5hbWUpKSB7XG4gICAgICAgICAgICBjb3VudGVyKys7XG4gICAgICAgICAgICBuYW1lID0gYCR7YmFzZU5hbWV9XyR7Y291bnRlci50b1N0cmluZygpLnBhZFN0YXJ0KDMsICcwJyl9YDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIG5hbWU7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IEV2ZW50R3JvdXBEYXRhIHdpdGggZGVmYXVsdCB2YWx1ZXNcbiAgICAgKi9cbiAgICBwdWJsaWMgY3JlYXRlTmV3RXZlbnRHcm91cChjYXRlZ29yeTogRXZlbnRHcm91cENhdGVnb3J5LCBuYW1lPzogc3RyaW5nKTogRXZlbnRHcm91cERhdGEge1xuICAgICAgICBjb25zdCBmaW5hbE5hbWUgPSBuYW1lIHx8IHRoaXMuZ2VuZXJhdGVVbmlxdWVOYW1lKGNhdGVnb3J5KTtcblxuICAgICAgICB2YXIgZXZlbnRHcm91cDogRXZlbnRHcm91cERhdGEgPSB7XG4gICAgICAgICAgICBuYW1lOiBmaW5hbE5hbWUsXG4gICAgICAgICAgICB0cmlnZ2VyQ291bnQ6IDEsXG4gICAgICAgICAgICBjb25kaXRpb25zOiBbXSxcbiAgICAgICAgICAgIGFjdGlvbnM6IFtdXG4gICAgICAgIH07XG5cbiAgICAgICAgcmV0dXJuIGV2ZW50R3JvdXA7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogRHVwbGljYXRlIGFuIGV4aXN0aW5nIEV2ZW50R3JvdXBEYXRhXG4gICAgICovXG4gICAgcHVibGljIGR1cGxpY2F0ZUV2ZW50R3JvdXAoY2F0ZWdvcnk6IEV2ZW50R3JvdXBDYXRlZ29yeSwgb3JpZ2luYWxOYW1lOiBzdHJpbmcsIG5ld05hbWU/OiBzdHJpbmcpOiBFdmVudEdyb3VwRGF0YSB8IG51bGwge1xuICAgICAgICBjb25zdCBvcmlnaW5hbCA9IHRoaXMubG9hZEV2ZW50R3JvdXAoY2F0ZWdvcnksIG9yaWdpbmFsTmFtZSk7XG4gICAgICAgIGlmICghb3JpZ2luYWwpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZmluYWxOYW1lID0gbmV3TmFtZSB8fCB0aGlzLmdlbmVyYXRlVW5pcXVlTmFtZShjYXRlZ29yeSwgb3JpZ2luYWxOYW1lKTtcbiAgICAgICAgXG4gICAgICAgIC8vIERlZXAgY29weSB0aGUgb3JpZ2luYWwgZGF0YVxuICAgICAgICBjb25zdCBkdXBsaWNhdGU6IEV2ZW50R3JvdXBEYXRhID0ge1xuICAgICAgICAgICAgbmFtZTogZmluYWxOYW1lLFxuICAgICAgICAgICAgdHJpZ2dlckNvdW50OiBvcmlnaW5hbC50cmlnZ2VyQ291bnQsXG4gICAgICAgICAgICBjb25kaXRpb25zOiBvcmlnaW5hbC5jb25kaXRpb25zLm1hcChjb25kaXRpb24gPT4gKHsgLi4uY29uZGl0aW9uIH0pKSxcbiAgICAgICAgICAgIGFjdGlvbnM6IG9yaWdpbmFsLmFjdGlvbnMubWFwKGFjdGlvbiA9PiAoeyAuLi5hY3Rpb24gfSkpXG4gICAgICAgIH07XG5cbiAgICAgICAgcmV0dXJuIGR1cGxpY2F0ZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBHZXQgYWxsIEV2ZW50R3JvdXBEYXRhIG5hbWVzIGZyb20gYWxsIGNhdGVnb3JpZXNcbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0QWxsRXZlbnRHcm91cE5hbWVzKCk6IHsgW2NhdGVnb3J5OiBzdHJpbmddOiBzdHJpbmdbXSB9IHtcbiAgICAgICAgY29uc3QgcmVzdWx0OiB7IFtjYXRlZ29yeTogc3RyaW5nXTogc3RyaW5nW10gfSA9IHt9O1xuICAgICAgICBcbiAgICAgICAgZm9yIChjb25zdCBjYXRlZ29yeSBvZiBPYmplY3QudmFsdWVzKEV2ZW50R3JvdXBDYXRlZ29yeSkpIHtcbiAgICAgICAgICAgIGNvbnN0IGV2ZW50R3JvdXBzID0gdGhpcy5sb2FkRXZlbnRHcm91cHNCeUNhdGVnb3J5KGNhdGVnb3J5KTtcbiAgICAgICAgICAgIHJlc3VsdFtjYXRlZ29yeV0gPSBldmVudEdyb3Vwcy5tYXAoZWcgPT4gZWcubmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxufVxuIl19