import { IEventAction } from "./IEventAction";
import { eEmitterProp } from "../Emitter";
import { eEasing, Easing} from "../Easing";
import { EventGroupContext } from "../EventGroup";
import { EventActionData } from "../../data/bullet/EventGroupData";

export class BulletActionBase implements IEventAction {
    readonly data: EventActionData;

    protected _isCompleted: boolean = false;
    protected _elapsedTime: number = 0;
    protected _startValue: number = 0;

    constructor(data: EventActionData) {
        this.data = data;
    }

    isCompleted(): boolean {
        return this._isCompleted;
    }

    canLerp(): boolean {
        return true;
    }

    onLoad(context: EventGroupContext): void {
        this._isCompleted = false;
        this._elapsedTime = 0;
        // override this to get the correct start value
        this._startValue = 0;
    }

    onExecute(context: EventGroupContext, dt: number): void {
        this._elapsedTime += dt;
        if (this._elapsedTime >= this.data.duration) {
            this.executeInternal(context, this.data.targetValue);
            this._isCompleted = true;
        }
        else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));
        }
    }

    lerpValue(startValue: number, targetValue: number): number {
        return Easing.lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));
    }

    protected executeInternal(context: EventGroupContext, value: number): void {
        // Default implementation does nothing
    }
}

export class BulletAction_Duration extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.duration.value = value;
    }
}

export class BulletAction_ElapsedTime extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.elapsedTime = value;
    }
}

export class BulletAction_PosX extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        const position = context.bullet!.node.position;
        context.bullet!.node.setPosition(value, position.y);
    }
}

export class BulletAction_PosY extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        const position = context.bullet!.node.position;
        context.bullet!.node.setPosition(position.x, value);
    }
}

export class BulletAction_Speed extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.speed.value = value;
    }
}

export class BulletAction_SpeedAngle extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.speedAngle.value = value;
    }
}

export class BulletAction_Acceleration extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.acceleration.value = value;
    }
}

export class BulletAction_AccelerationAngle extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.accelerationAngle.value = value;
    }
}

export class BulletAction_Scale extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.node.scale.x = value;
    }
}

export class BulletAction_ColorR extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.bulletSprite.color.r = value;
    }
}

export class BulletAction_ColorG extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.bulletSprite.color.g = value;
    }
}

export class BulletAction_ColorB extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.bulletSprite.color.b = value;
    }
}

export class BulletAction_FacingMoveDir extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        context.bullet!.isFacingMoveDir.value = (value === 1);
    }
}

export class BulletAction_Destructive extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.isDestructive.value = (value === 1);
    }
}

export class BulletAction_DestructiveOnHit extends BulletActionBase {
    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.bullet!.isPreWarm.value = (value === 1);
    }
}