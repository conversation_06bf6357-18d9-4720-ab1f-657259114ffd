import { Emitter } from "./Emitter";
import { Bullet } from "./BulletController";
import { eEmitterCondition } from "../data/bullet/EventConditionType";
import { eEmitterActionType } from "../data/bullet/EventActionType";
import { IEventCondition } from "./conditions/IEventCondition";
import { IEventAction } from "./actions/IEventAction";
import { eConditionOp, EventGroupData, EventActionData, EventConditionData } from "../data/bullet/EventGroupData";
import * as cond from "./conditions/EmitterEventConditions";
import * as act from "./actions/EmitterEventActions";

// context for running condition & action
export class EventGroupContext {
    emitter: Emitter;
    bullet: Bullet;
    // TODO: add level 

    reset(): void {
        this.emitter = null;
        this.bullet = null;
    }
}

// Condition chain with operators
class ConditionChain {
    conditions: Array<IEventCondition> = [];

    evaluate(context: EventGroupContext): boolean {
        if (this.conditions.length === 0) return true;
        
        let result = this.conditions[0].evaluate(context);
        
        for (let i = 1; i < this.conditions.length; i++) {
            const condition = this.conditions[i];
            const conditionResult = condition.evaluate(context);
            
            if (condition.data.op === eConditionOp.And) {
                result = result && conditionResult;
            } else if (condition.data.op === eConditionOp.Or) {
                result = result || conditionResult;
            }
        }
        
        return result;
    }
}

// Updated EventGroup
enum eEventGroupStatus {
    Idle,       // not active
    Waiting,    // waiting for conditions to be met
    Active,     // conditions are met, now ticking actions
    Stopped     // stopped
}

export class EventGroup {
    readonly data: EventGroupData;

    context: EventGroupContext;
    conditionChain: ConditionChain;
    actions: IEventAction[];

    private _triggerCount: number = 0;
    private _status: eEventGroupStatus = eEventGroupStatus.Idle;
    get status(): eEventGroupStatus {
        return this._status;
    }
    
    constructor(ctx: EventGroupContext, data: EventGroupData) {
        this.context = ctx;
        this.data = data;
        this.conditionChain = this.buildConditionChain(data.conditions);
        this.actions = data.actions.map(actionData => {
            let action = ActionFactory.create(actionData);
            action.onLoad(this.context);
            return action;
        });

        this._status = eEventGroupStatus.Idle;
        this._triggerCount = 0;
    }

    private buildConditionChain(conditions: EventConditionData[]): ConditionChain {
        const chain = new ConditionChain();
        conditions.forEach((condData, index) => {
            const condition = ConditionFactory.create(condData);
            if (condition) {
                chain.conditions.push(condition);
            }
        });
        return chain;
    }

    canExecute(): boolean {
        return this.conditionChain.evaluate(this.context);
    }
    
    tick(dt: number): void {
        switch (this._status) {
            case eEventGroupStatus.Idle:
                // not active
                break;
            case eEventGroupStatus.Waiting:
                // waiting for conditions to be met
                if (this.canExecute()) {
                    // TODO: 考虑这里检测增加时间间隔来减少消耗
                    this._status = eEventGroupStatus.Active;
                }
                break;
            case eEventGroupStatus.Active:
                // conditions are met, now ticking actions
                this.tickActive(dt);
                break;
            case eEventGroupStatus.Stopped:
                // stopped
                break;
        }
    }

    private tickActive(dt: number): void {
        let isAllFinished = true;

        for (const action of this.actions) {
            if (action.isCompleted()) continue;
            action.onExecute(this.context, dt);
            isAllFinished = false;
        }

        if (isAllFinished) {
            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {
                // restart
                this._triggerCount++;
                this._status = eEventGroupStatus.Waiting;
            }
            else {
                this._status = eEventGroupStatus.Stopped;
            }
        }
    }
}

// Factory pattern for conditions & actions
class ConditionFactory {
    static create(data: EventConditionData): IEventCondition {
        switch (data.type) {
            case eEmitterCondition.Level_Duration:
                return null;
            case eEmitterCondition.Player_PosX:
                return null;
            case eEmitterCondition.Emitter_Active:
                return new cond.EmitterEventCondition_Active(data);
            case eEmitterCondition.Emitter_InitialDelay:
                return new cond.EmitterEventCondition_InitialDelay(data);
            case eEmitterCondition.Emitter_Prewarm:
                return new cond.EmitterEventCondition_Prewarm(data);
            // ... other cases
            default:
                throw new Error(`Unknown condition type: ${data.type}`);
        }
        return null;
    }
}

class ActionFactory {
    static create(data: EventActionData): IEventAction {
        // switch (data.type) {
        //     case eEmitterActionType.Emitter_Start:
        //         return new StartAction(data);
        //     case eEmitterActionType.Emitter_Stop:
        //         return new StopAction(data);
        //     // ... other cases
        //     default:
        //         throw new Error(`Unknown action type: ${data.type}`);
        // }
        return null;
    }
}
