{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletController.ts"], "names": ["_decorator", "Sprite", "EDITOR", "ObjectPool", "Movable", "BulletSystem", "EventGroup", "EventGroupContext", "PropertyContainerComponent", "ccclass", "property", "executeInEditMode", "eBulletProp", "BulletController", "type", "displayName", "bulletData", "isRunning", "elapsedTime", "emitter", "duration", "delayDestroy", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "acceleration", "accelerationAngle", "_eventGroups", "onLoad", "mover", "getComponent", "addComponent", "onBecomeInvisible", "onDestroyBullet", "onCollisionEnter", "other", "self", "onCreate", "resetProperties", "eventGroupData", "length", "ctx", "bullet", "dataName", "eventGroup", "loadEmitterEventGroup", "start", "push", "clear", "addProperty", "Duration", "DelayDestroy", "IsFacingMoveDir", "IsTrackingTarget", "Speed", "SpeedAngle", "Acceleration", "AccelerationAngle", "on", "value", "notifyAll", "tick", "dt", "destroySelf", "for<PERSON>ach", "group", "stop", "cb", "node", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "returnNode", "scheduleOnce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAmCC,MAAAA,M,OAAAA,M;;AACnCC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CX,U,GAEjD;;6BACYY,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;cAiBZ;AACA;AACA;AACA;;;kCAGaC,gB,WAFZJ,OAAO,CAAC,kBAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAIRL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEb,MAAP;AAAec,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,gBAPZJ,iB,qBADD,MAEaE,gBAFb;AAAA;AAAA,oEAE8E;AAAA;AAAA;;AAAA;;AAK1E;AAL0E;;AAS1E;AACA;AAV0E,eAW1EG,UAX0E;AAAA,eAanEC,SAbmE,GAa9C,KAb8C;AAAA,eAcnEC,WAdmE,GAc7C,CAd6C;AAAA,eAgBnEC,OAhBmE;AAkB1E;AACA;AACA;AApB0E,eAqBnEC,QArBmE;AAqBvB;AArBuB,eAsBnEC,YAtBmE;AAsBvB;AAtBuB,eAwBnEC,eAxBmE;AAwBvB;AAxBuB,eAyBnEC,gBAzBmE;AAyBvB;AAzBuB,eA0BnEC,KA1BmE;AA0BvB;AA1BuB,eA2BnEC,UA3BmE;AA2BvB;AA3BuB,eA4BnEC,YA5BmE;AA4BvB;AA5BuB,eA6BnEC,iBA7BmE;AA6BvB;AA7BuB,eA+BlEC,YA/BkE,GA+BrC,EA/BqC;AAAA;;AAiC1EC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AAAA;;AACb,iBAAKA,KAAL,yBAAa,KAAKC,YAAL;AAAA;AAAA,mCAAb,qBAAa,mBAA4BC,YAA5B;AAAA;AAAA,mCAAb;AACH;;AAED,eAAKF,KAAL,CAAWG,iBAAX,GAA+B,MAAM;AACjC;AAAA;AAAA,8CAAaC,eAAb,CAA6B,IAA7B;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,KAAD,EAAcC,IAAd,EAAgC;AAC5C;AACA;AACA;AAAA;AAAA,4CAAaH,eAAb,CAA6B,IAA7B;AACH;;AAEMI,QAAAA,QAAQ,CAACnB,OAAD,EAAyB;AACpC,eAAKF,SAAL,GAAiB,IAAjB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKH,UAAL,GAAkBG,OAAO,CAACH,UAA1B;AAEA,eAAKuB,eAAL;;AAEA,cAAI,KAAKvB,UAAL,IAAmB,KAAKA,UAAL,CAAgBwB,cAAhB,CAA+BC,MAA/B,GAAwC,CAA/D,EAAkE;AAC9D,gBAAIC,GAAG,GAAG;AAAA;AAAA,yDAAV;AACAA,YAAAA,GAAG,CAACvB,OAAJ,GAAc,KAAKA,OAAnB;AACAuB,YAAAA,GAAG,CAACC,MAAJ,GAAa,IAAb;;AACA,iBAAK,MAAMC,QAAX,IAAuB,KAAK5B,UAAL,CAAgBwB,cAAvC,EAAuD;AACnD,oBAAMK,UAAU,GAAG;AAAA;AAAA,4CAAeH,GAAf,EAAoB;AAAA;AAAA,gDAAaI,qBAAb,CAAmCF,QAAnC,CAApB,CAAnB;AACAC,cAAAA,UAAU,CAACE,KAAX;;AACA,mBAAKnB,YAAL,CAAkBoB,IAAlB,CAAuBH,UAAvB;AACH;AACJ;AACJ;;AAEMN,QAAAA,eAAe,GAAS;AAC3B,eAAKU,KAAL;AACA,cAAI,CAAC,KAAKjC,UAAV,EAAsB;AAEtB,eAAKI,QAAL,GAAgB,KAAK8B,WAAL,CAAiBtC,WAAW,CAACuC,QAA7B,EAAuC,KAAKnC,UAAL,CAAgBI,QAAvD,CAAhB;AACA,eAAKC,YAAL,GAAoB,KAAK6B,WAAL,CAAiBtC,WAAW,CAACwC,YAA7B,EAA2C,KAAKpC,UAAL,CAAgBK,YAA3D,CAApB,CAL2B,CAM3B;AACA;AACA;;AAEA,eAAKC,eAAL,GAAuB,KAAK4B,WAAL,CAAiBtC,WAAW,CAACyC,eAA7B,EAA8C,KAAKrC,UAAL,CAAgBM,eAA9D,CAAvB;AACA,eAAKC,gBAAL,GAAwB,KAAK2B,WAAL,CAAiBtC,WAAW,CAAC0C,gBAA7B,EAA+C,KAAKtC,UAAL,CAAgBO,gBAA/D,CAAxB;AACA,eAAKC,KAAL,GAAa,KAAK0B,WAAL,CAAiBtC,WAAW,CAAC2C,KAA7B,EAAoC,KAAKvC,UAAL,CAAgBQ,KAApD,CAAb;AACA,eAAKC,UAAL,GAAkB,KAAKyB,WAAL,CAAiBtC,WAAW,CAAC4C,UAA7B,EAAyC,CAAzC,CAAlB;AACA,eAAK9B,YAAL,GAAoB,KAAKwB,WAAL,CAAiBtC,WAAW,CAAC6C,YAA7B,EAA2C,KAAKzC,UAAL,CAAgBU,YAA3D,CAApB;AACA,eAAKC,iBAAL,GAAyB,KAAKuB,WAAL,CAAiBtC,WAAW,CAAC8C,iBAA7B,EAAgD,KAAK1C,UAAL,CAAgBW,iBAAhE,CAAzB,CAf2B,CAiB3B;;AACA,eAAKL,eAAL,CAAqBqC,EAArB,CAAyBC,KAAD,IAAW;AAC/B,iBAAK9B,KAAL,CAAWR,eAAX,GAA6BsC,KAA7B;AACH,WAFD;AAGA,eAAKrC,gBAAL,CAAsBoC,EAAtB,CAA0BC,KAAD,IAAW;AAChC,iBAAK9B,KAAL,CAAWP,gBAAX,GAA8BqC,KAA9B;AACH,WAFD;AAGA,eAAKpC,KAAL,CAAWmC,EAAX,CAAeC,KAAD,IAAW;AACrB,iBAAK9B,KAAL,CAAWN,KAAX,GAAmBoC,KAAnB;AACH,WAFD;AAGA,eAAKnC,UAAL,CAAgBkC,EAAhB,CAAoBC,KAAD,IAAW;AAC1B,iBAAK9B,KAAL,CAAWL,UAAX,GAAwBmC,KAAxB;AACH,WAFD;AAGA,eAAKlC,YAAL,CAAkBiC,EAAlB,CAAsBC,KAAD,IAAW;AAC5B,iBAAK9B,KAAL,CAAWJ,YAAX,GAA0BkC,KAA1B;AACH,WAFD;AAGA,eAAKjC,iBAAL,CAAuBgC,EAAvB,CAA2BC,KAAD,IAAW;AACjC,iBAAK9B,KAAL,CAAWH,iBAAX,GAA+BiC,KAA/B;AACH,WAFD;AAIA,eAAKC,SAAL,CAAe,IAAf;AACH;;AAEMC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAAA;;AAC1B,cAAI,CAAC,KAAK9C,SAAV,EAAqB;AAErB,eAAKC,WAAL,IAAoB6C,EAApB;;AACA,cAAI,KAAK7C,WAAL,GAAmB,KAAKE,QAAL,CAAcwC,KAArC,EAA4C;AACxC,iBAAKI,WAAL;AACA;AACH;;AAED,8BAAKlC,KAAL,yBAAYgC,IAAZ,CAAiBC,EAAjB;AACH;;AAEMC,QAAAA,WAAW,GAAS;AACvB,eAAK/C,SAAL,GAAiB,KAAjB;;AACA,eAAKW,YAAL,CAAkBqC,OAAlB,CAA0BC,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAnC,EAFuB,CAE2B;;;AAClD,eAAKvC,YAAL,GAAoB,EAApB,CAHuB,CAGC;;AACxB,gBAAMwC,EAAE,GAAG,MAAM;AACb,gBAAI,CAAC,KAAKC,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUC,OAA7B,EAAsC;;AAEtC,gBAAIpE,MAAJ,EAAY;AACR,mBAAKmE,IAAL,CAAUE,OAAV;AACH,aAFD,MAEO;AACH;AAAA;AAAA,4CAAWC,UAAX,CAAsB,KAAKH,IAA3B;AACH;AACJ,WARD;;AASA,cAAI,KAAKhD,YAAL,IAAqB,KAAKA,YAAL,CAAkBuC,KAAlB,GAA0B,CAAnD,EAAsD;AAClD,iBAAKa,YAAL,CAAkB,MAAM;AACpBL,cAAAA,EAAE;AACL,aAFD,EAEG,KAAK/C,YAAL,CAAkBuC,KAFrB;AAGH,WAJD,MAIO;AACHQ,YAAAA,EAAE;AACL;AACJ;;AAhJyE,O;;;;;;;;;;iBAO5C,I", "sourcesContent": ["import { _decorator, misc, Component, Node, Sprite, Color } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from './EventGroup';\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport { Emitter } from './Emitter';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n// 用枚举定义属性\r\nexport enum eBulletProp {\r\n    IsDestructive,\r\n    IsDestructiveOnHit,\r\n    Duration,\r\n    Damage,\r\n    DelayDestroy,\r\n\r\n    // 移动属性\r\n    IsFacingMoveDir,\r\n    IsTrackingTarget,\r\n    Speed,\r\n    SpeedAngle,\r\n    Acceleration,\r\n    AccelerationAngle,\r\n}\r\n\r\n\r\n// 子弹 Bullet 伤害计算 \r\n// Weapon -> 发射器, 喷火, 技能武器, 激光\r\n// WeaponSlot -> SetWeapon\r\n// 如何集成到项目里? 考虑把这个类改为BulletController, 控制移动等属性, 作为一个component加入到Bullet:Entity里去\r\n@ccclass('BulletController')\r\n@executeInEditMode\r\nexport class BulletController extends PropertyContainerComponent<eBulletProp> {\r\n\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover!: Movable;\r\n\r\n    // TODO: 这里后续不处理子弹的sprite显示\r\n    @property({type: Sprite, displayName: \"子弹精灵\"})\r\n    public bulletSprite: Sprite = null;\r\n\r\n    // 由发射器传进来\r\n    // @property({type: BulletData})\r\n    bulletData!: BulletData;\r\n\r\n    public isRunning: boolean = false;\r\n    public elapsedTime: number = 0;\r\n\r\n    public emitter!: Emitter;\r\n\r\n    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里\r\n    // public isDestructive : Property<boolean>;          // 是否可被破坏\r\n    // public isDestructiveOnHit : Property<boolean>;     // 命中时是否被销毁\r\n    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)\r\n    public delayDestroy!: Property<number>;            // 延迟销毁时间\r\n\r\n    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向\r\n    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标\r\n    public speed!: Property<number>;                   // 子弹速度\r\n    public speedAngle!: Property<number>;              // 子弹速度角度\r\n    public acceleration!: Property<number>;            // 子弹加速度\r\n    public accelerationAngle!: Property<number>;       // 子弹加速度角度\r\n\r\n    private _eventGroups: EventGroup[] = [];\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;\r\n        }\r\n\r\n        this.mover.onBecomeInvisible = () => {\r\n            BulletSystem.onDestroyBullet(this);\r\n        };\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞, 这里要相应进行替换\r\n     */\r\n    onCollisionEnter(other: Node, self: Node): void {\r\n        // 判断另一个node也是子弹或者非子弹, 进行相应处理\r\n        // 根据this.isDestructive 和 this.isDestructiveOnHit\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n    \r\n    public onCreate(emitter: Emitter): void {\r\n        this.isRunning = true;\r\n        this.elapsedTime = 0;\r\n        this.emitter = emitter;\r\n        this.bulletData = emitter.bulletData;\r\n\r\n        this.resetProperties();\r\n\r\n        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {\r\n            let ctx = new EventGroupContext();\r\n            ctx.emitter = this.emitter;\r\n            ctx.bullet = this;\r\n            for (const dataName of this.bulletData.eventGroupData) {\r\n                const eventGroup = new EventGroup(ctx, BulletSystem.loadEmitterEventGroup(dataName));\r\n                eventGroup.start();\r\n                this._eventGroups.push(eventGroup);\r\n            }\r\n        }\r\n    }\r\n\r\n    public resetProperties(): void {\r\n        this.clear();\r\n        if (!this.bulletData) return;\r\n\r\n        this.duration = this.addProperty(eBulletProp.Duration, this.bulletData.duration);\r\n        this.delayDestroy = this.addProperty(eBulletProp.DelayDestroy, this.bulletData.delayDestroy);\r\n        // this.isDestructive = this.addProperty(eBulletProp.IsDestructive, this.bulletData.isDestructive);\r\n        // this.isDestructiveOnHit = this.addProperty(eBulletProp.IsDestructiveOnHit, this.bulletData.isDestructiveOnHit);\r\n        // this.damage = this.addProperty(eBulletProp.Damage, this.bulletData.damage);\r\n\r\n        this.isFacingMoveDir = this.addProperty(eBulletProp.IsFacingMoveDir, this.bulletData.isFacingMoveDir);\r\n        this.isTrackingTarget = this.addProperty(eBulletProp.IsTrackingTarget, this.bulletData.isTrackingTarget);\r\n        this.speed = this.addProperty(eBulletProp.Speed, this.bulletData.speed);\r\n        this.speedAngle = this.addProperty(eBulletProp.SpeedAngle, 0);\r\n        this.acceleration = this.addProperty(eBulletProp.Acceleration, this.bulletData.acceleration);\r\n        this.accelerationAngle = this.addProperty(eBulletProp.AccelerationAngle, this.bulletData.accelerationAngle);\r\n\r\n        // listen to property changes\r\n        this.isFacingMoveDir.on((value) => {\r\n            this.mover.isFacingMoveDir = value;\r\n        });\r\n        this.isTrackingTarget.on((value) => {\r\n            this.mover.isTrackingTarget = value;\r\n        });\r\n        this.speed.on((value) => {\r\n            this.mover.speed = value;\r\n        });\r\n        this.speedAngle.on((value) => {\r\n            this.mover.speedAngle = value;\r\n        });\r\n        this.acceleration.on((value) => {\r\n            this.mover.acceleration = value;\r\n        });\r\n        this.accelerationAngle.on((value) => {\r\n            this.mover.accelerationAngle = value;\r\n        });\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    public tick(dt:number) : void {\r\n        if (!this.isRunning) return;\r\n\r\n        this.elapsedTime += dt;\r\n        if (this.elapsedTime > this.duration.value) {\r\n            this.destroySelf();\r\n            return;\r\n        }\r\n\r\n        this.mover?.tick(dt);\r\n    }\r\n\r\n    public destroySelf(): void {\r\n        this.isRunning = false;\r\n        this._eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.\r\n        this._eventGroups = []; // clear the event groups array\r\n        const cb = () => {\r\n            if (!this.node || !this.node.isValid) return;\r\n            \r\n            if (EDITOR) {\r\n                this.node.destroy();\r\n            } else {\r\n                ObjectPool.returnNode(this.node);\r\n            }\r\n        };\r\n        if (this.delayDestroy && this.delayDestroy.value > 0) {\r\n            this.scheduleOnce(() => {\r\n                cb();\r\n            }, this.delayDestroy.value);\r\n        } else {\r\n            cb();\r\n        }\r\n    }\r\n}\r\n"]}