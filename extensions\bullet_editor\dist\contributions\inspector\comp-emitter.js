'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.style = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const fs_1 = require("fs");
const path_1 = require("path");
const { dialog } = require('electron');
exports.template = `
<ui-section expand>
  <ui-prop slot="header">
    <ui-label slot="label">发射器属性</ui-label>
  </ui-prop>
    <ui-prop type="dump" class="isOnlyInScreen"></ui-prop>
    <ui-prop type="dump" class="isPreWarm"></ui-prop>
    <ui-prop type="dump" class="isLoop"></ui-prop>
    <ui-prop type="dump" class="initialDelay"></ui-prop>
    <ui-prop type="dump" class="preWarmDuration"></ui-prop>
    <ui-prop type="dump" class="preWarmEffect"></ui-prop>
    <ui-prop type="dump" class="emitDuration"></ui-prop>
    <ui-prop type="dump" class="emitInterval"></ui-prop>
    <ui-prop type="dump" class="emitPower"></ui-prop>
    <ui-prop type="dump" class="loopInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitCount"></ui-prop>
    <ui-prop type="dump" class="perEmitInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitOffsetX"></ui-prop>
    <ui-prop type="dump" class="angle"></ui-prop>
    <ui-prop type="dump" class="count"></ui-prop>
    <ui-prop type="dump" class="arc"></ui-prop>
    <ui-prop type="dump" class="radius"></ui-prop>
    <ui-prop type="dump" class="emitEffect"></ui-prop>

    <!-- 自定义 eventGroupData -->
    <div class="eventGroupData"></div>
</ui-section>

<!-- 额外的 bullet 相关 -->
<ui-prop type="dump" class="bulletID"></ui-prop>
<ui-prop type="dump" class="bulletData"></ui-prop>
`;
exports.style = `
.eventGroupData {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  align-items: flex-start; /* avoid children stretching full width */
  width: 100%;
}
.eventGroupItem {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 2px;
  width: 100%;
}
.eventGroupItem ui-select {
  flex: 1;
  min-width: 200px;
  max-width: 90%;
}
.eventGroupItem button {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
}
.eventGroupItem input {
  flex: 1;
  padding: 2px 4px;
  border: 1px solid #555;
  border-radius: 2px;
  background: #1a1a1a;
  color: white;
}
.eventGroupItem.invalid input {
  border-color: #e74c3c;
}
.eventGroupItem.valid input {
  border-color: #2ecc71;
}
.addEventGroupBtn {
  align-self: flex-start;
}
/* Compact icon button used inside eventGroupItem */
.eventGroupItem .icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  padding: 0;
  border: none;
  border-radius: 2px;
}

.eventGroupItem .icon-btn[title="编辑"] { background: #4a90e2; }
.eventGroupItem .icon-btn[title="删除"] { background: #e74c3c; }
`;
exports.$ = {
    isOnlyInScreen: '.isOnlyInScreen',
    isPreWarm: '.isPreWarm',
    isLoop: '.isLoop',
    initialDelay: '.initialDelay',
    preWarmDuration: '.preWarmDuration',
    preWarmEffect: '.preWarmEffect',
    emitDuration: '.emitDuration',
    emitInterval: '.emitInterval',
    emitPower: '.emitPower',
    loopInterval: '.loopInterval',
    perEmitCount: '.perEmitCount',
    perEmitInterval: '.perEmitInterval',
    perEmitOffsetX: '.perEmitOffsetX',
    angle: '.angle',
    count: '.count',
    arc: '.arc',
    radius: '.radius',
    emitEffect: '.emitEffect',
    eventGroupData: '.eventGroupData',
    bulletID: '.bulletID',
    bulletData: '.bulletData',
};
const root_path = 'assets/resources/Game/emitter/events/';
function checkEventGroupFileExists(category, eventGroupName) {
    if (!eventGroupName)
        return false;
    const projectPath = Editor.Project.path;
    const filePath = (0, path_1.join)(projectPath, root_path, category, `${eventGroupName}.json`);
    return (0, fs_1.existsSync)(filePath);
}
async function listEventNames(category) {
    const pattern = (0, path_1.join)('db://', root_path, category, '**/*.json').replace(/\\/g, '/');
    try {
        const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        // res can be an array of asset infos (or nested in an array in some editor versions)
        const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
        const names = arr
            .filter((a) => a && !a.isDirectory)
            .map((a) => String(a.name || '').replace(/\.json$/i, ''))
            .filter(Boolean)
            .sort();
        return names;
    }
    catch (e) {
        console.warn('listEventNames failed', e);
        return [];
    }
}
function renderEventGroupDataArray(container, category, dump) {
    const build = (names) => {
        container.innerHTML = '';
        // 创建数组容器
        const arrayContainer = document.createElement('div');
        arrayContainer.className = 'eventGroupData';
        arrayContainer.title = '事件组';
        arrayContainer.style.marginLeft = '10px';
        dump.value.forEach((item, index) => {
            const itemContainer = document.createElement('div');
            itemContainer.className = 'eventGroupItem';
            const sel = document.createElement('ui-select');
            (names.length ? names : ['']).forEach(n => {
                const o = document.createElement('option');
                o.value = n;
                o.innerText = n;
                sel.appendChild(o);
            });
            sel.value = item || (names[0] || ''); // 取出当前项的值，如果没有则取第一个值作为默认值
            sel.addEventListener('change', () => {
                dump.value[index] = sel.value; // 设置当前项的值
                renderEventGroupDataArray(container, category, dump); // 重新渲染
            });
            const btnOpen = document.createElement('button');
            btnOpen.setAttribute('title', '编辑');
            btnOpen.classList.add('icon-btn');
            const icon = document.createElement('ui-icon');
            icon.value = 'edit';
            btnOpen.appendChild(icon);
            btnOpen.onclick = () => Editor.Message.send('bullet_editor', 'open-event-editor', sel.value);
            // Per-item remove button with trash icon
            const btnRemove = document.createElement('button');
            btnRemove.setAttribute('title', '删除');
            btnRemove.classList.add('icon-btn');
            const trash = document.createElement('ui-icon');
            trash.value = 'remove'; // fallback to 'delete' if 'trash' not available
            btnRemove.appendChild(trash);
            btnRemove.onclick = () => {
                dump.value.splice(index, 1);
                renderEventGroupDataArray(container, category, dump);
            };
            itemContainer.appendChild(sel);
            itemContainer.appendChild(btnOpen);
            itemContainer.appendChild(btnRemove);
            arrayContainer.appendChild(itemContainer);
        });
        container.appendChild(arrayContainer);
        // 添加按钮
        const btnAdd = document.createElement('button');
        btnAdd.textContent = '+ 添加事件组';
        btnAdd.className = 'addEventGroupBtn';
        btnAdd.style.cssText = 'background:#2ecc71;color:#fff;border:none;border-radius:2px;padding:2px 6px;margin-top:4px;align-self:flex-start;';
        btnAdd.onclick = () => {
            dump.value.push(names[0] || ''); // 默认值
            renderEventGroupDataArray(container, category, dump);
        };
        container.appendChild(btnAdd);
    };
    listEventNames(category).then(build).catch(() => build([]));
}
function update(dump) {
    this.dump = dump;
    const emitterData = dump.value.emitterData;
    // 普通字段直接走 render
    this.$.isOnlyInScreen.render(emitterData.value.isOnlyInScreen);
    this.$.isPreWarm.render(emitterData.value.isPreWarm);
    this.$.isLoop.render(emitterData.value.isLoop);
    this.$.initialDelay.render(emitterData.value.initialDelay);
    this.$.preWarmDuration.render(emitterData.value.preWarmDuration);
    this.$.preWarmEffect.render(emitterData.value.preWarmEffect);
    this.$.emitDuration.render(emitterData.value.emitDuration);
    this.$.emitInterval.render(emitterData.value.emitInterval);
    this.$.emitPower.render(emitterData.value.emitPower);
    this.$.loopInterval.render(emitterData.value.loopInterval);
    this.$.perEmitCount.render(emitterData.value.perEmitCount);
    this.$.perEmitInterval.render(emitterData.value.perEmitInterval);
    this.$.perEmitOffsetX.render(emitterData.value.perEmitOffsetX);
    this.$.angle.render(emitterData.value.angle);
    this.$.count.render(emitterData.value.count);
    this.$.arc.render(emitterData.value.arc);
    this.$.radius.render(emitterData.value.radius);
    this.$.emitEffect.render(emitterData.value.emitEffect);
    renderEventGroupDataArray(this.$.eventGroupData, "Emitter", emitterData.value.eventGroupData);
    // bullet 相关
    this.$.bulletID.render(dump.value.bulletID);
    this.$.bulletData.render(dump.value.bulletData);
}
function ready() { }
//# sourceMappingURL=data:application/json;base64,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