{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "resources", "JsonAsset", "path", "eEventGroupStatus", "EventGroupData", "ccclass", "join", "tick", "dt", "tickEmitters", "tickBullets", "tickEventGroups", "emitter", "allEmitters", "bullet", "allBullets", "i", "allEventGroups", "length", "group", "status", "Stopped", "splice", "onCreateEmitter", "push", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "foundNode", "console", "warn", "node", "onDestroyEmitter", "filter", "e", "onCreateBullet", "onCreate", "setParent", "onDestroyBullet", "destroySelf", "b", "destroyAllBullets", "loadEmitterEventGroup", "name", "finalPath", "emitterEventGroupPath", "json", "load", "data", "fromJSON", "loadBulletEventGroup", "bulletEventGroupPath", "onCreateEventGroup", "eventGroup", "onDestroyEventGroup", "g"], "mappings": ";;;gKAYaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAkBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAGxCC,MAAAA,iB,iBAAAA,iB;;AACZC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcP,U;OACd;AAAEQ,QAAAA;AAAF,O,GAAWJ,I;AAEjB;AACA;AACA;AACA;;8BACaL,Y,GAAN,MAAMA,YAAN,CAAmB;AAwBtB;AACJ;AACA;AACsB,eAAJU,IAAI,CAACC,EAAD,EAAa;AAC3B,eAAKC,YAAL,CAAkBD,EAAlB;AACA,eAAKE,WAAL,CAAiBF,EAAjB;AACA,eAAKG,eAAL,CAAqBH,EAArB;AACH;;AAEyB,eAAZC,YAAY,CAACD,EAAD,EAAY;AAClC,eAAK,IAAMI,OAAX,IAAsB,KAAKC,WAA3B,EAAwC;AACpCD,YAAAA,OAAO,CAACL,IAAR,CAAaC,EAAb;AACH;AACJ;;AAEwB,eAAXE,WAAW,CAACF,EAAD,EAAY;AACjC,eAAK,IAAMM,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACP,IAAP,CAAYC,EAAZ;AACH;AACJ;;AAE4B,eAAfG,eAAe,CAACH,EAAD,EAAa;AACtC,eAAK,IAAIQ,CAAC,GAAG,KAAKC,cAAL,CAAoBC,MAApB,GAA6B,CAA1C,EAA6CF,CAAC,IAAI,CAAlD,EAAqDA,CAAC,EAAtD,EAA0D;AACtD,gBAAMG,KAAK,GAAG,KAAKF,cAAL,CAAoBD,CAApB,CAAd;AACAG,YAAAA,KAAK,CAACZ,IAAN,CAAWC,EAAX;;AACA,gBAAIW,KAAK,CAACC,MAAN,KAAiB;AAAA;AAAA,wDAAkBC,OAAvC,EAAgD;AAC5C,mBAAKJ,cAAL,CAAoBK,MAApB,CAA2BN,CAA3B,EAA8B,CAA9B;AACH;AACJ;AACJ;;AAE4B,eAAfO,eAAe,CAACX,OAAD,EAAkB;AAC3C,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,WAAL,CAAiBK,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKH,WAAL,CAAiBG,CAAjB,MAAwBJ,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKC,WAAL,CAAiBW,IAAjB,CAAsBZ,OAAtB;;AAEA,cAAI,CAAC,KAAKa,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBT,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,kBAAMU,SAAS,GAAG7B,IAAI,CAAC,KAAK4B,gBAAN,CAAtB;;AACA,kBAAIC,SAAJ,EAAe;AACX,qBAAKH,YAAL,GAAoBG,SAApB;AACH,eAFD,MAEO;AACHC,gBAAAA,OAAO,CAACC,IAAR,CAAa,oBAAoB,KAAKH,gBAAtC;AACA,qBAAKF,YAAL,GAAoBb,OAAO,CAACmB,IAA5B;AACH;AACJ;AACJ;AACJ;;AAE6B,eAAhBC,gBAAgB,CAACpB,OAAD,EAAkB;AAC5C,eAAKC,WAAL,GAAmB,KAAKA,WAAL,CAAiBoB,MAAjB,CAAwBC,CAAC,IAAIA,CAAC,KAAKtB,OAAnC,CAAnB;AACH;;AAE2B,eAAduB,cAAc,CAACvB,OAAD,EAAmBE,MAAnB,EAA6C;AACrE;AACA;AACA;AACA;AACA;AACA;AAEAA,UAAAA,MAAM,CAACsB,QAAP,CAAgBxB,OAAhB;AACA,eAAKG,UAAL,CAAgBS,IAAhB,CAAqBV,MAArB;AACAA,UAAAA,MAAM,CAACiB,IAAP,CAAYM,SAAZ,CAAsB,KAAKZ,YAA3B,EAAyC,IAAzC;AACH;;AAE4B,eAAfa,eAAe,CAACxB,MAAD,EAA2B;AACpDA,UAAAA,MAAM,CAACyB,WAAP;AACA,eAAKxB,UAAL,GAAkB,KAAKA,UAAL,CAAgBkB,MAAhB,CAAuBO,CAAC,IAAIA,CAAC,KAAK1B,MAAlC,CAAlB;AACH;;AAE8B,eAAjB2B,iBAAiB,GAAG;AAC9B,eAAK,IAAM3B,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACyB,WAAP;AACH;;AACD,eAAKxB,UAAL,GAAkB,EAAlB;AACH;;AAEkC,eAArB2B,qBAAqB,CAACC,IAAD,EAA+B;AAC9D;AACA,cAAIC,SAAS,GAAGtC,IAAI,CAAC,KAAKuC,qBAAN,EAA6BF,IAAI,GAAG,OAApC,CAApB;AACA,cAAIG,IAAI,GAAG9C,SAAS,CAAC+C,IAAV,CAAeH,SAAf,EAA0B3C,SAA1B,CAAX;AACA,cAAI+C,IAAI,GAAG;AAAA;AAAA,gDAAeC,QAAf,CAAwBH,IAAxB,CAAX;AACA,iBAAOE,IAAP;AACH;;AAEiC,eAApBE,oBAAoB,CAACP,IAAD,EAA+B;AAC7D;AACA,cAAIC,SAAS,GAAGtC,IAAI,CAAC,KAAK6C,oBAAN,EAA4BR,IAAI,GAAG,OAAnC,CAApB;AACA,cAAIG,IAAI,GAAG9C,SAAS,CAAC+C,IAAV,CAAeH,SAAf,EAA0B3C,SAA1B,CAAX;AACA,cAAI+C,IAAI,GAAG;AAAA;AAAA,gDAAeC,QAAf,CAAwBH,IAAxB,CAAX;AACA,iBAAOE,IAAP;AACH;AAED;AACJ;AACA;;;AACoC,eAAlBI,kBAAkB,CAACC,UAAD,EAAyB;AACrD,eAAKpC,cAAL,CAAoBO,IAApB,CAAyB6B,UAAzB;AACH;;AAEgC,eAAnBC,mBAAmB,CAACD,UAAD,EAAyB;AACtD,eAAKpC,cAAL,GAAsB,KAAKA,cAAL,CAAoBgB,MAApB,CAA2BsB,CAAC,IAAIA,CAAC,KAAKF,UAAtC,CAAtB;AACH;;AAnIqB,O;;AAAbxD,MAAAA,Y,CACc8B,gB,GAA2B,2B;AADzC9B,MAAAA,Y,CAEcgD,qB,GAAgC,wB;AAF9ChD,MAAAA,Y,CAGcsD,oB,GAA+B,uB;;AAEtD;AACJ;AACA;AAPatD,MAAAA,Y,CAQKkB,U,GAAiC,E;;AAE/C;AACJ;AACA;AAZalB,MAAAA,Y,CAaKgB,W,GAAyB,E;;AAEvC;AACJ;AACA;AAjBahB,MAAAA,Y,CAkBKoB,c,GAA+B,E;AAE7C;AACA;AArBSpB,MAAAA,Y,CAsBK4B,Y", "sourcesContent": ["import { _decorator, find, Vec3, Node, resources, JsonAsset, path } from \"cc\";\nimport { BulletController } from \"./BulletController\";\nimport { Emitter } from \"./Emitter\";\nimport { EventGroup, eEventGroupStatus } from \"./EventGroup\";\nimport { EventGroupData } from \"../data/bullet/EventGroupData\";\nconst { ccclass } = _decorator;\nconst { join } = path;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n    public static readonly bulletParentPath: string = 'Canvas/GameUI/bullet_root';\n    public static readonly emitterEventGroupPath: string = 'emitter/events/Emitter';\n    public static readonly bulletEventGroupPath: string = 'emitter/events/Bullet';\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: BulletController[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    /**\n     * All active action groups\n     */\n    public static allEventGroups: EventGroup[] = [];\n\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node;\n\n    /**\n     * Main update loop\n     */\n    public static tick(dt: number) {\n        this.tickEmitters(dt);\n        this.tickBullets(dt);\n        this.tickEventGroups(dt);\n    }\n\n    public static tickEmitters(dt:number) {\n        for (const emitter of this.allEmitters) {\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (const bullet of this.allBullets) {\n            bullet.tick(dt);\n        }\n    }\n\n    public static tickEventGroups(dt: number) {\n        for (let i = this.allEventGroups.length - 1; i >= 0; i--) {\n            const group = this.allEventGroups[i];\n            group.tick(dt);\n            if (group.status === eEventGroupStatus.Stopped) {\n                this.allEventGroups.splice(i, 1);\n            }\n        }\n    }\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                const foundNode = find(this.bulletParentPath);\n                if (foundNode) {\n                    this.bulletParent = foundNode;\n                } else {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                    this.bulletParent = emitter.node;\n                }\n            }\n        }\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        this.allEmitters = this.allEmitters.filter(e => e !== emitter);\n    }\n\n    public static onCreateBullet(emitter: Emitter, bullet: BulletController) {\n        // 这个检查是否会比较冗余\n        // for (let i = 0; i < this.allBullets.length; i++) {\n        //     if (this.allBullets[i] === bullet) {\n        //         return;\n        //     }\n        // }\n\n        bullet.onCreate(emitter);\n        this.allBullets.push(bullet);\n        bullet.node.setParent(this.bulletParent, true);\n    }\n\n    public static onDestroyBullet(bullet: BulletController) {\n        bullet.destroySelf();\n        this.allBullets = this.allBullets.filter(b => b !== bullet);\n    }\n\n    public static destroyAllBullets() {\n        for (const bullet of this.allBullets) {\n            bullet.destroySelf();\n        }\n        this.allBullets = [];\n    }\n\n    public static loadEmitterEventGroup(name: string): EventGroupData {\n        // the name is the json file name\n        let finalPath = join(this.emitterEventGroupPath, name + '.json');\n        let json = resources.load(finalPath, JsonAsset);\n        let data = EventGroupData.fromJSON(json);\n        return data;\n    }\n\n    public static loadBulletEventGroup(name: string): EventGroupData {\n        // the name is the json file name\n        let finalPath = join(this.bulletEventGroupPath, name + '.json');\n        let json = resources.load(finalPath, JsonAsset);\n        let data = EventGroupData.fromJSON(json);\n        return data;\n    }\n\n    /**\n     * Called when a new event group is created or turn active\n     */\n    public static onCreateEventGroup(eventGroup: EventGroup) {\n        this.allEventGroups.push(eventGroup);\n    }\n\n    public static onDestroyEventGroup(eventGroup: EventGroup) {\n        this.allEventGroups = this.allEventGroups.filter(g => g !== eventGroup);\n    }\n}"]}