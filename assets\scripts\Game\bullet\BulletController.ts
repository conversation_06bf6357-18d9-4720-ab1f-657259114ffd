import { _decorator, misc, Component, Node, Sprite, Color } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from './EventGroup';
import { Property, PropertyContainerComponent } from './PropertyContainer';
import { Emitter } from './Emitter';
const { ccclass, property, executeInEditMode } = _decorator;

// 用枚举定义属性
export enum eBulletProp {
    IsDestructive,
    IsDestructiveOnHit,
    Duration,
    Damage,
    DelayDestroy,

    // 移动属性
    IsFacingMoveDir,
    IsTrackingTarget,
    Speed,
    SpeedAngle,
    Acceleration,
    AccelerationAngle,
}


// 子弹 Bullet 伤害计算 
// Weapon -> 发射器, 喷火, 技能武器, 激光
// WeaponSlot -> SetWeapon
// 如何集成到项目里? 考虑把这个类改为BulletController, 控制移动等属性, 作为一个component加入到Bullet:Entity里去
@ccclass('BulletController')
@executeInEditMode
export class BulletController extends PropertyContainerComponent<eBulletProp> {

    @property({type: Movable, displayName: "移动组件"})
    public mover!: Movable;

    // TODO: 这里后续不处理子弹的sprite显示
    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite = null;

    // 由发射器传进来
    // @property({type: BulletData})
    bulletData!: BulletData;

    public isRunning: boolean = false;
    public elapsedTime: number = 0;

    public emitter!: Emitter;

    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    // public isDestructive : Property<boolean>;          // 是否可被破坏
    // public isDestructiveOnHit : Property<boolean>;     // 命中时是否被销毁
    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)
    public delayDestroy!: Property<number>;            // 延迟销毁时间

    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向
    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标
    public speed!: Property<number>;                   // 子弹速度
    public speedAngle!: Property<number>;              // 子弹速度角度
    public acceleration!: Property<number>;            // 子弹加速度
    public accelerationAngle!: Property<number>;       // 子弹加速度角度

    private _eventGroups: EventGroup[] = [];

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;
        }

        this.mover.onBecomeInvisible = () => {
            BulletSystem.onDestroyBullet(this);
        };
    }

    /**
     * TODO: 如果后续自己写碰撞, 这里要相应进行替换
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
        // 根据this.isDestructive 和 this.isDestructiveOnHit
        BulletSystem.onDestroyBullet(this);
    }
    
    public onCreate(emitter: Emitter): void {
        this.isRunning = true;
        this.elapsedTime = 0;
        this.emitter = emitter;
        this.bulletData = emitter.bulletData;

        this.resetProperties();

        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            let ctx = new EventGroupContext();
            ctx.emitter = this.emitter;
            ctx.bullet = this;
            for (const dataName of this.bulletData.eventGroupData) {
                const eventGroup = new EventGroup(ctx, BulletSystem.loadEmitterEventGroup(dataName));
                eventGroup.start();
                this._eventGroups.push(eventGroup);
            }
        }
    }

    public resetProperties(): void {
        this.clear();
        if (!this.bulletData) return;

        this.duration = this.addProperty(eBulletProp.Duration, this.bulletData.duration);
        this.delayDestroy = this.addProperty(eBulletProp.DelayDestroy, this.bulletData.delayDestroy);
        // this.isDestructive = this.addProperty(eBulletProp.IsDestructive, this.bulletData.isDestructive);
        // this.isDestructiveOnHit = this.addProperty(eBulletProp.IsDestructiveOnHit, this.bulletData.isDestructiveOnHit);
        // this.damage = this.addProperty(eBulletProp.Damage, this.bulletData.damage);

        this.isFacingMoveDir = this.addProperty(eBulletProp.IsFacingMoveDir, this.bulletData.isFacingMoveDir);
        this.isTrackingTarget = this.addProperty(eBulletProp.IsTrackingTarget, this.bulletData.isTrackingTarget);
        this.speed = this.addProperty(eBulletProp.Speed, this.bulletData.speed);
        this.speedAngle = this.addProperty(eBulletProp.SpeedAngle, 0);
        this.acceleration = this.addProperty(eBulletProp.Acceleration, this.bulletData.acceleration);
        this.accelerationAngle = this.addProperty(eBulletProp.AccelerationAngle, this.bulletData.accelerationAngle);

        // listen to property changes
        this.isFacingMoveDir.on((value) => {
            this.mover.isFacingMoveDir = value;
        });
        this.isTrackingTarget.on((value) => {
            this.mover.isTrackingTarget = value;
        });
        this.speed.on((value) => {
            this.mover.speed = value;
        });
        this.speedAngle.on((value) => {
            this.mover.speedAngle = value;
        });
        this.acceleration.on((value) => {
            this.mover.acceleration = value;
        });
        this.accelerationAngle.on((value) => {
            this.mover.accelerationAngle = value;
        });

        this.notifyAll(true);
    }

    public tick(dt:number) : void {
        if (!this.isRunning) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.duration.value) {
            this.destroySelf();
            return;
        }

        this.mover?.tick(dt);
    }

    public destroySelf(): void {
        this.isRunning = false;
        this._eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.
        this._eventGroups = []; // clear the event groups array
        const cb = () => {
            if (!this.node || !this.node.isValid) return;
            
            if (EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.returnNode(this.node);
            }
        };
        if (this.delayDestroy && this.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
                cb();
            }, this.delayDestroy.value);
        } else {
            cb();
        }
    }
}
