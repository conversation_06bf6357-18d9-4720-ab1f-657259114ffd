System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, eEmitterCondition, eBulletActionType, eConditionOp, eCompareOp, emitter_cond, bullet_act, BulletSystem, EventGroupContext, ConditionChain, EventGroup, Comparer, ConditionFactory, ActionFactory, _crd, eEventGroupStatus;

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletController(extras) {
    _reporterNs.report("BulletController", "./BulletController", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterCondition(extras) {
    _reporterNs.report("eEmitterCondition", "../data/bullet/EventConditionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletActionType(extras) {
    _reporterNs.report("eBulletActionType", "../data/bullet/EventActionType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "./conditions/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "./actions/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionOp(extras) {
    _reporterNs.report("eConditionOp", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionData(extras) {
    _reporterNs.report("EventActionData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventConditionData(extras) {
    _reporterNs.report("EventConditionData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  _export({
    EventGroupContext: void 0,
    EventGroup: void 0,
    Comparer: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      eEmitterCondition = _unresolved_2.eEmitterCondition;
    }, function (_unresolved_3) {
      eBulletActionType = _unresolved_3.eBulletActionType;
    }, function (_unresolved_4) {
      eConditionOp = _unresolved_4.eConditionOp;
      eCompareOp = _unresolved_4.eCompareOp;
    }, function (_unresolved_5) {
      emitter_cond = _unresolved_5;
    }, function (_unresolved_6) {
      bullet_act = _unresolved_6;
    }, function (_unresolved_7) {
      BulletSystem = _unresolved_7.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "269cc0mLCdIDJz1bc2aNgzv", "EventGroup", undefined);

      // context for running condition & action
      _export("EventGroupContext", EventGroupContext = class EventGroupContext {
        constructor() {
          this.emitter = null;
          this.bullet = null;
        }

        // TODO: add level 
        reset() {
          this.emitter = null;
          this.bullet = null;
        }

      }); // Condition chain with operators


      ConditionChain = class ConditionChain {
        constructor() {
          this.conditions = [];
        }

        evaluate(context) {
          if (this.conditions.length === 0) return true;
          var result = this.conditions[0].evaluate(context);

          for (var i = 1; i < this.conditions.length; i++) {
            var condition = this.conditions[i];
            var conditionResult = condition.evaluate(context);

            if (condition.data.op === (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
              error: Error()
            }), eConditionOp) : eConditionOp).And) {
              result = result && conditionResult;
            } else if (condition.data.op === (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
              error: Error()
            }), eConditionOp) : eConditionOp).Or) {
              result = result || conditionResult;
            }
          }

          return result;
        }

      }; // Updated EventGroup

      _export("eEventGroupStatus", eEventGroupStatus = /*#__PURE__*/function (eEventGroupStatus) {
        eEventGroupStatus[eEventGroupStatus["Idle"] = 0] = "Idle";
        eEventGroupStatus[eEventGroupStatus["Waiting"] = 1] = "Waiting";
        eEventGroupStatus[eEventGroupStatus["Active"] = 2] = "Active";
        eEventGroupStatus[eEventGroupStatus["Stopped"] = 3] = "Stopped";
        return eEventGroupStatus;
      }({}));

      _export("EventGroup", EventGroup = class EventGroup {
        get status() {
          return this._status;
        }

        constructor(ctx, data) {
          this.data = void 0;
          this.context = void 0;
          this.conditionChain = void 0;
          this.actions = void 0;
          this._triggerCount = 0;
          this._status = eEventGroupStatus.Idle;
          this.context = ctx;
          this.data = data;
          this.conditionChain = this.buildConditionChain(data.conditions);
          this.actions = data.actions.map(actionData => {
            var action = ActionFactory.create(actionData);
            action.onLoad(this.context);
            return action;
          });
          this.changeStatus(eEventGroupStatus.Idle);
          this._triggerCount = 0;
        }

        start() {
          this.changeStatus(eEventGroupStatus.Waiting);
        }

        stop() {
          // both stop and idle will do the trick
          this.changeStatus(eEventGroupStatus.Stopped);
        }

        canExecute() {
          return this.conditionChain.evaluate(this.context);
        }

        tick(dt) {
          switch (this._status) {
            case eEventGroupStatus.Idle:
              // not active
              break;

            case eEventGroupStatus.Waiting:
              // waiting for conditions to be met
              if (this.canExecute()) {
                // TODO: 考虑这里检测增加时间间隔来减少消耗
                this._status = eEventGroupStatus.Active;
              }

              break;

            case eEventGroupStatus.Active:
              // conditions are met, now ticking actions
              this.tickActive(dt);
              break;

            case eEventGroupStatus.Stopped:
              // stopped
              break;
          }
        }

        changeStatus(newStatus) {
          if (this._status === newStatus) return;
          this._status = newStatus;

          switch (this._status) {
            case eEventGroupStatus.Waiting:
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onCreateEventGroup(this);
              break;

            case eEventGroupStatus.Stopped:
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onDestroyEventGroup(this);
              break;

            default:
              break;
          }
        }

        buildConditionChain(conditions) {
          var chain = new ConditionChain();
          conditions.forEach((condData, index) => {
            var condition = ConditionFactory.create(condData);

            if (condition) {
              chain.conditions.push(condition);
            }
          });
          return chain;
        }

        tickActive(dt) {
          var isAllFinished = true;

          for (var action of this.actions) {
            if (action.isCompleted()) continue;
            action.onExecute(this.context, dt);
            isAllFinished = false;
          }

          if (isAllFinished) {
            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {
              // restart
              this._triggerCount++;
              this.changeStatus(eEventGroupStatus.Waiting);
            } else {
              this.changeStatus(eEventGroupStatus.Stopped);
            }
          }
        }

      }); // 提供一个静态函数帮助比较value


      _export("Comparer", Comparer = class Comparer {
        static compare(a, b, op) {
          switch (op) {
            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Equal:
              return a === b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).NotEqual:
              return a !== b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Greater:
              return a > b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).GreaterEqual:
              return a >= b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).Less:
              return a < b;

            case (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
              error: Error()
            }), eCompareOp) : eCompareOp).LessEqual:
              return a <= b;

            default:
              throw new Error("Unknown compare operator: " + op);
          }
        }

      }); // Factory pattern for conditions & actions


      ConditionFactory = class ConditionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Active:
              return new emitter_cond.EmitterCondition_Active(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_InitialDelay:
              return new emitter_cond.EmitterCondition_InitialDelay(data);

            case (_crd && eEmitterCondition === void 0 ? (_reportPossibleCrUseOfeEmitterCondition({
              error: Error()
            }), eEmitterCondition) : eEmitterCondition).Emitter_Prewarm:
              return new emitter_cond.EmitterCondition_Prewarm(data);
            // ... other cases

            default:
              throw new Error("Unknown condition type: " + data.type);
          }
        }

      };
      ActionFactory = class ActionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Duration:
              return new bullet_act.BulletAction_Duration(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ElapsedTime:
              return new bullet_act.BulletAction_ElapsedTime(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_PosX:
              return new bullet_act.BulletAction_PosX(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_PosY:
              return new bullet_act.BulletAction_PosY(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Speed:
              return new bullet_act.BulletAction_Speed(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_SpeedAngle:
              return new bullet_act.BulletAction_SpeedAngle(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Acceleration:
              return new bullet_act.BulletAction_Acceleration(data);

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_AccelerationAngle:
              return new bullet_act.BulletAction_AccelerationAngle(data);
            // ... other cases

            default:
              throw new Error("Unknown action type: " + data.type);
          }
        }

      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cf0420955340bd06f2e23b1e1ab8d1926dbc074e.js.map