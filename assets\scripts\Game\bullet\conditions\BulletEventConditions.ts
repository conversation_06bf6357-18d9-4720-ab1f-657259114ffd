import { IEventCondition } from "./IEventCondition";
import { EventGroupContext, Comparer } from "../EventGroup";
import { EventConditionData, eCompareOp, eConditionOp } from "../../data/bullet/EventGroupData";

export class BulletConditionBase implements IEventCondition {
    readonly data: EventConditionData;

    constructor(data: EventConditionData) {
        this.data = data;
    }

    public evaluate(context: EventGroupContext): boolean {
        return true;
    }
}

export class BulletCondition_Duration extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // Custom evaluation logic for active condition
        return Comparer.compare(context.bullet!.duration.value, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ElapsedTime extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.elapsedTime, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_PosX extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.position.x, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_PosY extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.position.y, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Damage extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // return Comparer.compare(context.bullet!.damage.value, this.data.targetValue, this.data.compareOp);
        return false;
    }
}

export class BulletCondition_Speed extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.speed.value, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_SpeedAngle extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.speedAngle.value, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Acceleration extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.acceleration.value, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_AccelerationAngle extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.accelerationAngle.value, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Scale extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.scale.x, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorR extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.bulletSprite.color.r, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorG extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.bulletSprite.color.g, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorB extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.bulletSprite.color.b, this.data.targetValue, this.data.compareOp);
    }
}

export class BulletCondition_FacingMoveDir extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {        
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.bullet!.isFacingMoveDir.value === (this.data.targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.bullet!.isFacingMoveDir.value !== (this.data.targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class BulletCondition_Destructive extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            // case eCompareOp.Equal:
            //     return context.bullet!.isDestructive.value === (this.data.targetValue === 1) ? true : false;
            // case eCompareOp.NotEqual:
            //     return context.bullet!.isDestructive.value !== (this.data.targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class BulletCondition_DestructiveOnHit extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            // case eCompareOp.Equal:
            //     return context.bullet!.isPreWarm.value === (this.data.targetValue === 1) ? true : false;
            // case eCompareOp.NotEqual:
            //     return context.bullet!.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}