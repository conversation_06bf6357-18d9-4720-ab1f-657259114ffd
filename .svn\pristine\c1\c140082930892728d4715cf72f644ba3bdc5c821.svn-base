'use strict';

import { existsSync } from 'fs';
import { join } from 'path';

const { dialog } = require('electron')

type Selector<$> = { $: Record<keyof $, any | null> };

interface DumpData {
    value: {
        uuid: string;
        emitterData: {
            value: {
                isOnlyInScreen: boolean;
                isPreWarm: boolean;
                isLoop: boolean;
                initialDelay: number;
                preWarmDuration: number;
                preWarmEffect: any;
                emitDuration: number;
                emitInterval: number;
                emitPower: number;
                loopInterval: number;
                perEmitCount: number;
                perEmitInterval: number;
                perEmitOffsetX: number;
                angle: number;
                count: number;
                arc: number;
                radius: number;
                emitEffect: any;
                eventGroupData: string[];
            };
        };
        bulletID: any;
        bulletData: any;
    };
}

export const template = `
<ui-section expand>
  <ui-prop slot="header">
    <ui-label slot="label">发射器属性</ui-label>
  </ui-prop>
    <ui-prop type="dump" class="isOnlyInScreen"></ui-prop>
    <ui-prop type="dump" class="isPreWarm"></ui-prop>
    <ui-prop type="dump" class="isLoop"></ui-prop>
    <ui-prop type="dump" class="initialDelay"></ui-prop>
    <ui-prop type="dump" class="preWarmDuration"></ui-prop>
    <ui-prop type="dump" class="preWarmEffect"></ui-prop>
    <ui-prop type="dump" class="emitDuration"></ui-prop>
    <ui-prop type="dump" class="emitInterval"></ui-prop>
    <ui-prop type="dump" class="emitPower"></ui-prop>
    <ui-prop type="dump" class="loopInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitCount"></ui-prop>
    <ui-prop type="dump" class="perEmitInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitOffsetX"></ui-prop>
    <ui-prop type="dump" class="angle"></ui-prop>
    <ui-prop type="dump" class="count"></ui-prop>
    <ui-prop type="dump" class="arc"></ui-prop>
    <ui-prop type="dump" class="radius"></ui-prop>
    <ui-prop type="dump" class="emitEffect"></ui-prop>

    <!-- 自定义 eventGroupData -->
    <div class="eventGroupData"></div>
</ui-section>

<!-- 额外的 bullet 相关 -->
<ui-prop type="dump" class="bulletID"></ui-prop>
<ui-prop type="dump" class="bulletData"></ui-prop>
`;

export const style = `
.eventGroupData {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  align-items: flex-start; /* avoid children stretching full width */
  width: 100%;
}
.eventGroupItem {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px;
  width: 100%;
}
.eventGroupItem ui-select {
  flex: 1;
  min-width: 240px;
  max-width: 100%;
}
.eventGroupItem button {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
}
.eventGroupItem input {
  flex: 1;
  padding: 2px 4px;
  border: 1px solid #555;
  border-radius: 2px;
  background: #1a1a1a;
  color: white;
}
.eventGroupItem.invalid input {
  border-color: #e74c3c;
}
.eventGroupItem.valid input {
  border-color: #2ecc71;
}
.addEventGroupBtn {
  align-self: flex-start;
}
/* Compact icon button used inside eventGroupItem */
.eventGroupItem .icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  padding: 0;
  border: none;
  border-radius: 2px;
}
.eventGroupItem .icon-btn ui-icon { width: 16px; height: 16px; }
.eventGroupItem .icon-btn[title="编辑"] { background: #4a90e2; }
.eventGroupItem .icon-btn[title="删除"] { background: #e74c3c; }
`;

export const $ = {
    isOnlyInScreen: '.isOnlyInScreen',
    isPreWarm: '.isPreWarm',
    isLoop: '.isLoop',
    initialDelay: '.initialDelay',
    preWarmDuration: '.preWarmDuration',
    preWarmEffect: '.preWarmEffect',
    emitDuration: '.emitDuration',
    emitInterval: '.emitInterval',
    emitPower: '.emitPower',
    loopInterval: '.loopInterval',
    perEmitCount: '.perEmitCount',
    perEmitInterval: '.perEmitInterval',
    perEmitOffsetX: '.perEmitOffsetX',
    angle: '.angle',
    count: '.count',
    arc: '.arc',
    radius: '.radius',
    emitEffect: '.emitEffect',
    eventGroupData: '.eventGroupData',
    bulletID: '.bulletID',
    bulletData: '.bulletData',
};

type EmitterPanel = Selector<typeof $> & {
    dump: DumpData;
};

const root_path = 'assets/resources/Game/emitter/events/';

function checkEventGroupFileExists(category:string, eventGroupName: string): boolean {
    if (!eventGroupName) return false;
    const projectPath = Editor.Project.path;
    const filePath = join(projectPath, root_path, category, `${eventGroupName}.json`);
   
    return existsSync(filePath);
}

async function listEventNames(category: string): Promise<string[]> {
    const pattern = join('db://', root_path, category, '**/*.json').replace(/\\/g, '/');
    try {
        const res: any = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        // res can be an array of asset infos (or nested in an array in some editor versions)
        const arr: any[] = Array.isArray(res) ? res : (Array.isArray(res?.[0]) ? res[0] : []);
        const names = arr
            .filter((a: any) => a && !a.isDirectory)
            .map((a: any) => String(a.name || '').replace(/\.json$/i, ''))
            .filter(Boolean)
            .sort();
        return names;
    } catch (e) {
        console.warn('listEventNames failed', e);
        return [];
    }
}

function renderEventGroupDataArray(container: HTMLElement, category: string, dump: any) {

    const build = (names: string[]) => {
        container.innerHTML = '';

        // 创建数组容器
        const arrayContainer = document.createElement('div');
        arrayContainer.className = 'eventGroupData';
        arrayContainer.title = '事件组';
        arrayContainer.style.marginLeft = '10px';

        dump.value.forEach((item: any, index: number) => {
            const itemContainer = document.createElement('div');
            itemContainer.className = 'eventGroupItem';
            const sel = document.createElement('ui-select') as any;
            (names.length ? names : ['']).forEach(n => {
                const o = document.createElement('option'); o.value = n; o.innerText = n; sel.appendChild(o);
            });

            sel.value = item || (names[0] || ''); // 取出当前项的值，如果没有则取第一个值作为默认值
            sel.addEventListener('change', () => {
                dump.value[index] = sel.value; // 设置当前项的值
                renderEventGroupDataArray(container, category, dump); // 重新渲染
            });

            const btnOpen = document.createElement('button');
            btnOpen.setAttribute('title', '编辑');
            btnOpen.classList.add('icon-btn');
            const icon = document.createElement('ui-icon') as any;
            icon.value = 'edit';
            btnOpen.appendChild(icon);
            btnOpen.onclick = () => Editor.Message.send('bullet_editor', 'open-event-editor', sel.value);

            // Per-item remove button with trash icon
            const btnRemove = document.createElement('button');
            btnRemove.setAttribute('title', '删除');
            btnRemove.classList.add('icon-btn');
            const trash = document.createElement('ui-icon') as any;
            trash.value = 'delete'; // fallback to 'delete' if 'trash' not available
            btnRemove.appendChild(trash);
            btnRemove.onclick = () => {
                dump.value.splice(index, 1);
                renderEventGroupDataArray(container, category, dump);
            };

            itemContainer.appendChild(sel);
            itemContainer.appendChild(btnOpen);
            itemContainer.appendChild(btnRemove);
            arrayContainer.appendChild(itemContainer);
        });

        container.appendChild(arrayContainer);

        // 添加按钮
        const btnAdd = document.createElement('button'); btnAdd.textContent = '+ 添加事件组';
        btnAdd.className = 'addEventGroupBtn';
        btnAdd.style.cssText = 'background:#2ecc71;color:#fff;border:none;border-radius:2px;padding:2px 6px;margin-top:4px;align-self:flex-start;';
        btnAdd.onclick = () => {
            dump.value.push(names[0] || ''); // 默认值
            renderEventGroupDataArray(container, category, dump);
        };
        container.appendChild(btnAdd);
    };

    listEventNames(category).then(build).catch(() => build([]));
}

export function update(this: EmitterPanel, dump: DumpData) {
    this.dump = dump;

    const emitterData = dump.value.emitterData;

    // 普通字段直接走 render
    this.$.isOnlyInScreen.render(emitterData.value.isOnlyInScreen);
    this.$.isPreWarm.render(emitterData.value.isPreWarm);
    this.$.isLoop.render(emitterData.value.isLoop);
    this.$.initialDelay.render(emitterData.value.initialDelay);
    this.$.preWarmDuration.render(emitterData.value.preWarmDuration);
    this.$.preWarmEffect.render(emitterData.value.preWarmEffect);
    this.$.emitDuration.render(emitterData.value.emitDuration);
    this.$.emitInterval.render(emitterData.value.emitInterval);
    this.$.emitPower.render(emitterData.value.emitPower);
    this.$.loopInterval.render(emitterData.value.loopInterval);
    this.$.perEmitCount.render(emitterData.value.perEmitCount);
    this.$.perEmitInterval.render(emitterData.value.perEmitInterval);
    this.$.perEmitOffsetX.render(emitterData.value.perEmitOffsetX);
    this.$.angle.render(emitterData.value.angle);
    this.$.count.render(emitterData.value.count);
    this.$.arc.render(emitterData.value.arc);
    this.$.radius.render(emitterData.value.radius);
    this.$.emitEffect.render(emitterData.value.emitEffect);

    renderEventGroupDataArray(this.$.eventGroupData, "Emitter", emitterData.value.eventGroupData);

    // bullet 相关
    this.$.bulletID.render(dump.value.bulletID);
    this.$.bulletData.render(dump.value.bulletData);
}

export function ready(this: EmitterPanel) {}
