2025-9-1 15:13:57-debug: start **** info
2025-9-1 15:13:58-log: Cannot access game frame or container.
2025-9-1 15:13:58-debug: asset-db:require-engine-code (390ms)
2025-9-1 15:13:58-log: meshopt wasm decoder initialized
2025-9-1 15:13:58-log: [box2d]:box2d wasm lib loaded.
2025-9-1 15:13:58-log: [bullet]:bullet wasm lib loaded.
2025-9-1 15:13:58-log: Using legacy pipeline
2025-9-1 15:13:58-log: Forward render pipeline initialized.
2025-9-1 15:13:58-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.67MB, end 79.96MB, increase: 49.29MB
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.34MB, end 288.93MB, increase: 204.58MB
2025-9-1 15:13:58-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.87MB, end 84.31MB, increase: 3.44MB
2025-9-1 15:13:58-log: Cocos Creator v3.8.6
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.72MB, end 287.39MB, increase: 206.66MB
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.99MB, end 287.42MB, increase: 207.43MB
2025-9-1 15:13:59-debug: run package(google-play) handler(enable) start
2025-9-1 15:13:59-debug: run package(google-play) handler(enable) success!
2025-9-1 15:13:59-debug: run package(harmonyos-next) handler(enable) success!
2025-9-1 15:13:59-debug: run package(honor-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(harmonyos-next) handler(enable) start
2025-9-1 15:13:59-debug: run package(huawei-agc) handler(enable) success!
2025-9-1 15:13:59-debug: run package(huawei-quick-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(huawei-agc) handler(enable) start
2025-9-1 15:13:59-debug: run package(ios) handler(enable) start
2025-9-1 15:13:59-debug: run package(ios) handler(enable) success!
2025-9-1 15:13:59-debug: run package(linux) handler(enable) success!
2025-9-1 15:13:59-debug: run package(honor-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(linux) handler(enable) start
2025-9-1 15:13:59-debug: run package(mac) handler(enable) success!
2025-9-1 15:13:59-debug: run package(migu-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(migu-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(mac) handler(enable) start
2025-9-1 15:13:59-debug: run package(native) handler(enable) success!
2025-9-1 15:13:59-debug: run package(native) handler(enable) start
2025-9-1 15:13:59-debug: run package(ohos) handler(enable) start
2025-9-1 15:13:59-debug: run package(ohos) handler(enable) success!
2025-9-1 15:13:59-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(taobao-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-1 15:13:59-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-1 15:13:59-debug: run package(oppo-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(vivo-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-desktop) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-mobile) handler(enable) success!
2025-9-1 15:13:59-debug: run package(web-desktop) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatgame) handler(enable) start
2025-9-1 15:13:59-debug: run package(wechatgame) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatprogram) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-mobile) handler(enable) start
2025-9-1 15:13:59-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatprogram) handler(enable) success!
2025-9-1 15:13:59-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(windows) handler(enable) success!
2025-9-1 15:13:59-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(cocos-service) handler(enable) success!
2025-9-1 15:13:59-debug: run package(cocos-service) handler(enable) start
2025-9-1 15:13:59-debug: run package(windows) handler(enable) start
2025-9-1 15:13:59-debug: run package(im-plugin) handler(enable) start
2025-9-1 15:13:59-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-1 15:13:59-debug: run package(im-plugin) handler(enable) success!
2025-9-1 15:13:59-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-1 15:13:59-debug: run package(bullet_editor) handler(enable) success!
2025-9-1 15:13:59-debug: run package(bullet_editor) handler(enable) start
2025-9-1 15:13:59-debug: asset-db:worker-init: initPlugin (1004ms)
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db:worker-init start:30.66MB, end 289.64MB, increase: 258.98MB
2025-9-1 15:13:59-debug: Run asset db hook programming:beforePreStart ...
2025-9-1 15:13:59-debug: Run asset db hook programming:beforePreStart success!
2025-9-1 15:13:59-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-1 15:13:59-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-1 15:13:59-debug: run package(event_editor_panel) handler(enable) start
2025-9-1 15:13:59-debug: run package(event_editor_panel) handler(enable) success!
2025-9-1 15:13:59-debug: run package(level-editor) handler(enable) start
2025-9-1 15:13:59-debug: run package(level-editor) handler(enable) success!
2025-9-1 15:13:59-debug: asset-db:worker-init (1506ms)
2025-9-1 15:13:59-debug: asset-db-hook-programming-beforePreStart (54ms)
2025-9-1 15:13:59-debug: asset-db-hook-engine-extends-beforePreStart (54ms)
2025-9-1 15:13:59-debug: Preimport db internal success
2025-9-1 15:13:59-debug: Preimport db assets success
2025-9-1 15:13:59-debug: Run asset db hook programming:afterPreStart ...
2025-9-1 15:13:59-debug: starting packer-driver...
2025-9-1 15:13:59-debug: run package(localization-editor) handler(enable) start
2025-9-1 15:13:59-debug: run package(localization-editor) handler(enable) success!
2025-9-1 15:13:59-debug: run package(placeholder) handler(enable) start
2025-9-1 15:13:59-debug: run package(placeholder) handler(enable) success!
2025-9-1 15:14:03-debug: initialize scripting environment...
2025-9-1 15:14:03-debug: [[Executor]] prepare before lock
2025-9-1 15:14:03-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-1 15:14:03-debug: [[Executor]] prepare after unlock
2025-9-1 15:14:03-debug: Run asset db hook programming:afterPreStart success!
2025-9-1 15:14:03-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-1 15:14:03-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-1 15:14:03-debug: Start up the 'internal' database...
2025-9-1 15:14:04-debug: asset-db-hook-programming-afterPreStart (4625ms)
2025-9-1 15:14:04-debug: asset-db:worker-effect-data-processing (199ms)
2025-9-1 15:14:04-debug: asset-db-hook-engine-extends-afterPreStart (199ms)
2025-9-1 15:14:04-debug: Start up the 'assets' database...
2025-9-1 15:14:04-debug: asset-db:worker-startup-database[internal] (4851ms)
2025-9-1 15:14:04-debug: [Assets Memory track]: asset-db:worker-init: startup start:176.66MB, end 191.67MB, increase: 15.01MB
2025-9-1 15:14:04-debug: lazy register asset handler *
2025-9-1 15:14:04-debug: lazy register asset handler directory
2025-9-1 15:14:04-debug: lazy register asset handler json
2025-9-1 15:14:04-debug: lazy register asset handler text
2025-9-1 15:14:04-debug: lazy register asset handler spine-data
2025-9-1 15:14:04-debug: lazy register asset handler dragonbones
2025-9-1 15:14:04-debug: lazy register asset handler dragonbones-atlas
2025-9-1 15:14:04-debug: lazy register asset handler javascript
2025-9-1 15:14:04-debug: lazy register asset handler terrain
2025-9-1 15:14:04-debug: lazy register asset handler scene
2025-9-1 15:14:04-debug: lazy register asset handler sprite-frame
2025-9-1 15:14:04-debug: lazy register asset handler tiled-map
2025-9-1 15:14:04-debug: lazy register asset handler typescript
2025-9-1 15:14:04-debug: lazy register asset handler prefab
2025-9-1 15:14:04-debug: lazy register asset handler sign-image
2025-9-1 15:14:04-debug: lazy register asset handler buffer
2025-9-1 15:14:04-debug: lazy register asset handler texture-cube
2025-9-1 15:14:04-debug: lazy register asset handler texture
2025-9-1 15:14:04-debug: lazy register asset handler render-texture
2025-9-1 15:14:04-debug: lazy register asset handler alpha-image
2025-9-1 15:14:04-debug: lazy register asset handler image
2025-9-1 15:14:04-debug: lazy register asset handler erp-texture-cube
2025-9-1 15:14:04-debug: lazy register asset handler rt-sprite-frame
2025-9-1 15:14:04-debug: lazy register asset handler texture-cube-face
2025-9-1 15:14:04-debug: lazy register asset handler gltf
2025-9-1 15:14:04-debug: lazy register asset handler gltf-skeleton
2025-9-1 15:14:04-debug: lazy register asset handler gltf-material
2025-9-1 15:14:04-debug: lazy register asset handler gltf-animation
2025-9-1 15:14:04-debug: lazy register asset handler gltf-mesh
2025-9-1 15:14:04-debug: lazy register asset handler fbx
2025-9-1 15:14:04-debug: lazy register asset handler material
2025-9-1 15:14:04-debug: lazy register asset handler gltf-embeded-image
2025-9-1 15:14:04-debug: lazy register asset handler gltf-scene
2025-9-1 15:14:04-debug: lazy register asset handler physics-material
2025-9-1 15:14:04-debug: lazy register asset handler effect
2025-9-1 15:14:04-debug: lazy register asset handler audio-clip
2025-9-1 15:14:04-debug: lazy register asset handler effect-header
2025-9-1 15:14:04-debug: lazy register asset handler animation-graph
2025-9-1 15:14:04-debug: lazy register asset handler animation-clip
2025-9-1 15:14:04-debug: lazy register asset handler animation-graph-variant
2025-9-1 15:14:04-debug: lazy register asset handler animation-mask
2025-9-1 15:14:04-debug: lazy register asset handler bitmap-font
2025-9-1 15:14:04-debug: lazy register asset handler ttf-font
2025-9-1 15:14:04-debug: lazy register asset handler label-atlas
2025-9-1 15:14:04-debug: lazy register asset handler auto-atlas
2025-9-1 15:14:04-debug: lazy register asset handler sprite-atlas
2025-9-1 15:14:04-debug: lazy register asset handler render-stage
2025-9-1 15:14:04-debug: lazy register asset handler render-pipeline
2025-9-1 15:14:04-debug: lazy register asset handler particle
2025-9-1 15:14:04-debug: lazy register asset handler render-flow
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-material
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-skeleton
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-mesh
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-animation
2025-9-1 15:14:04-debug: lazy register asset handler video-clip
2025-9-1 15:14:04-debug: asset-db:worker-startup-database[assets] (4848ms)
2025-9-1 15:14:04-debug: asset-db:start-database (4925ms)
2025-9-1 15:14:04-debug: fix the bug of updateDefaultUserData
2025-9-1 15:14:04-debug: asset-db:ready (7855ms)
2025-9-1 15:14:04-debug: init worker message success
2025-9-1 15:14:04-debug: programming:execute-script (3ms)
2025-9-1 15:14:04-debug: [Build Memory track]: builder:worker-init start:196.31MB, end 208.75MB, increase: 12.43MB
2025-9-1 15:14:04-debug: builder:worker-init (271ms)
2025-9-1 15:14:37-debug: refresh db internal success
2025-9-1 15:14:37-debug: refresh db assets success
2025-9-1 15:14:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:14:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:14:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:14:37-debug: asset-db:refresh-all-database (149ms)
2025-9-1 15:14:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:14:50-debug: refresh db internal success
2025-9-1 15:14:50-debug: refresh db assets success
2025-9-1 15:14:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:14:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:14:50-debug: asset-db:refresh-all-database (111ms)
2025-9-1 15:14:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:14:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:15:07-debug: refresh db internal success
2025-9-1 15:15:07-debug: refresh db assets success
2025-9-1 15:15:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:15:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:15:07-debug: asset-db:refresh-all-database (115ms)
2025-9-1 15:17:11-debug: refresh db internal success
2025-9-1 15:17:11-debug: refresh db assets success
2025-9-1 15:17:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:17:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:17:11-debug: asset-db:refresh-all-database (110ms)
2025-9-1 15:17:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:17:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:45:56-debug: refresh db internal success
2025-9-1 15:45:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:45:56-debug: refresh db assets success
2025-9-1 15:45:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:45:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:45:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:45:56-debug: asset-db:refresh-all-database (140ms)
2025-9-1 15:45:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:55:58-debug: refresh db internal success
2025-9-1 15:55:58-debug: refresh db assets success
2025-9-1 15:55:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:55:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:55:58-debug: asset-db:refresh-all-database (146ms)
2025-9-1 15:55:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:55:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:56:00-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:56:00-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-1 15:56:00-debug: refresh db internal success
2025-9-1 15:56:00-debug: refresh db assets success
2025-9-1 15:56:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:56:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:56:00-debug: asset-db:refresh-all-database (156ms)
2025-9-1 15:56:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:56:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 15:57:42-debug: refresh db internal success
2025-9-1 15:57:42-debug: refresh db assets success
2025-9-1 15:57:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:57:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:57:42-debug: asset-db:refresh-all-database (136ms)
2025-9-1 15:57:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:04-debug: refresh db internal success
2025-9-1 16:03:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:04-debug: refresh db assets success
2025-9-1 16:03:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:04-debug: asset-db:refresh-all-database (128ms)
2025-9-1 16:03:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:03:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:08-debug: refresh db internal success
2025-9-1 16:03:08-debug: refresh db assets success
2025-9-1 16:03:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:08-debug: asset-db:refresh-all-database (142ms)
2025-9-1 16:03:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:03:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:15-debug: refresh db internal success
2025-9-1 16:03:15-debug: refresh db assets success
2025-9-1 16:03:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:15-debug: asset-db:refresh-all-database (100ms)
2025-9-1 16:05:42-debug: refresh db internal success
2025-9-1 16:05:42-debug: refresh db assets success
2025-9-1 16:05:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:05:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:05:42-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:05:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:07:10-debug: refresh db internal success
2025-9-1 16:07:10-debug: refresh db assets success
2025-9-1 16:07:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:07:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:07:10-debug: asset-db:refresh-all-database (125ms)
2025-9-1 16:07:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:07:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:07:44-debug: refresh db internal success
2025-9-1 16:07:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:07:44-debug: refresh db assets success
2025-9-1 16:07:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:07:44-debug: asset-db:refresh-all-database (127ms)
2025-9-1 16:07:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:07:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:08:20-debug: refresh db internal success
2025-9-1 16:08:20-debug: refresh db assets success
2025-9-1 16:08:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:08:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:08:20-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:09:56-debug: refresh db internal success
2025-9-1 16:09:56-debug: refresh db assets success
2025-9-1 16:09:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:09:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:09:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:09:56-debug: asset-db:refresh-all-database (126ms)
2025-9-1 16:09:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:10:37-debug: refresh db internal success
2025-9-1 16:10:37-debug: refresh db assets success
2025-9-1 16:10:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:10:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:10:37-debug: asset-db:refresh-all-database (132ms)
2025-9-1 16:10:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:10:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:10:43-debug: refresh db internal success
2025-9-1 16:10:43-debug: refresh db assets success
2025-9-1 16:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:10:43-debug: asset-db:refresh-all-database (120ms)
2025-9-1 16:10:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:10:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:12:04-debug: refresh db internal success
2025-9-1 16:12:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:12:04-debug: refresh db assets success
2025-9-1 16:12:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:12:04-debug: asset-db:refresh-all-database (129ms)
2025-9-1 16:12:04-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 16:12:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 16:12:32-debug: refresh db internal success
2025-9-1 16:12:32-debug: refresh db assets success
2025-9-1 16:12:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:12:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:12:32-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:12:32-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 16:12:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 16:19:27-debug: refresh db internal success
2025-9-1 16:19:27-debug: refresh db assets success
2025-9-1 16:19:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:19:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:19:27-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-1 16:19:27-debug: asset-db:refresh-all-database (138ms)
2025-9-1 16:19:27-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-1 16:25:11-debug: refresh db internal success
2025-9-1 16:25:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:11-debug: refresh db assets success
2025-9-1 16:25:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:11-debug: asset-db:refresh-all-database (142ms)
2025-9-1 16:25:11-debug: asset-db:worker-effect-data-processing (10ms)
2025-9-1 16:25:11-debug: asset-db-hook-engine-extends-afterRefresh (10ms)
2025-9-1 16:25:14-debug: refresh db internal success
2025-9-1 16:25:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:14-debug: refresh db assets success
2025-9-1 16:25:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:14-debug: asset-db:refresh-all-database (172ms)
2025-9-1 16:25:14-debug: asset-db:worker-effect-data-processing (12ms)
2025-9-1 16:25:14-debug: asset-db-hook-engine-extends-afterRefresh (12ms)
2025-9-1 16:25:19-debug: refresh db internal success
2025-9-1 16:25:19-debug: refresh db assets success
2025-9-1 16:25:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:19-debug: asset-db:refresh-all-database (111ms)
2025-9-1 16:25:19-debug: asset-db:worker-effect-data-processing (10ms)
2025-9-1 16:25:19-debug: asset-db-hook-engine-extends-afterRefresh (11ms)
2025-9-1 16:26:42-debug: refresh db internal success
2025-9-1 16:26:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:26:42-debug: refresh db assets success
2025-9-1 16:26:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:26:42-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:26:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:26:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:41:32-debug: refresh db internal success
2025-9-1 16:41:32-debug: refresh db assets success
2025-9-1 16:41:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:41:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:41:32-debug: asset-db:refresh-all-database (137ms)
2025-9-1 16:41:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:41:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:48:05-debug: refresh db internal success
2025-9-1 16:48:05-debug: refresh db assets success
2025-9-1 16:48:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:48:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:48:05-debug: asset-db:refresh-all-database (128ms)
2025-9-1 16:48:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:48:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:50:04-debug: refresh db internal success
2025-9-1 16:50:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:50:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:50:04-debug: refresh db assets success
2025-9-1 16:50:04-debug: asset-db:refresh-all-database (134ms)
2025-9-1 16:50:04-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 16:50:04-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 16:50:43-debug: refresh db internal success
2025-9-1 16:50:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:50:43-debug: refresh db assets success
2025-9-1 16:50:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:50:43-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:50:43-debug: asset-db:worker-effect-data-processing (-2ms)
2025-9-1 16:50:43-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 16:51:47-debug: refresh db internal success
2025-9-1 16:51:47-debug: refresh db assets success
2025-9-1 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:51:47-debug: asset-db:refresh-all-database (138ms)
2025-9-1 16:51:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:51:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:53:55-debug: refresh db internal success
2025-9-1 16:53:55-debug: refresh db assets success
2025-9-1 16:53:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:53:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:53:55-debug: asset-db:refresh-all-database (124ms)
2025-9-1 16:53:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:53:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:55:03-debug: refresh db internal success
2025-9-1 16:55:03-debug: refresh db assets success
2025-9-1 16:55:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:55:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:55:03-debug: asset-db:refresh-all-database (136ms)
2025-9-1 16:55:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:55:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:59:24-debug: refresh db internal success
2025-9-1 16:59:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:59:25-debug: refresh db assets success
2025-9-1 16:59:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:59:25-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 16:59:25-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:59:25-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 17:00:07-debug: refresh db internal success
2025-9-1 17:00:07-debug: refresh db assets success
2025-9-1 17:00:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:07-debug: asset-db:refresh-all-database (126ms)
2025-9-1 17:00:45-debug: refresh db internal success
2025-9-1 17:00:45-debug: refresh db assets success
2025-9-1 17:00:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:45-debug: asset-db:refresh-all-database (134ms)
2025-9-1 17:00:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:00:48-debug: refresh db internal success
2025-9-1 17:00:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:48-debug: refresh db assets success
2025-9-1 17:00:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:48-debug: asset-db:refresh-all-database (140ms)
2025-9-1 17:00:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:01:49-debug: refresh db internal success
2025-9-1 17:01:49-debug: refresh db assets success
2025-9-1 17:01:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:01:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:01:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:01:49-debug: asset-db:refresh-all-database (108ms)
2025-9-1 17:01:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:03:06-debug: refresh db internal success
2025-9-1 17:03:06-debug: refresh db assets success
2025-9-1 17:03:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:03:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:03:06-debug: asset-db:refresh-all-database (128ms)
2025-9-1 17:03:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:03:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:18:08-debug: refresh db internal success
2025-9-1 17:18:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 17:18:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:18:08-debug: refresh db assets success
2025-9-1 17:18:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:18:08-debug: asset-db:worker-effect-data-processing (-4ms)
2025-9-1 17:18:08-debug: asset-db:refresh-all-database (158ms)
2025-9-1 17:18:08-debug: asset-db-hook-engine-extends-afterRefresh (-4ms)
2025-9-1 17:18:19-debug: refresh db internal success
2025-9-1 17:18:19-debug: refresh db assets success
2025-9-1 17:18:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:18:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:18:19-debug: asset-db:refresh-all-database (97ms)
2025-9-1 17:18:19-debug: asset-db:worker-effect-data-processing (-5ms)
2025-9-1 17:18:19-debug: asset-db-hook-engine-extends-afterRefresh (-4ms)
2025-9-1 17:27:51-debug: refresh db internal success
2025-9-1 17:27:51-debug: refresh db assets success
2025-9-1 17:27:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:27:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:27:51-debug: asset-db:refresh-all-database (109ms)
2025-9-1 17:27:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:28:31-debug: refresh db internal success
2025-9-1 17:28:31-debug: refresh db assets success
2025-9-1 17:28:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:28:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:28:31-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 17:28:31-debug: asset-db:refresh-all-database (104ms)
2025-9-1 17:28:31-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 17:32:05-debug: refresh db internal success
2025-9-1 17:32:05-debug: refresh db assets success
2025-9-1 17:32:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:05-debug: asset-db:refresh-all-database (129ms)
2025-9-1 17:32:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:32:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:32:11-debug: refresh db internal success
2025-9-1 17:32:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:11-debug: refresh db assets success
2025-9-1 17:32:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:11-debug: asset-db:refresh-all-database (102ms)
2025-9-1 17:32:18-debug: refresh db internal success
2025-9-1 17:32:18-debug: refresh db assets success
2025-9-1 17:32:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:18-debug: asset-db:refresh-all-database (100ms)
2025-9-1 17:32:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:34:16-debug: refresh db internal success
2025-9-1 17:34:16-debug: refresh db assets success
2025-9-1 17:34:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:34:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:34:16-debug: asset-db:refresh-all-database (125ms)
2025-9-1 17:34:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:38:37-debug: refresh db internal success
2025-9-1 17:38:37-debug: refresh db assets success
2025-9-1 17:38:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:38:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:38:37-debug: asset-db:refresh-all-database (126ms)
2025-9-1 17:38:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:38:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:38:42-debug: refresh db internal success
2025-9-1 17:38:42-debug: refresh db assets success
2025-9-1 17:38:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:38:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:38:42-debug: asset-db:refresh-all-database (142ms)
2025-9-1 17:38:42-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-1 17:38:42-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-1 17:41:00-debug: refresh db internal success
2025-9-1 17:41:00-debug: refresh db assets success
2025-9-1 17:41:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:41:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:41:00-debug: asset-db:refresh-all-database (147ms)
2025-9-1 17:41:00-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 17:41:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 17:46:46-debug: refresh db internal success
2025-9-1 17:46:46-debug: refresh db assets success
2025-9-1 17:46:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:46-debug: asset-db:refresh-all-database (129ms)
2025-9-1 17:46:46-debug: asset-db:worker-effect-data-processing (-7ms)
2025-9-1 17:46:46-debug: asset-db-hook-engine-extends-afterRefresh (-7ms)
2025-9-1 17:46:48-debug: refresh db internal success
2025-9-1 17:46:49-debug: refresh db assets success
2025-9-1 17:46:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:49-debug: asset-db:refresh-all-database (150ms)
2025-9-1 17:46:49-debug: asset-db:worker-effect-data-processing (-6ms)
2025-9-1 17:46:49-debug: asset-db-hook-engine-extends-afterRefresh (-6ms)
2025-9-1 17:46:55-debug: refresh db internal success
2025-9-1 17:46:55-debug: refresh db assets success
2025-9-1 17:46:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:55-debug: asset-db:refresh-all-database (113ms)
2025-9-1 17:46:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:50:13-debug: refresh db internal success
2025-9-1 17:50:13-debug: refresh db assets success
2025-9-1 17:50:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:50:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:50:13-debug: asset-db:refresh-all-database (123ms)
2025-9-1 17:50:24-debug: refresh db internal success
2025-9-1 17:50:24-debug: refresh db assets success
2025-9-1 17:50:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:50:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:50:24-debug: asset-db:refresh-all-database (104ms)
2025-9-1 18:04:12-debug: refresh db internal success
2025-9-1 18:04:12-debug: refresh db assets success
2025-9-1 18:04:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:04:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:04:12-debug: asset-db:refresh-all-database (136ms)
2025-9-1 18:04:12-debug: asset-db:worker-effect-data-processing (-3ms)
2025-9-1 18:04:12-debug: asset-db-hook-engine-extends-afterRefresh (-3ms)
2025-9-1 18:09:11-debug: refresh db internal success
2025-9-1 18:09:12-debug: refresh db assets success
2025-9-1 18:09:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:09:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:09:12-debug: asset-db:refresh-all-database (130ms)
2025-9-1 18:13:53-debug: refresh db internal success
2025-9-1 18:13:53-debug: refresh db assets success
2025-9-1 18:13:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:13:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:13:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 18:13:53-debug: asset-db:refresh-all-database (145ms)
2025-9-1 18:13:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 18:15:03-debug: refresh db internal success
2025-9-1 18:15:03-debug: refresh db assets success
2025-9-1 18:15:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:15:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:15:03-debug: asset-db:refresh-all-database (126ms)
2025-9-1 18:16:15-debug: refresh db internal success
2025-9-1 18:16:15-debug: refresh db assets success
2025-9-1 18:16:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:15-debug: asset-db:refresh-all-database (128ms)
2025-9-1 18:16:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 18:16:28-debug: refresh db internal success
2025-9-1 18:16:28-debug: refresh db assets success
2025-9-1 18:16:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:28-debug: asset-db:refresh-all-database (110ms)
2025-9-1 18:16:47-debug: refresh db internal success
2025-9-1 18:16:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:47-debug: refresh db assets success
2025-9-1 18:16:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:47-debug: asset-db:refresh-all-database (109ms)
2025-9-1 18:49:29-debug: refresh db internal success
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter\EventGroup_001.json
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: refresh db assets success
2025-9-1 18:49:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:49:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:49:29-debug: asset-db:refresh-all-database (151ms)
2025-9-1 18:49:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:40-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 18:49:43-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-1 19:15:28-debug: refresh db internal success
2025-9-1 19:15:28-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:15:28-debug: refresh db assets success
2025-9-1 19:15:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:15:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:15:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 19:15:28-debug: asset-db:refresh-all-database (153ms)
2025-9-1 19:15:28-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 19:17:32-debug: refresh db internal success
2025-9-1 19:17:33-debug: refresh db assets success
2025-9-1 19:17:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:33-debug: asset-db:refresh-all-database (132ms)
2025-9-1 19:17:33-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 19:17:37-debug: refresh db internal success
2025-9-1 19:17:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:37-debug: refresh db assets success
2025-9-1 19:17:37-debug: asset-db:refresh-all-database (140ms)
2025-9-1 19:17:37-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 19:17:39-debug: refresh db internal success
2025-9-1 19:17:39-debug: refresh db assets success
2025-9-1 19:17:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:39-debug: asset-db:refresh-all-database (107ms)
2025-9-1 19:18:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:18:25-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 19:18:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:18:25-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 09:27:58-debug: refresh db internal success
2025-9-2 09:27:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter\boss_pattern_1.json
background: #aaff85; color: #000;
color: #000;
2025-9-2 09:27:59-debug: refresh db assets success
2025-9-2 09:27:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:27:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:27:59-debug: asset-db:refresh-all-database (155ms)
2025-9-2 09:27:59-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-2 09:27:59-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-9-2 09:28:29-debug: refresh db internal success
2025-9-2 09:28:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:28:29-debug: refresh db assets success
2025-9-2 09:28:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:28:29-debug: asset-db:refresh-all-database (104ms)
2025-9-2 09:28:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 09:28:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 09:44:41-debug: refresh db internal success
2025-9-2 09:44:42-debug: refresh db assets success
2025-9-2 09:44:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:44:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:44:42-debug: asset-db:refresh-all-database (124ms)
2025-9-2 09:44:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 09:44:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
