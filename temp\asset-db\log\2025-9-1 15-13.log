2025-9-1 15:13:57-debug: start **** info
2025-9-1 15:13:58-log: Cannot access game frame or container.
2025-9-1 15:13:58-debug: asset-db:require-engine-code (390ms)
2025-9-1 15:13:58-log: meshopt wasm decoder initialized
2025-9-1 15:13:58-log: [box2d]:box2d wasm lib loaded.
2025-9-1 15:13:58-log: [bullet]:bullet wasm lib loaded.
2025-9-1 15:13:58-log: Using legacy pipeline
2025-9-1 15:13:58-log: Forward render pipeline initialized.
2025-9-1 15:13:58-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.67MB, end 79.96MB, increase: 49.29MB
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.34MB, end 288.93MB, increase: 204.58MB
2025-9-1 15:13:58-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.87MB, end 84.31MB, increase: 3.44MB
2025-9-1 15:13:58-log: Cocos Creator v3.8.6
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.72MB, end 287.39MB, increase: 206.66MB
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.99MB, end 287.42MB, increase: 207.43MB
2025-9-1 15:13:59-debug: run package(google-play) handler(enable) start
2025-9-1 15:13:59-debug: run package(google-play) handler(enable) success!
2025-9-1 15:13:59-debug: run package(harmonyos-next) handler(enable) success!
2025-9-1 15:13:59-debug: run package(honor-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(harmonyos-next) handler(enable) start
2025-9-1 15:13:59-debug: run package(huawei-agc) handler(enable) success!
2025-9-1 15:13:59-debug: run package(huawei-quick-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(huawei-agc) handler(enable) start
2025-9-1 15:13:59-debug: run package(ios) handler(enable) start
2025-9-1 15:13:59-debug: run package(ios) handler(enable) success!
2025-9-1 15:13:59-debug: run package(linux) handler(enable) success!
2025-9-1 15:13:59-debug: run package(honor-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(linux) handler(enable) start
2025-9-1 15:13:59-debug: run package(mac) handler(enable) success!
2025-9-1 15:13:59-debug: run package(migu-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(migu-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(mac) handler(enable) start
2025-9-1 15:13:59-debug: run package(native) handler(enable) success!
2025-9-1 15:13:59-debug: run package(native) handler(enable) start
2025-9-1 15:13:59-debug: run package(ohos) handler(enable) start
2025-9-1 15:13:59-debug: run package(ohos) handler(enable) success!
2025-9-1 15:13:59-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(taobao-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-1 15:13:59-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-1 15:13:59-debug: run package(oppo-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(vivo-mini-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-desktop) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-mobile) handler(enable) success!
2025-9-1 15:13:59-debug: run package(web-desktop) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatgame) handler(enable) start
2025-9-1 15:13:59-debug: run package(wechatgame) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatprogram) handler(enable) start
2025-9-1 15:13:59-debug: run package(web-mobile) handler(enable) start
2025-9-1 15:13:59-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(wechatprogram) handler(enable) success!
2025-9-1 15:13:59-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-1 15:13:59-debug: run package(windows) handler(enable) success!
2025-9-1 15:13:59-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-1 15:13:59-debug: run package(cocos-service) handler(enable) success!
2025-9-1 15:13:59-debug: run package(cocos-service) handler(enable) start
2025-9-1 15:13:59-debug: run package(windows) handler(enable) start
2025-9-1 15:13:59-debug: run package(im-plugin) handler(enable) start
2025-9-1 15:13:59-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-1 15:13:59-debug: run package(im-plugin) handler(enable) success!
2025-9-1 15:13:59-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-1 15:13:59-debug: run package(bullet_editor) handler(enable) success!
2025-9-1 15:13:59-debug: run package(bullet_editor) handler(enable) start
2025-9-1 15:13:59-debug: asset-db:worker-init: initPlugin (1004ms)
2025-9-1 15:13:59-debug: [Assets Memory track]: asset-db:worker-init start:30.66MB, end 289.64MB, increase: 258.98MB
2025-9-1 15:13:59-debug: Run asset db hook programming:beforePreStart ...
2025-9-1 15:13:59-debug: Run asset db hook programming:beforePreStart success!
2025-9-1 15:13:59-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-1 15:13:59-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-1 15:13:59-debug: run package(event_editor_panel) handler(enable) start
2025-9-1 15:13:59-debug: run package(event_editor_panel) handler(enable) success!
2025-9-1 15:13:59-debug: run package(level-editor) handler(enable) start
2025-9-1 15:13:59-debug: run package(level-editor) handler(enable) success!
2025-9-1 15:13:59-debug: asset-db:worker-init (1506ms)
2025-9-1 15:13:59-debug: asset-db-hook-programming-beforePreStart (54ms)
2025-9-1 15:13:59-debug: asset-db-hook-engine-extends-beforePreStart (54ms)
2025-9-1 15:13:59-debug: Preimport db internal success
2025-9-1 15:13:59-debug: Preimport db assets success
2025-9-1 15:13:59-debug: Run asset db hook programming:afterPreStart ...
2025-9-1 15:13:59-debug: starting packer-driver...
2025-9-1 15:13:59-debug: run package(localization-editor) handler(enable) start
2025-9-1 15:13:59-debug: run package(localization-editor) handler(enable) success!
2025-9-1 15:13:59-debug: run package(placeholder) handler(enable) start
2025-9-1 15:13:59-debug: run package(placeholder) handler(enable) success!
2025-9-1 15:14:03-debug: initialize scripting environment...
2025-9-1 15:14:03-debug: [[Executor]] prepare before lock
2025-9-1 15:14:03-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-1 15:14:03-debug: [[Executor]] prepare after unlock
2025-9-1 15:14:03-debug: Run asset db hook programming:afterPreStart success!
2025-9-1 15:14:03-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-1 15:14:03-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-1 15:14:03-debug: Start up the 'internal' database...
2025-9-1 15:14:04-debug: asset-db-hook-programming-afterPreStart (4625ms)
2025-9-1 15:14:04-debug: asset-db:worker-effect-data-processing (199ms)
2025-9-1 15:14:04-debug: asset-db-hook-engine-extends-afterPreStart (199ms)
2025-9-1 15:14:04-debug: Start up the 'assets' database...
2025-9-1 15:14:04-debug: asset-db:worker-startup-database[internal] (4851ms)
2025-9-1 15:14:04-debug: [Assets Memory track]: asset-db:worker-init: startup start:176.66MB, end 191.67MB, increase: 15.01MB
2025-9-1 15:14:04-debug: lazy register asset handler *
2025-9-1 15:14:04-debug: lazy register asset handler directory
2025-9-1 15:14:04-debug: lazy register asset handler json
2025-9-1 15:14:04-debug: lazy register asset handler text
2025-9-1 15:14:04-debug: lazy register asset handler spine-data
2025-9-1 15:14:04-debug: lazy register asset handler dragonbones
2025-9-1 15:14:04-debug: lazy register asset handler dragonbones-atlas
2025-9-1 15:14:04-debug: lazy register asset handler javascript
2025-9-1 15:14:04-debug: lazy register asset handler terrain
2025-9-1 15:14:04-debug: lazy register asset handler scene
2025-9-1 15:14:04-debug: lazy register asset handler sprite-frame
2025-9-1 15:14:04-debug: lazy register asset handler tiled-map
2025-9-1 15:14:04-debug: lazy register asset handler typescript
2025-9-1 15:14:04-debug: lazy register asset handler prefab
2025-9-1 15:14:04-debug: lazy register asset handler sign-image
2025-9-1 15:14:04-debug: lazy register asset handler buffer
2025-9-1 15:14:04-debug: lazy register asset handler texture-cube
2025-9-1 15:14:04-debug: lazy register asset handler texture
2025-9-1 15:14:04-debug: lazy register asset handler render-texture
2025-9-1 15:14:04-debug: lazy register asset handler alpha-image
2025-9-1 15:14:04-debug: lazy register asset handler image
2025-9-1 15:14:04-debug: lazy register asset handler erp-texture-cube
2025-9-1 15:14:04-debug: lazy register asset handler rt-sprite-frame
2025-9-1 15:14:04-debug: lazy register asset handler texture-cube-face
2025-9-1 15:14:04-debug: lazy register asset handler gltf
2025-9-1 15:14:04-debug: lazy register asset handler gltf-skeleton
2025-9-1 15:14:04-debug: lazy register asset handler gltf-material
2025-9-1 15:14:04-debug: lazy register asset handler gltf-animation
2025-9-1 15:14:04-debug: lazy register asset handler gltf-mesh
2025-9-1 15:14:04-debug: lazy register asset handler fbx
2025-9-1 15:14:04-debug: lazy register asset handler material
2025-9-1 15:14:04-debug: lazy register asset handler gltf-embeded-image
2025-9-1 15:14:04-debug: lazy register asset handler gltf-scene
2025-9-1 15:14:04-debug: lazy register asset handler physics-material
2025-9-1 15:14:04-debug: lazy register asset handler effect
2025-9-1 15:14:04-debug: lazy register asset handler audio-clip
2025-9-1 15:14:04-debug: lazy register asset handler effect-header
2025-9-1 15:14:04-debug: lazy register asset handler animation-graph
2025-9-1 15:14:04-debug: lazy register asset handler animation-clip
2025-9-1 15:14:04-debug: lazy register asset handler animation-graph-variant
2025-9-1 15:14:04-debug: lazy register asset handler animation-mask
2025-9-1 15:14:04-debug: lazy register asset handler bitmap-font
2025-9-1 15:14:04-debug: lazy register asset handler ttf-font
2025-9-1 15:14:04-debug: lazy register asset handler label-atlas
2025-9-1 15:14:04-debug: lazy register asset handler auto-atlas
2025-9-1 15:14:04-debug: lazy register asset handler sprite-atlas
2025-9-1 15:14:04-debug: lazy register asset handler render-stage
2025-9-1 15:14:04-debug: lazy register asset handler render-pipeline
2025-9-1 15:14:04-debug: lazy register asset handler particle
2025-9-1 15:14:04-debug: lazy register asset handler render-flow
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-material
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-skeleton
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-mesh
2025-9-1 15:14:04-debug: lazy register asset handler instantiation-animation
2025-9-1 15:14:04-debug: lazy register asset handler video-clip
2025-9-1 15:14:04-debug: asset-db:worker-startup-database[assets] (4848ms)
2025-9-1 15:14:04-debug: asset-db:start-database (4925ms)
2025-9-1 15:14:04-debug: fix the bug of updateDefaultUserData
2025-9-1 15:14:04-debug: asset-db:ready (7855ms)
2025-9-1 15:14:04-debug: init worker message success
2025-9-1 15:14:04-debug: programming:execute-script (3ms)
2025-9-1 15:14:04-debug: [Build Memory track]: builder:worker-init start:196.31MB, end 208.75MB, increase: 12.43MB
2025-9-1 15:14:04-debug: builder:worker-init (271ms)
2025-9-1 15:14:37-debug: refresh db internal success
2025-9-1 15:14:37-debug: refresh db assets success
2025-9-1 15:14:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:14:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:14:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:14:37-debug: asset-db:refresh-all-database (149ms)
2025-9-1 15:14:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:14:50-debug: refresh db internal success
2025-9-1 15:14:50-debug: refresh db assets success
2025-9-1 15:14:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:14:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:14:50-debug: asset-db:refresh-all-database (111ms)
2025-9-1 15:14:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:14:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:15:07-debug: refresh db internal success
2025-9-1 15:15:07-debug: refresh db assets success
2025-9-1 15:15:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:15:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:15:07-debug: asset-db:refresh-all-database (115ms)
2025-9-1 15:17:11-debug: refresh db internal success
2025-9-1 15:17:11-debug: refresh db assets success
2025-9-1 15:17:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:17:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:17:11-debug: asset-db:refresh-all-database (110ms)
2025-9-1 15:17:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:17:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:45:56-debug: refresh db internal success
2025-9-1 15:45:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:45:56-debug: refresh db assets success
2025-9-1 15:45:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:45:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:45:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:45:56-debug: asset-db:refresh-all-database (140ms)
2025-9-1 15:45:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:55:58-debug: refresh db internal success
2025-9-1 15:55:58-debug: refresh db assets success
2025-9-1 15:55:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:55:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:55:58-debug: asset-db:refresh-all-database (146ms)
2025-9-1 15:55:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:55:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:56:00-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:56:00-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-1 15:56:00-debug: refresh db internal success
2025-9-1 15:56:00-debug: refresh db assets success
2025-9-1 15:56:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:56:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:56:00-debug: asset-db:refresh-all-database (156ms)
2025-9-1 15:56:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:56:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 15:57:42-debug: refresh db internal success
2025-9-1 15:57:42-debug: refresh db assets success
2025-9-1 15:57:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:57:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:57:42-debug: asset-db:refresh-all-database (136ms)
2025-9-1 15:57:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:04-debug: refresh db internal success
2025-9-1 16:03:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:04-debug: refresh db assets success
2025-9-1 16:03:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:04-debug: asset-db:refresh-all-database (128ms)
2025-9-1 16:03:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:03:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:08-debug: refresh db internal success
2025-9-1 16:03:08-debug: refresh db assets success
2025-9-1 16:03:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:08-debug: asset-db:refresh-all-database (142ms)
2025-9-1 16:03:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:03:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:03:15-debug: refresh db internal success
2025-9-1 16:03:15-debug: refresh db assets success
2025-9-1 16:03:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:03:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:03:15-debug: asset-db:refresh-all-database (100ms)
2025-9-1 16:05:42-debug: refresh db internal success
2025-9-1 16:05:42-debug: refresh db assets success
2025-9-1 16:05:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:05:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:05:42-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:05:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:07:10-debug: refresh db internal success
2025-9-1 16:07:10-debug: refresh db assets success
2025-9-1 16:07:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:07:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:07:10-debug: asset-db:refresh-all-database (125ms)
2025-9-1 16:07:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:07:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:07:44-debug: refresh db internal success
2025-9-1 16:07:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:07:44-debug: refresh db assets success
2025-9-1 16:07:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:07:44-debug: asset-db:refresh-all-database (127ms)
2025-9-1 16:07:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:07:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:08:20-debug: refresh db internal success
2025-9-1 16:08:20-debug: refresh db assets success
2025-9-1 16:08:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:08:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:08:20-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:09:56-debug: refresh db internal success
2025-9-1 16:09:56-debug: refresh db assets success
2025-9-1 16:09:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:09:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:09:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:09:56-debug: asset-db:refresh-all-database (126ms)
2025-9-1 16:09:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:10:37-debug: refresh db internal success
2025-9-1 16:10:37-debug: refresh db assets success
2025-9-1 16:10:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:10:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:10:37-debug: asset-db:refresh-all-database (132ms)
2025-9-1 16:10:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:10:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:10:43-debug: refresh db internal success
2025-9-1 16:10:43-debug: refresh db assets success
2025-9-1 16:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:10:43-debug: asset-db:refresh-all-database (120ms)
2025-9-1 16:10:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:10:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:12:04-debug: refresh db internal success
2025-9-1 16:12:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:12:04-debug: refresh db assets success
2025-9-1 16:12:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:12:04-debug: asset-db:refresh-all-database (129ms)
2025-9-1 16:12:04-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 16:12:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 16:12:32-debug: refresh db internal success
2025-9-1 16:12:32-debug: refresh db assets success
2025-9-1 16:12:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:12:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:12:32-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:12:32-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 16:12:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 16:19:27-debug: refresh db internal success
2025-9-1 16:19:27-debug: refresh db assets success
2025-9-1 16:19:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:19:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:19:27-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-1 16:19:27-debug: asset-db:refresh-all-database (138ms)
2025-9-1 16:19:27-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-1 16:25:11-debug: refresh db internal success
2025-9-1 16:25:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:11-debug: refresh db assets success
2025-9-1 16:25:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:11-debug: asset-db:refresh-all-database (142ms)
2025-9-1 16:25:11-debug: asset-db:worker-effect-data-processing (10ms)
2025-9-1 16:25:11-debug: asset-db-hook-engine-extends-afterRefresh (10ms)
2025-9-1 16:25:14-debug: refresh db internal success
2025-9-1 16:25:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:14-debug: refresh db assets success
2025-9-1 16:25:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:14-debug: asset-db:refresh-all-database (172ms)
2025-9-1 16:25:14-debug: asset-db:worker-effect-data-processing (12ms)
2025-9-1 16:25:14-debug: asset-db-hook-engine-extends-afterRefresh (12ms)
2025-9-1 16:25:19-debug: refresh db internal success
2025-9-1 16:25:19-debug: refresh db assets success
2025-9-1 16:25:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:25:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:25:19-debug: asset-db:refresh-all-database (111ms)
2025-9-1 16:25:19-debug: asset-db:worker-effect-data-processing (10ms)
2025-9-1 16:25:19-debug: asset-db-hook-engine-extends-afterRefresh (11ms)
2025-9-1 16:26:42-debug: refresh db internal success
2025-9-1 16:26:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:26:42-debug: refresh db assets success
2025-9-1 16:26:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:26:42-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:26:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:26:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:41:32-debug: refresh db internal success
2025-9-1 16:41:32-debug: refresh db assets success
2025-9-1 16:41:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:41:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:41:32-debug: asset-db:refresh-all-database (137ms)
2025-9-1 16:41:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:41:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:48:05-debug: refresh db internal success
2025-9-1 16:48:05-debug: refresh db assets success
2025-9-1 16:48:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:48:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:48:05-debug: asset-db:refresh-all-database (128ms)
2025-9-1 16:48:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:48:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:50:04-debug: refresh db internal success
2025-9-1 16:50:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:50:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:50:04-debug: refresh db assets success
2025-9-1 16:50:04-debug: asset-db:refresh-all-database (134ms)
2025-9-1 16:50:04-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 16:50:04-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 16:50:43-debug: refresh db internal success
2025-9-1 16:50:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:50:43-debug: refresh db assets success
2025-9-1 16:50:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:50:43-debug: asset-db:refresh-all-database (121ms)
2025-9-1 16:50:43-debug: asset-db:worker-effect-data-processing (-2ms)
2025-9-1 16:50:43-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 16:51:47-debug: refresh db internal success
2025-9-1 16:51:47-debug: refresh db assets success
2025-9-1 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:51:47-debug: asset-db:refresh-all-database (138ms)
2025-9-1 16:51:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:51:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:53:55-debug: refresh db internal success
2025-9-1 16:53:55-debug: refresh db assets success
2025-9-1 16:53:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:53:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:53:55-debug: asset-db:refresh-all-database (124ms)
2025-9-1 16:53:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:53:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:55:03-debug: refresh db internal success
2025-9-1 16:55:03-debug: refresh db assets success
2025-9-1 16:55:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:55:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:55:03-debug: asset-db:refresh-all-database (136ms)
2025-9-1 16:55:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 16:55:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 16:59:24-debug: refresh db internal success
2025-9-1 16:59:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 16:59:25-debug: refresh db assets success
2025-9-1 16:59:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 16:59:25-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 16:59:25-debug: asset-db:refresh-all-database (130ms)
2025-9-1 16:59:25-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-1 17:00:07-debug: refresh db internal success
2025-9-1 17:00:07-debug: refresh db assets success
2025-9-1 17:00:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:07-debug: asset-db:refresh-all-database (126ms)
2025-9-1 17:00:45-debug: refresh db internal success
2025-9-1 17:00:45-debug: refresh db assets success
2025-9-1 17:00:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:45-debug: asset-db:refresh-all-database (134ms)
2025-9-1 17:00:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:00:48-debug: refresh db internal success
2025-9-1 17:00:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:00:48-debug: refresh db assets success
2025-9-1 17:00:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:00:48-debug: asset-db:refresh-all-database (140ms)
2025-9-1 17:00:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:01:49-debug: refresh db internal success
2025-9-1 17:01:49-debug: refresh db assets success
2025-9-1 17:01:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:01:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:01:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:01:49-debug: asset-db:refresh-all-database (108ms)
2025-9-1 17:01:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:03:06-debug: refresh db internal success
2025-9-1 17:03:06-debug: refresh db assets success
2025-9-1 17:03:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:03:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:03:06-debug: asset-db:refresh-all-database (128ms)
2025-9-1 17:03:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:03:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:18:08-debug: refresh db internal success
2025-9-1 17:18:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 17:18:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:18:08-debug: refresh db assets success
2025-9-1 17:18:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:18:08-debug: asset-db:worker-effect-data-processing (-4ms)
2025-9-1 17:18:08-debug: asset-db:refresh-all-database (158ms)
2025-9-1 17:18:08-debug: asset-db-hook-engine-extends-afterRefresh (-4ms)
2025-9-1 17:18:19-debug: refresh db internal success
2025-9-1 17:18:19-debug: refresh db assets success
2025-9-1 17:18:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:18:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:18:19-debug: asset-db:refresh-all-database (97ms)
2025-9-1 17:18:19-debug: asset-db:worker-effect-data-processing (-5ms)
2025-9-1 17:18:19-debug: asset-db-hook-engine-extends-afterRefresh (-4ms)
2025-9-1 17:27:51-debug: refresh db internal success
2025-9-1 17:27:51-debug: refresh db assets success
2025-9-1 17:27:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:27:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:27:51-debug: asset-db:refresh-all-database (109ms)
2025-9-1 17:27:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:28:31-debug: refresh db internal success
2025-9-1 17:28:31-debug: refresh db assets success
2025-9-1 17:28:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:28:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:28:31-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 17:28:31-debug: asset-db:refresh-all-database (104ms)
2025-9-1 17:28:31-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 17:32:05-debug: refresh db internal success
2025-9-1 17:32:05-debug: refresh db assets success
2025-9-1 17:32:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:05-debug: asset-db:refresh-all-database (129ms)
2025-9-1 17:32:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:32:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:32:11-debug: refresh db internal success
2025-9-1 17:32:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:11-debug: refresh db assets success
2025-9-1 17:32:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:11-debug: asset-db:refresh-all-database (102ms)
2025-9-1 17:32:18-debug: refresh db internal success
2025-9-1 17:32:18-debug: refresh db assets success
2025-9-1 17:32:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:32:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:32:18-debug: asset-db:refresh-all-database (100ms)
2025-9-1 17:32:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:34:16-debug: refresh db internal success
2025-9-1 17:34:16-debug: refresh db assets success
2025-9-1 17:34:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:34:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:34:16-debug: asset-db:refresh-all-database (125ms)
2025-9-1 17:34:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:38:37-debug: refresh db internal success
2025-9-1 17:38:37-debug: refresh db assets success
2025-9-1 17:38:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:38:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:38:37-debug: asset-db:refresh-all-database (126ms)
2025-9-1 17:38:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 17:38:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:38:42-debug: refresh db internal success
2025-9-1 17:38:42-debug: refresh db assets success
2025-9-1 17:38:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:38:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:38:42-debug: asset-db:refresh-all-database (142ms)
2025-9-1 17:38:42-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-1 17:38:42-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-1 17:41:00-debug: refresh db internal success
2025-9-1 17:41:00-debug: refresh db assets success
2025-9-1 17:41:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:41:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:41:00-debug: asset-db:refresh-all-database (147ms)
2025-9-1 17:41:00-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 17:41:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 17:46:46-debug: refresh db internal success
2025-9-1 17:46:46-debug: refresh db assets success
2025-9-1 17:46:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:46-debug: asset-db:refresh-all-database (129ms)
2025-9-1 17:46:46-debug: asset-db:worker-effect-data-processing (-7ms)
2025-9-1 17:46:46-debug: asset-db-hook-engine-extends-afterRefresh (-7ms)
2025-9-1 17:46:48-debug: refresh db internal success
2025-9-1 17:46:49-debug: refresh db assets success
2025-9-1 17:46:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:49-debug: asset-db:refresh-all-database (150ms)
2025-9-1 17:46:49-debug: asset-db:worker-effect-data-processing (-6ms)
2025-9-1 17:46:49-debug: asset-db-hook-engine-extends-afterRefresh (-6ms)
2025-9-1 17:46:55-debug: refresh db internal success
2025-9-1 17:46:55-debug: refresh db assets success
2025-9-1 17:46:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:46:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:46:55-debug: asset-db:refresh-all-database (113ms)
2025-9-1 17:46:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 17:50:13-debug: refresh db internal success
2025-9-1 17:50:13-debug: refresh db assets success
2025-9-1 17:50:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:50:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:50:13-debug: asset-db:refresh-all-database (123ms)
2025-9-1 17:50:24-debug: refresh db internal success
2025-9-1 17:50:24-debug: refresh db assets success
2025-9-1 17:50:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 17:50:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 17:50:24-debug: asset-db:refresh-all-database (104ms)
2025-9-1 18:04:12-debug: refresh db internal success
2025-9-1 18:04:12-debug: refresh db assets success
2025-9-1 18:04:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:04:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:04:12-debug: asset-db:refresh-all-database (136ms)
2025-9-1 18:04:12-debug: asset-db:worker-effect-data-processing (-3ms)
2025-9-1 18:04:12-debug: asset-db-hook-engine-extends-afterRefresh (-3ms)
2025-9-1 18:09:11-debug: refresh db internal success
2025-9-1 18:09:12-debug: refresh db assets success
2025-9-1 18:09:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:09:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:09:12-debug: asset-db:refresh-all-database (130ms)
2025-9-1 18:13:53-debug: refresh db internal success
2025-9-1 18:13:53-debug: refresh db assets success
2025-9-1 18:13:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:13:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:13:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 18:13:53-debug: asset-db:refresh-all-database (145ms)
2025-9-1 18:13:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 18:15:03-debug: refresh db internal success
2025-9-1 18:15:03-debug: refresh db assets success
2025-9-1 18:15:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:15:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:15:03-debug: asset-db:refresh-all-database (126ms)
2025-9-1 18:16:15-debug: refresh db internal success
2025-9-1 18:16:15-debug: refresh db assets success
2025-9-1 18:16:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:15-debug: asset-db:refresh-all-database (128ms)
2025-9-1 18:16:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 18:16:28-debug: refresh db internal success
2025-9-1 18:16:28-debug: refresh db assets success
2025-9-1 18:16:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:28-debug: asset-db:refresh-all-database (110ms)
2025-9-1 18:16:47-debug: refresh db internal success
2025-9-1 18:16:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:16:47-debug: refresh db assets success
2025-9-1 18:16:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:16:47-debug: asset-db:refresh-all-database (109ms)
2025-9-1 18:49:29-debug: refresh db internal success
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter\EventGroup_001.json
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:29-debug: refresh db assets success
2025-9-1 18:49:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 18:49:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 18:49:29-debug: asset-db:refresh-all-database (151ms)
2025-9-1 18:49:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:40-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 18:49:43-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 18:49:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-1 19:15:28-debug: refresh db internal success
2025-9-1 19:15:28-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:15:28-debug: refresh db assets success
2025-9-1 19:15:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:15:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:15:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 19:15:28-debug: asset-db:refresh-all-database (153ms)
2025-9-1 19:15:28-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 19:17:32-debug: refresh db internal success
2025-9-1 19:17:33-debug: refresh db assets success
2025-9-1 19:17:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:33-debug: asset-db:refresh-all-database (132ms)
2025-9-1 19:17:33-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 19:17:37-debug: refresh db internal success
2025-9-1 19:17:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:37-debug: refresh db assets success
2025-9-1 19:17:37-debug: asset-db:refresh-all-database (140ms)
2025-9-1 19:17:37-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-1 19:17:39-debug: refresh db internal success
2025-9-1 19:17:39-debug: refresh db assets success
2025-9-1 19:17:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 19:17:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 19:17:39-debug: asset-db:refresh-all-database (107ms)
2025-9-1 19:18:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:18:25-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 19:18:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 19:18:25-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 09:27:58-debug: refresh db internal success
2025-9-2 09:27:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\emitter\events\Emitter\boss_pattern_1.json
background: #aaff85; color: #000;
color: #000;
2025-9-2 09:27:59-debug: refresh db assets success
2025-9-2 09:27:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:27:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:27:59-debug: asset-db:refresh-all-database (155ms)
2025-9-2 09:27:59-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-2 09:27:59-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-9-2 09:28:29-debug: refresh db internal success
2025-9-2 09:28:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:28:29-debug: refresh db assets success
2025-9-2 09:28:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:28:29-debug: asset-db:refresh-all-database (104ms)
2025-9-2 09:28:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 09:28:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 09:44:41-debug: refresh db internal success
2025-9-2 09:44:42-debug: refresh db assets success
2025-9-2 09:44:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 09:44:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 09:44:42-debug: asset-db:refresh-all-database (124ms)
2025-9-2 09:44:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 09:44:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 10:14:32-debug: refresh db internal success
2025-9-2 10:14:33-debug: refresh db assets success
2025-9-2 10:14:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 10:14:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 10:14:33-debug: asset-db:refresh-all-database (132ms)
2025-9-2 10:14:42-debug: Query all assets info in project
2025-9-2 10:14:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 10:14:42-debug: Skip compress image, progress: 0%
2025-9-2 10:14:42-debug: Init all bundles start..., progress: 0%
2025-9-2 10:14:42-debug: Num of bundles: 3..., progress: 0%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: Init bundle root assets start..., progress: 0%
2025-9-2 10:14:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 10:14:42-debug:   Number of other assets: 2044
2025-9-2 10:14:42-debug: Init bundle root assets success..., progress: 0%
2025-9-2 10:14:42-debug:   Number of all scripts: 274
2025-9-2 10:14:42-debug:   Number of all scenes: 8
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:218.24MB, end 220.40MB, increase: 2.15MB
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:220.43MB, end 219.12MB, increase: -1342.28KB
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 10:14:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 10:14:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.15MB, end 219.17MB, increase: 26.76KB
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 10:14:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.20MB, end 219.22MB, increase: 16.60KB
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-2 10:14:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.25MB, end 219.54MB, increase: 296.61KB
2025-9-2 10:14:42-debug: Query all assets info in project
2025-9-2 10:14:42-debug: Query all assets info in project
2025-9-2 10:14:42-debug: Query all assets info in project
2025-9-2 10:14:42-debug: Query all assets info in project
2025-9-2 10:14:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 10:14:42-debug: Skip compress image, progress: 0%
2025-9-2 10:14:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 10:14:42-debug: Skip compress image, progress: 0%
2025-9-2 10:14:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 10:14:42-debug: Skip compress image, progress: 0%
2025-9-2 10:14:42-debug: Skip compress image, progress: 0%
2025-9-2 10:14:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 10:14:42-debug: Init all bundles start..., progress: 0%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: Init bundle root assets start..., progress: 0%
2025-9-2 10:14:42-debug: Num of bundles: 3..., progress: 0%
2025-9-2 10:14:42-debug: Init all bundles start..., progress: 0%
2025-9-2 10:14:42-debug: Num of bundles: 3..., progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: Init bundle root assets start..., progress: 0%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 10:14:42-debug: Init all bundles start..., progress: 0%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: Init bundle root assets start..., progress: 0%
2025-9-2 10:14:42-debug: Num of bundles: 3..., progress: 0%
2025-9-2 10:14:42-debug: Init all bundles start..., progress: 0%
2025-9-2 10:14:42-debug: Num of bundles: 3..., progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: Init bundle root assets start..., progress: 0%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 10:14:42-debug:   Number of all scenes: 8
2025-9-2 10:14:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 10:14:42-debug: Init bundle root assets success..., progress: 0%
2025-9-2 10:14:42-debug:   Number of other assets: 2044
2025-9-2 10:14:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 10:14:42-debug:   Number of all scripts: 274
2025-9-2 10:14:42-debug:   Number of all scripts: 274
2025-9-2 10:14:42-debug:   Number of all scenes: 8
2025-9-2 10:14:42-debug:   Number of other assets: 2044
2025-9-2 10:14:42-debug: Init bundle root assets success..., progress: 0%
2025-9-2 10:14:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 10:14:42-debug:   Number of all scripts: 274
2025-9-2 10:14:42-debug:   Number of other assets: 2044
2025-9-2 10:14:42-debug: Init bundle root assets success..., progress: 0%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ---- (52ms)
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in 52 ms√, progress: 5%
2025-9-2 10:14:42-debug:   Number of all scenes: 8
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:219.81MB, end 225.15MB, increase: 5.34MB
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:225.17MB, end 225.19MB, increase: 12.10KB
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:225.21MB, end 225.23MB, increase: 12.17KB
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug:   Number of all scripts: 274
2025-9-2 10:14:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 10:14:42-debug:   Number of all scenes: 8
2025-9-2 10:14:42-debug: Init bundle root assets success..., progress: 0%
2025-9-2 10:14:42-debug:   Number of other assets: 2044
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ---- (36ms)
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in 36 ms√, progress: 10%
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:225.26MB, end 206.92MB, increase: -18786.49KB
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-2 10:14:42-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 10:14:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.01MB, end 207.07MB, increase: 59.20KB
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 10:14:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 10:14:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 10:14:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 10:14:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.18MB, end 207.44MB, increase: 269.05KB
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-2 10:14:42-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 10:14:42-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-9-2 10:14:42-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-9-2 10:14:42-debug: [Build Memory track]: 查询 Asset Bundle start:207.05MB, end 207.58MB, increase: 540.79KB
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 10:14:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.61MB, end 207.63MB, increase: 23.39KB
2025-9-2 10:14:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 10:14:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 10:14:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.66MB, end 207.68MB, increase: 22.29KB
2025-9-2 10:14:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 10:14:42-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 10:14:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.71MB, end 208.47MB, increase: 774.30KB
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 10:14:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-2 10:14:42-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-2 10:16:48-debug: refresh db internal success
2025-9-2 10:16:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 10:16:48-debug: refresh db assets success
2025-9-2 10:16:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 10:16:48-debug: asset-db:refresh-all-database (109ms)
2025-9-2 10:16:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 10:16:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 10:17:50-debug: refresh db internal success
2025-9-2 10:17:50-debug: refresh db assets success
2025-9-2 10:17:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 10:17:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 10:17:50-debug: asset-db:refresh-all-database (113ms)
2025-9-2 10:57:55-debug: refresh db internal success
2025-9-2 10:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 10:57:55-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\bullet\Bullet.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 10:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet
background: #aaff85; color: #000;
color: #000;
2025-9-2 10:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 10:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 10:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 10:57:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 10:57:55-debug: refresh db assets success
2025-9-2 10:57:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 10:57:55-debug: asset-db:refresh-all-database (156ms)
2025-9-2 10:59:39-debug: refresh db internal success
2025-9-2 10:59:40-debug: refresh db assets success
2025-9-2 10:59:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 10:59:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 10:59:40-debug: asset-db:refresh-all-database (160ms)
2025-9-2 11:03:57-debug: refresh db internal success
2025-9-2 11:03:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:03:57-debug: refresh db assets success
2025-9-2 11:03:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:03:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:03:57-debug: asset-db:refresh-all-database (109ms)
2025-9-2 11:23:15-debug: refresh db internal success
2025-9-2 11:23:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:23:15-debug: refresh db assets success
2025-9-2 11:23:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:23:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:23:15-debug: asset-db:refresh-all-database (145ms)
2025-9-2 11:23:26-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:23:26-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 11:23:48-debug: refresh db internal success
2025-9-2 11:23:48-debug: refresh db assets success
2025-9-2 11:23:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:23:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:23:48-debug: asset-db:refresh-all-database (136ms)
2025-9-2 11:23:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:23:48-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 11:24:45-debug: refresh db internal success
2025-9-2 11:24:45-debug: refresh db assets success
2025-9-2 11:24:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:24:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:24:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:24:45-debug: asset-db:refresh-all-database (154ms)
2025-9-2 11:24:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 11:25:18-debug: refresh db internal success
2025-9-2 11:25:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:25:18-debug: refresh db assets success
2025-9-2 11:25:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:25:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:25:18-debug: asset-db:refresh-all-database (156ms)
2025-9-2 11:25:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:25:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:29:05-debug: refresh db internal success
2025-9-2 11:29:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:29:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:29:05-debug: refresh db assets success
2025-9-2 11:29:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:29:05-debug: asset-db:refresh-all-database (140ms)
2025-9-2 11:29:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:29:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:29:21-debug: refresh db internal success
2025-9-2 11:29:21-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:29:21-debug: refresh db assets success
2025-9-2 11:29:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:29:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:29:21-debug: asset-db:refresh-all-database (136ms)
2025-9-2 11:29:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:29:29-debug: refresh db internal success
2025-9-2 11:29:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:29:29-debug: refresh db assets success
2025-9-2 11:29:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:29:29-debug: asset-db:refresh-all-database (104ms)
2025-9-2 11:32:10-debug: refresh db internal success
2025-9-2 11:32:10-debug: refresh db assets success
2025-9-2 11:32:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:32:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:32:10-debug: asset-db:refresh-all-database (129ms)
2025-9-2 11:32:10-debug: asset-db:worker-effect-data-processing (-3ms)
2025-9-2 11:32:10-debug: asset-db-hook-engine-extends-afterRefresh (-2ms)
2025-9-2 11:33:55-debug: refresh db internal success
2025-9-2 11:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:33:55-debug: refresh db assets success
2025-9-2 11:33:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:33:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:33:55-debug: asset-db:refresh-all-database (139ms)
2025-9-2 11:33:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:33:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:34:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\Bullet_New.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:34:00-debug: asset-db:reimport-asset68ac1a9d-3829-40ab-9efb-62a7794c31ed (3ms)
2025-9-2 11:34:00-debug: refresh db internal success
2025-9-2 11:34:00-debug: refresh db assets success
2025-9-2 11:34:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:34:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:34:00-debug: asset-db:refresh-all-database (105ms)
2025-9-2 11:34:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:34:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:45:09-debug: refresh db internal success
2025-9-2 11:45:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:45:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:45:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:45:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:45:09-debug: refresh db assets success
2025-9-2 11:45:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:45:09-debug: asset-db:refresh-all-database (149ms)
2025-9-2 11:45:12-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\Bullet_New.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:45:12-debug: asset-db:reimport-asset68ac1a9d-3829-40ab-9efb-62a7794c31ed (2ms)
2025-9-2 11:48:07-debug: refresh db internal success
2025-9-2 11:48:07-debug: refresh db assets success
2025-9-2 11:48:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:48:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:48:07-debug: asset-db:refresh-all-database (131ms)
2025-9-2 11:48:07-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-2 11:48:07-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-2 11:48:08-debug: Query all assets info in project
2025-9-2 11:48:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 11:48:08-debug: Skip compress image, progress: 0%
2025-9-2 11:48:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 11:48:08-debug: Init all bundles start..., progress: 0%
2025-9-2 11:48:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:48:08-debug: Num of bundles: 3..., progress: 0%
2025-9-2 11:48:08-debug: Init bundle root assets start..., progress: 0%
2025-9-2 11:48:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 11:48:08-debug:   Number of other assets: 2044
2025-9-2 11:48:08-debug:   Number of all scripts: 274
2025-9-2 11:48:08-debug:   Number of all scenes: 8
2025-9-2 11:48:08-debug: Init bundle root assets success..., progress: 0%
2025-9-2 11:48:08-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-2 11:48:08-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-2 11:48:08-debug: [Build Memory track]: 查询 Asset Bundle start:214.71MB, end 215.76MB, increase: 1.05MB
2025-9-2 11:48:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 11:48:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:48:08-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-2 11:48:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 11:48:08-debug: [Build Memory track]: 查询 Asset Bundle start:215.79MB, end 216.05MB, increase: 265.99KB
2025-9-2 11:48:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:48:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-2ms)
2025-9-2 11:48:08-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-2 11:48:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.08MB, end 216.10MB, increase: 25.96KB
2025-9-2 11:48:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 11:48:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 11:48:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (-2ms)
2025-9-2 11:48:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.13MB, end 216.16MB, increase: 27.62KB
2025-9-2 11:48:08-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-2 11:48:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 11:48:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:48:08-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-2 11:48:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.19MB, end 216.47MB, increase: 287.35KB
2025-9-2 11:48:23-debug: refresh db internal success
2025-9-2 11:48:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:48:23-debug: refresh db assets success
2025-9-2 11:48:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:48:23-debug: asset-db:refresh-all-database (99ms)
2025-9-2 11:48:23-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-2 11:48:23-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-2 11:49:51-debug: refresh db internal success
2025-9-2 11:49:51-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:49:51-debug: refresh db assets success
2025-9-2 11:49:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:49:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:49:51-debug: asset-db:refresh-all-database (116ms)
2025-9-2 11:49:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:49:51-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 11:49:53-debug: Query all assets info in project
2025-9-2 11:49:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 11:49:53-debug: Skip compress image, progress: 0%
2025-9-2 11:49:53-debug: Init all bundles start..., progress: 0%
2025-9-2 11:49:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:49:53-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 11:49:53-debug: Num of bundles: 3..., progress: 0%
2025-9-2 11:49:53-debug: Init bundle root assets start..., progress: 0%
2025-9-2 11:49:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 11:49:53-debug:   Number of all scenes: 8
2025-9-2 11:49:53-debug:   Number of all scripts: 274
2025-9-2 11:49:53-debug: Init bundle root assets success..., progress: 0%
2025-9-2 11:49:53-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-2 11:49:53-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-2 11:49:53-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 11:49:53-debug:   Number of other assets: 2044
2025-9-2 11:49:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:49:53-debug: [Build Memory track]: 查询 Asset Bundle start:205.18MB, end 205.52MB, increase: 341.96KB
2025-9-2 11:49:53-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-9-2 11:49:53-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-9-2 11:49:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 11:49:53-debug: [Build Memory track]: 查询 Asset Bundle start:205.54MB, end 205.85MB, increase: 317.77KB
2025-9-2 11:49:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:49:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 11:49:53-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 11:49:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.88MB, end 205.91MB, increase: 26.79KB
2025-9-2 11:49:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 11:49:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 11:49:53-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 11:49:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 11:49:53-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 11:49:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.94MB, end 205.96MB, increase: 25.56KB
2025-9-2 11:49:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:49:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-2 11:49:53-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-2 11:49:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.99MB, end 206.29MB, increase: 307.55KB
2025-9-2 11:50:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:50:40-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-2 11:50:42-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:50:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 11:50:45-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:50:45-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-2 11:50:47-debug: Query all assets info in project
2025-9-2 11:50:47-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 11:50:47-debug: Skip compress image, progress: 0%
2025-9-2 11:50:47-debug: Init all bundles start..., progress: 0%
2025-9-2 11:50:47-debug: Num of bundles: 3..., progress: 0%
2025-9-2 11:50:47-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:50:47-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 11:50:47-debug: Init bundle root assets start..., progress: 0%
2025-9-2 11:50:47-debug:   Number of all scripts: 274
2025-9-2 11:50:47-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 11:50:47-debug:   Number of other assets: 2044
2025-9-2 11:50:47-debug: Init bundle root assets success..., progress: 0%
2025-9-2 11:50:47-debug:   Number of all scenes: 8
2025-9-2 11:50:47-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-2 11:50:47-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-2 11:50:47-debug: [Build Memory track]: 查询 Asset Bundle start:210.26MB, end 207.19MB, increase: -3142.63KB
2025-9-2 11:50:47-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 11:50:47-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:50:47-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 11:50:47-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 11:50:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 11:50:47-debug: [Build Memory track]: 查询 Asset Bundle start:207.22MB, end 207.49MB, increase: 274.88KB
2025-9-2 11:50:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:50:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 11:50:47-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 11:50:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.52MB, end 207.54MB, increase: 26.05KB
2025-9-2 11:50:47-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 11:50:47-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 11:50:47-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 11:50:47-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 11:50:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 11:50:47-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.57MB, end 207.60MB, increase: 25.98KB
2025-9-2 11:50:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:50:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 11:50:47-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 11:50:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.63MB, end 207.91MB, increase: 292.13KB
2025-9-2 11:52:01-debug: refresh db internal success
2025-9-2 11:52:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:52:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:52:01-debug: refresh db assets success
2025-9-2 11:52:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:52:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:52:01-debug: asset-db:refresh-all-database (136ms)
2025-9-2 11:52:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 11:52:04-debug: Query all assets info in project
2025-9-2 11:52:04-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 11:52:04-debug: Skip compress image, progress: 0%
2025-9-2 11:52:04-debug: Init all bundles start..., progress: 0%
2025-9-2 11:52:04-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 11:52:04-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:52:04-debug: Num of bundles: 3..., progress: 0%
2025-9-2 11:52:04-debug: Init bundle root assets start..., progress: 0%
2025-9-2 11:52:04-debug:   Number of all scripts: 274
2025-9-2 11:52:04-debug:   Number of other assets: 2044
2025-9-2 11:52:04-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 11:52:04-debug: Init bundle root assets success..., progress: 0%
2025-9-2 11:52:04-debug:   Number of all scenes: 8
2025-9-2 11:52:04-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-2 11:52:04-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-2 11:52:04-debug: [Build Memory track]: 查询 Asset Bundle start:211.79MB, end 212.48MB, increase: 703.08KB
2025-9-2 11:52:04-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 11:52:04-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 11:52:04-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 11:52:04-debug: [Build Memory track]: 查询 Asset Bundle start:212.51MB, end 212.79MB, increase: 288.77KB
2025-9-2 11:52:04-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 11:52:04-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 11:52:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:52:04-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.82MB, end 212.83MB, increase: 16.53KB
2025-9-2 11:52:04-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 11:52:04-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 11:52:04-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 11:52:04-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 11:52:04-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 11:52:04-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.86MB, end 212.88MB, increase: 16.38KB
2025-9-2 11:52:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 11:52:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 11:52:04-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-2 11:52:04-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.91MB, end 213.21MB, increase: 313.18KB
2025-9-2 11:52:17-debug: refresh db internal success
2025-9-2 11:52:18-debug: refresh db assets success
2025-9-2 11:52:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:52:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:52:18-debug: asset-db:refresh-all-database (119ms)
2025-9-2 11:52:21-debug: refresh db internal success
2025-9-2 11:52:21-debug: refresh db assets success
2025-9-2 11:52:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:52:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:52:21-debug: asset-db:refresh-all-database (154ms)
2025-9-2 11:59:02-debug: refresh db internal success
2025-9-2 11:59:02-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:59:02-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:59:02-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 11:59:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 11:59:02-debug: refresh db assets success
2025-9-2 11:59:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 11:59:02-debug: asset-db:refresh-all-database (142ms)
2025-9-2 11:59:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 11:59:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:30:46-debug: refresh db internal success
2025-9-2 12:30:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:30:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:30:47-debug: refresh db assets success
2025-9-2 12:30:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:30:47-debug: asset-db:refresh-all-database (135ms)
2025-9-2 12:30:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:30:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:30:57-debug: Query all assets info in project
2025-9-2 12:30:57-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:30:57-debug: Skip compress image, progress: 0%
2025-9-2 12:30:57-debug: Init all bundles start..., progress: 0%
2025-9-2 12:30:57-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:30:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:30:57-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:30:57-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:30:57-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:30:57-debug:   Number of all scripts: 274
2025-9-2 12:30:57-debug:   Number of other assets: 2044
2025-9-2 12:30:57-debug:   Number of all scenes: 8
2025-9-2 12:30:57-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:30:57-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-2 12:30:57-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:30:57-debug: [Build Memory track]: 查询 Asset Bundle start:216.43MB, end 217.27MB, increase: 861.50KB
2025-9-2 12:30:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:30:57-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-2 12:30:57-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:30:57-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:30:57-debug: [Build Memory track]: 查询 Asset Bundle start:217.30MB, end 217.57MB, increase: 275.10KB
2025-9-2 12:30:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:30:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:30:57-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:30:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.59MB, end 217.61MB, increase: 17.42KB
2025-9-2 12:30:57-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:30:57-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:30:57-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-2 12:30:57-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-2 12:30:57-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.64MB, end 217.67MB, increase: 26.42KB
2025-9-2 12:30:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:30:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:30:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-2 12:30:57-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-2 12:30:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.69MB, end 217.98MB, increase: 291.81KB
2025-9-2 12:31:56-debug: refresh db internal success
2025-9-2 12:31:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:31:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:31:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:31:56-debug: refresh db assets success
2025-9-2 12:31:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:31:56-debug: asset-db:refresh-all-database (154ms)
2025-9-2 12:31:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:31:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:32:06-debug: refresh db internal success
2025-9-2 12:32:06-debug: refresh db assets success
2025-9-2 12:32:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:32:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:32:06-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-2 12:32:06-debug: asset-db:refresh-all-database (127ms)
2025-9-2 12:32:06-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-2 12:32:14-debug: Query all assets info in project
2025-9-2 12:32:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:32:14-debug: Skip compress image, progress: 0%
2025-9-2 12:32:14-debug: Init all bundles start..., progress: 0%
2025-9-2 12:32:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:32:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:32:14-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:32:14-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:32:14-debug:   Number of all scripts: 274
2025-9-2 12:32:14-debug:   Number of other assets: 2044
2025-9-2 12:32:14-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:32:14-debug:   Number of all scenes: 8
2025-9-2 12:32:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:32:14-debug: [Build Memory track]: 查询 Asset Bundle start:226.64MB, end 225.34MB, increase: -1326.86KB
2025-9-2 12:32:14-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-2 12:32:14-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-2 12:32:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:32:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:32:14-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:32:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:32:14-debug: [Build Memory track]: 查询 Asset Bundle start:225.37MB, end 225.64MB, increase: 276.02KB
2025-9-2 12:32:14-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:32:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:32:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:32:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:225.67MB, end 225.70MB, increase: 25.84KB
2025-9-2 12:32:14-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 12:32:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:32:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:32:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:32:14-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:32:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:225.72MB, end 225.75MB, increase: 26.91KB
2025-9-2 12:32:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:32:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:32:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:32:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:225.78MB, end 226.07MB, increase: 297.33KB
2025-9-2 12:32:14-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:32:43-debug: refresh db internal success
2025-9-2 12:32:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:32:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:32:43-debug: refresh db assets success
2025-9-2 12:32:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:32:43-debug: asset-db:refresh-all-database (157ms)
2025-9-2 12:32:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:32:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 12:32:45-debug: Query all assets info in project
2025-9-2 12:32:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:32:45-debug: Skip compress image, progress: 0%
2025-9-2 12:32:45-debug: Init all bundles start..., progress: 0%
2025-9-2 12:32:45-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:32:45-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:32:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:32:45-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:32:45-debug:   Number of all scenes: 8
2025-9-2 12:32:45-debug:   Number of all scripts: 274
2025-9-2 12:32:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:32:45-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:32:45-debug:   Number of other assets: 2044
2025-9-2 12:32:45-log: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-9-2 12:32:45-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-9-2 12:32:45-debug: [Build Memory track]: 查询 Asset Bundle start:205.32MB, end 208.17MB, increase: 2.85MB
2025-9-2 12:32:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:32:45-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:32:45-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:32:45-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:32:45-debug: [Build Memory track]: 查询 Asset Bundle start:208.20MB, end 208.47MB, increase: 275.10KB
2025-9-2 12:32:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:32:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:32:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:32:45-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 12:32:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:32:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.49MB, end 208.52MB, increase: 26.26KB
2025-9-2 12:32:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:32:45-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:32:45-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:32:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.55MB, end 208.58MB, increase: 25.70KB
2025-9-2 12:32:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:32:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:32:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-2 12:32:45-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-2 12:32:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.61MB, end 206.07MB, increase: -2599.86KB
2025-9-2 12:33:06-debug: refresh db internal success
2025-9-2 12:33:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:33:06-debug: refresh db assets success
2025-9-2 12:33:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:33:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:33:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:33:06-debug: asset-db:refresh-all-database (137ms)
2025-9-2 12:33:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:33:07-debug: Query all assets info in project
2025-9-2 12:33:07-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:33:07-debug: Skip compress image, progress: 0%
2025-9-2 12:33:07-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:33:07-debug: Init all bundles start..., progress: 0%
2025-9-2 12:33:07-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:07-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:33:07-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:33:07-debug:   Number of all scenes: 8
2025-9-2 12:33:07-debug:   Number of all scripts: 274
2025-9-2 12:33:07-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:33:07-debug:   Number of other assets: 2044
2025-9-2 12:33:07-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:33:07-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-2 12:33:07-debug: [Build Memory track]: 查询 Asset Bundle start:209.54MB, end 211.94MB, increase: 2.40MB
2025-9-2 12:33:07-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:33:07-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-2 12:33:07-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:07-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:33:07-debug: [Build Memory track]: 查询 Asset Bundle start:211.97MB, end 212.24MB, increase: 274.68KB
2025-9-2 12:33:07-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:33:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:33:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:07-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:33:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.26MB, end 212.28MB, increase: 16.33KB
2025-9-2 12:33:07-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:33:07-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:33:07-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 12:33:07-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.31MB, end 212.33MB, increase: 17.01KB
2025-9-2 12:33:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:33:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:33:07-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:33:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.35MB, end 212.64MB, increase: 290.89KB
2025-9-2 12:33:31-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:33:31-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-2 12:33:34-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:33:34-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-2 12:33:34-debug: Query all assets info in project
2025-9-2 12:33:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:33:34-debug: Skip compress image, progress: 0%
2025-9-2 12:33:34-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:33:34-debug: Init all bundles start..., progress: 0%
2025-9-2 12:33:34-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:34-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:33:34-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:33:34-debug:   Number of all scenes: 8
2025-9-2 12:33:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:33:34-debug:   Number of all scripts: 274
2025-9-2 12:33:34-debug:   Number of other assets: 2044
2025-9-2 12:33:34-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:33:34-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-2 12:33:34-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-9-2 12:33:34-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:33:34-debug: [Build Memory track]: 查询 Asset Bundle start:213.44MB, end 215.83MB, increase: 2.39MB
2025-9-2 12:33:34-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:34-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:33:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:33:34-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:33:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:34-debug: [Build Memory track]: 查询 Asset Bundle start:215.86MB, end 216.13MB, increase: 275.05KB
2025-9-2 12:33:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:33:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 12:33:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.16MB, end 216.18MB, increase: 25.80KB
2025-9-2 12:33:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:33:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:33:34-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 12:33:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.21MB, end 216.23MB, increase: 16.77KB
2025-9-2 12:33:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:33:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:33:34-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:33:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.25MB, end 216.54MB, increase: 291.36KB
2025-9-2 12:33:54-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:33:54-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (5ms)
2025-9-2 12:33:57-debug: Query all assets info in project
2025-9-2 12:33:57-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:33:57-debug: Skip compress image, progress: 0%
2025-9-2 12:33:57-debug: Init all bundles start..., progress: 0%
2025-9-2 12:33:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:57-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:33:57-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:33:57-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:33:57-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:33:57-debug:   Number of all scripts: 274
2025-9-2 12:33:57-debug:   Number of other assets: 2044
2025-9-2 12:33:57-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:33:57-debug:   Number of all scenes: 8
2025-9-2 12:33:57-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-2 12:33:57-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-2 12:33:57-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:33:57-debug: [Build Memory track]: 查询 Asset Bundle start:216.65MB, end 219.09MB, increase: 2.44MB
2025-9-2 12:33:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:33:57-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:33:57-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:33:57-debug: [Build Memory track]: 查询 Asset Bundle start:219.12MB, end 219.39MB, increase: 275.23KB
2025-9-2 12:33:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:33:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:33:57-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 12:33:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.42MB, end 219.44MB, increase: 26.16KB
2025-9-2 12:33:57-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:33:57-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:33:57-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 12:33:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:33:57-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.47MB, end 219.49MB, increase: 16.46KB
2025-9-2 12:33:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:33:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:33:57-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-2 12:33:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.52MB, end 216.78MB, increase: -2800.18KB
2025-9-2 12:35:25-debug: refresh db internal success
2025-9-2 12:35:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:35:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:35:25-debug: refresh db assets success
2025-9-2 12:35:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:35:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:35:25-debug: asset-db:refresh-all-database (138ms)
2025-9-2 12:35:30-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:35:30-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 12:35:37-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:35:37-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (1ms)
2025-9-2 12:35:39-debug: Query all assets info in project
2025-9-2 12:35:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:35:39-debug: Skip compress image, progress: 0%
2025-9-2 12:35:39-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:35:39-debug: Init all bundles start..., progress: 0%
2025-9-2 12:35:39-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:35:39-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:35:39-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:35:39-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:35:39-debug:   Number of all scenes: 8
2025-9-2 12:35:39-debug:   Number of all scripts: 274
2025-9-2 12:35:39-debug:   Number of other assets: 2044
2025-9-2 12:35:39-debug: [Build Memory track]: 查询 Asset Bundle start:224.21MB, end 220.97MB, increase: -3314.95KB
2025-9-2 12:35:39-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-9-2 12:35:39-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-9-2 12:35:39-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:35:39-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:35:39-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:35:39-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:35:39-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:35:39-debug: [Build Memory track]: 查询 Asset Bundle start:221.00MB, end 221.27MB, increase: 276.47KB
2025-9-2 12:35:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:35:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:35:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-2 12:35:39-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-2 12:35:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.30MB, end 221.33MB, increase: 26.44KB
2025-9-2 12:35:39-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:35:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:35:39-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 12:35:39-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.36MB, end 221.37MB, increase: 16.20KB
2025-9-2 12:35:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:35:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:35:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:35:39-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-2 12:35:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.40MB, end 221.70MB, increase: 307.25KB
2025-9-2 12:35:56-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:35:56-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-2 12:35:58-debug: Query all assets info in project
2025-9-2 12:35:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:35:58-debug: Skip compress image, progress: 0%
2025-9-2 12:35:58-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:35:58-debug: Init all bundles start..., progress: 0%
2025-9-2 12:35:58-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:35:58-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:35:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:35:58-debug:   Number of other assets: 2044
2025-9-2 12:35:58-debug:   Number of all scripts: 274
2025-9-2 12:35:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:35:58-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:35:58-debug:   Number of all scenes: 8
2025-9-2 12:35:58-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-2 12:35:58-debug: [Build Memory track]: 查询 Asset Bundle start:223.90MB, end 223.33MB, increase: -585.37KB
2025-9-2 12:35:58-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:35:58-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-2 12:35:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:35:58-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:35:58-debug: [Build Memory track]: 查询 Asset Bundle start:223.36MB, end 223.63MB, increase: 275.26KB
2025-9-2 12:35:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:35:58-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:35:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:35:58-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:35:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.65MB, end 223.67MB, increase: 16.88KB
2025-9-2 12:35:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:35:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:35:58-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:35:58-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:35:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:223.70MB, end 223.72MB, increase: 25.28KB
2025-9-2 12:35:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:35:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:35:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:35:58-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:35:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.75MB, end 224.04MB, increase: 291.73KB
2025-9-2 12:38:29-debug: refresh db internal success
2025-9-2 12:38:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:38:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:38:29-debug: refresh db assets success
2025-9-2 12:38:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:38:29-debug: asset-db:refresh-all-database (149ms)
2025-9-2 12:38:30-debug: Query all assets info in project
2025-9-2 12:38:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:38:30-debug: Skip compress image, progress: 0%
2025-9-2 12:38:30-debug: Init all bundles start..., progress: 0%
2025-9-2 12:38:30-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:38:30-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:38:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:38:30-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:38:30-debug:   Number of all scenes: 8
2025-9-2 12:38:30-debug:   Number of all scripts: 274
2025-9-2 12:38:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:38:30-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:38:30-debug:   Number of other assets: 2044
2025-9-2 12:38:30-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-2 12:38:30-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-2 12:38:30-debug: [Build Memory track]: 查询 Asset Bundle start:204.01MB, end 206.70MB, increase: 2.69MB
2025-9-2 12:38:30-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:38:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:38:30-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:38:30-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:38:30-debug: [Build Memory track]: 查询 Asset Bundle start:206.73MB, end 207.01MB, increase: 283.55KB
2025-9-2 12:38:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:38:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:38:30-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:38:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.04MB, end 207.05MB, increase: 16.32KB
2025-9-2 12:38:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:38:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:38:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:38:30-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:38:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:38:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.08MB, end 207.11MB, increase: 25.90KB
2025-9-2 12:38:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:38:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:38:30-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:38:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.14MB, end 207.43MB, increase: 296.56KB
2025-9-2 12:39:50-debug: refresh db internal success
2025-9-2 12:39:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:39:50-debug: refresh db assets success
2025-9-2 12:39:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:39:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:39:50-debug: asset-db:refresh-all-database (130ms)
2025-9-2 12:39:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:39:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:40:09-debug: refresh db internal success
2025-9-2 12:40:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:40:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:40:09-debug: refresh db assets success
2025-9-2 12:40:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:40:09-debug: asset-db:refresh-all-database (108ms)
2025-9-2 12:40:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:40:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:45:34-debug: refresh db internal success
2025-9-2 12:45:34-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:45:34-debug: refresh db assets success
2025-9-2 12:45:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:45:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:45:34-debug: asset-db:refresh-all-database (104ms)
2025-9-2 12:45:35-debug: Query all assets info in project
2025-9-2 12:45:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:45:35-debug: Skip compress image, progress: 0%
2025-9-2 12:45:35-debug: Init all bundles start..., progress: 0%
2025-9-2 12:45:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:45:35-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:45:35-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:45:35-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:45:35-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:45:35-debug:   Number of all scenes: 8
2025-9-2 12:45:35-debug:   Number of all scripts: 274
2025-9-2 12:45:35-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:45:35-debug:   Number of other assets: 2044
2025-9-2 12:45:35-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-9-2 12:45:35-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-9-2 12:45:35-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:45:35-debug: [Build Memory track]: 查询 Asset Bundle start:213.28MB, end 213.87MB, increase: 610.31KB
2025-9-2 12:45:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:45:35-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:45:35-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:45:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:45:35-debug: [Build Memory track]: 查询 Asset Bundle start:213.90MB, end 214.17MB, increase: 275.04KB
2025-9-2 12:45:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:45:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-2 12:45:35-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-2 12:45:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.20MB, end 214.22MB, increase: 26.44KB
2025-9-2 12:45:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:45:35-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:45:35-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-2 12:45:35-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-2 12:45:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:45:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:45:35-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.25MB, end 214.28MB, increase: 25.84KB
2025-9-2 12:45:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:45:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.31MB, end 214.59MB, increase: 292.01KB
2025-9-2 12:45:35-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-2 12:46:43-debug: refresh db internal success
2025-9-2 12:46:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:46:43-debug: refresh db assets success
2025-9-2 12:46:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:46:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:46:43-debug: asset-db:refresh-all-database (132ms)
2025-9-2 12:46:51-debug: Query all assets info in project
2025-9-2 12:46:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:46:51-debug: Skip compress image, progress: 0%
2025-9-2 12:46:51-debug: Init all bundles start..., progress: 0%
2025-9-2 12:46:51-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:46:51-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:46:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:46:51-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:46:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:46:51-debug:   Number of all scenes: 8
2025-9-2 12:46:51-debug:   Number of other assets: 2044
2025-9-2 12:46:51-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:46:51-debug:   Number of all scripts: 274
2025-9-2 12:46:51-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-9-2 12:46:51-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-2 12:46:51-debug: [Build Memory track]: 查询 Asset Bundle start:217.90MB, end 218.73MB, increase: 853.89KB
2025-9-2 12:46:51-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:46:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:46:51-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:46:51-debug: [Build Memory track]: 查询 Asset Bundle start:218.76MB, end 219.03MB, increase: 275.53KB
2025-9-2 12:46:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:46:51-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:46:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:46:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:46:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:46:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:46:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.06MB, end 219.07MB, increase: 17.39KB
2025-9-2 12:46:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-2 12:46:51-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-2 12:46:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:46:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:46:51-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.10MB, end 219.13MB, increase: 25.65KB
2025-9-2 12:46:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-2 12:46:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.16MB, end 219.43MB, increase: 281.90KB
2025-9-2 12:47:29-debug: refresh db internal success
2025-9-2 12:47:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:47:29-debug: refresh db assets success
2025-9-2 12:47:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:47:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:47:29-debug: asset-db:refresh-all-database (135ms)
2025-9-2 12:47:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 12:47:30-debug: Query all assets info in project
2025-9-2 12:47:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:47:30-debug: Skip compress image, progress: 0%
2025-9-2 12:47:30-debug: Init all bundles start..., progress: 0%
2025-9-2 12:47:30-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:47:30-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:47:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:47:30-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:47:30-debug:   Number of all scripts: 274
2025-9-2 12:47:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:47:30-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:47:30-debug:   Number of other assets: 2044
2025-9-2 12:47:30-debug:   Number of all scenes: 8
2025-9-2 12:47:30-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-2 12:47:30-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:47:30-debug: [Build Memory track]: 查询 Asset Bundle start:224.97MB, end 223.45MB, increase: -1555.24KB
2025-9-2 12:47:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:47:30-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-2 12:47:30-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-2 12:47:30-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-2 12:47:30-debug: [Build Memory track]: 查询 Asset Bundle start:223.48MB, end 223.75MB, increase: 275.05KB
2025-9-2 12:47:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:47:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:47:30-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:47:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.78MB, end 223.80MB, increase: 16.55KB
2025-9-2 12:47:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:47:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:47:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:47:30-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:47:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:47:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:223.82MB, end 223.85MB, increase: 25.62KB
2025-9-2 12:47:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:47:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:47:30-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:47:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.88MB, end 224.17MB, increase: 294.41KB
2025-9-2 12:50:56-debug: refresh db internal success
2025-9-2 12:50:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:50:56-debug: refresh db assets success
2025-9-2 12:50:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:50:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:50:56-debug: asset-db:refresh-all-database (138ms)
2025-9-2 12:50:57-debug: Query all assets info in project
2025-9-2 12:50:57-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:50:57-debug: Skip compress image, progress: 0%
2025-9-2 12:50:57-debug: Init all bundles start..., progress: 0%
2025-9-2 12:50:57-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:50:57-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:50:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:50:57-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:50:57-debug:   Number of all scenes: 8
2025-9-2 12:50:57-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:50:57-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:50:57-debug:   Number of all scripts: 274
2025-9-2 12:50:57-debug:   Number of other assets: 2044
2025-9-2 12:50:57-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-2 12:50:57-debug: [Build Memory track]: 查询 Asset Bundle start:206.74MB, end 207.72MB, increase: 1003.86KB
2025-9-2 12:50:57-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:50:57-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-2 12:50:57-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:50:57-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 10%
2025-9-2 12:50:57-debug: [Build Memory track]: 查询 Asset Bundle start:207.75MB, end 208.02MB, increase: 280.02KB
2025-9-2 12:50:57-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-2 12:50:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:50:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:50:57-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-2 12:50:57-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:50:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.05MB, end 208.06MB, increase: 16.17KB
2025-9-2 12:50:57-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:50:57-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-2 12:50:57-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:50:57-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.09MB, end 208.11MB, increase: 16.50KB
2025-9-2 12:50:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:50:57-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:50:57-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.14MB, end 208.43MB, increase: 295.75KB
2025-9-2 12:50:57-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 12:52:29-debug: refresh db internal success
2025-9-2 12:52:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 12:52:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 12:52:29-debug: refresh db assets success
2025-9-2 12:52:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 12:52:29-debug: asset-db:refresh-all-database (135ms)
2025-9-2 12:52:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 12:52:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-2 12:52:30-debug: Query all assets info in project
2025-9-2 12:52:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-2 12:52:30-debug: Skip compress image, progress: 0%
2025-9-2 12:52:30-debug: Init all bundles start..., progress: 0%
2025-9-2 12:52:30-debug: Num of bundles: 3..., progress: 0%
2025-9-2 12:52:30-debug: 查询 Asset Bundle start, progress: 0%
2025-9-2 12:52:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:52:30-debug: Init bundle root assets start..., progress: 0%
2025-9-2 12:52:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-2 12:52:30-debug:   Number of other assets: 2044
2025-9-2 12:52:30-debug:   Number of all scenes: 8
2025-9-2 12:52:30-debug:   Number of all scripts: 274
2025-9-2 12:52:30-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-2 12:52:30-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-2 12:52:30-debug: 查询 Asset Bundle start, progress: 5%
2025-9-2 12:52:30-debug: Init bundle root assets success..., progress: 0%
2025-9-2 12:52:30-debug: [Build Memory track]: 查询 Asset Bundle start:210.05MB, end 210.75MB, increase: 716.32KB
2025-9-2 12:52:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-2 12:52:30-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-2 12:52:30-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-2 12:52:30-debug: [Build Memory track]: 查询 Asset Bundle start:210.78MB, end 211.05MB, increase: 276.23KB
2025-9-2 12:52:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-2 12:52:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:52:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-2 12:52:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.08MB, end 211.11MB, increase: 25.42KB
2025-9-2 12:52:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-2 12:52:30-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-2 12:52:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-2 12:52:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-2 12:52:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.13MB, end 211.16MB, increase: 25.84KB
2025-9-2 12:52:30-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-2 12:52:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-2 12:52:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-2 12:52:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-2 12:52:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.19MB, end 211.47MB, increase: 291.93KB
2025-9-2 12:52:30-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-2 13:01:40-debug: refresh db internal success
2025-9-2 13:01:40-debug: refresh db assets success
2025-9-2 13:01:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 13:01:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 13:01:40-debug: asset-db:refresh-all-database (107ms)
2025-9-2 14:38:06-debug: refresh db internal success
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 14:38:06-debug: refresh db assets success
2025-9-2 14:38:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 14:38:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 14:38:06-debug: asset-db:refresh-all-database (152ms)
2025-9-2 14:38:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 14:38:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 15:21:46-debug: refresh db internal success
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\player
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\weapon
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\bg_layer
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\event_group
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Background.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Anim.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Enemy.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\EnemyBullet.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\GameOver.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\GamePersistNode.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Global.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Goods.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\MainGame.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Menu.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\PlayerBullet.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\Player.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\AnimFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\EnemyFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\EnemyBulletFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\GameFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\GoodsFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\factroy\PlayerBulletFactory.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos\EmitterArcGizmo.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos\GizmoDrawer.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos\GizmoManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos\index.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\Bootstrap.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\gizmos\GizmoUtils.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\WorldInitializeData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\Entity.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\EntityContainer.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\EventCondition.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\EventGroup.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\EventAction.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\Messaging.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\Object.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\System.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\SystemContainer.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\TypeID.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\World.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\LevelEventGroup.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\Level.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\LevelSystem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\player\PlayerSystem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\weapon\Weapon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\bg_layer\BackgroundLayer.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\bg_layer\Background.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\event_group\ChangeBackgroundAction.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\weapon\WeaponSlot.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\event_group\ChangeBackgroundSpeedAction.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\event_group\LogEventAction.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\event_group\LevelTimeEventCondition.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\GameOld\world\level\LevelDesign.md
background: #ffb8b8; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:21:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:21:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 15:21:46-debug: refresh db assets success
2025-9-2 15:21:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 15:21:46-debug: asset-db:refresh-all-database (180ms)
2025-9-2 15:21:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 15:21:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 15:58:46-debug: refresh db internal success
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 15:58:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 15:58:46-debug: refresh db assets success
2025-9-2 15:58:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 15:58:46-debug: asset-db:refresh-all-database (138ms)
2025-9-2 15:58:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 15:58:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 16:06:33-debug: refresh db internal success
2025-9-2 16:06:33-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 16:06:33-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-2 16:06:33-debug: refresh db assets success
2025-9-2 16:06:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 16:06:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 16:06:33-debug: asset-db:refresh-all-database (115ms)
2025-9-2 16:06:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-2 16:06:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-2 16:06:37-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-2 16:06:37-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-2 16:08:45-debug: refresh db internal success
2025-9-2 16:08:45-debug: refresh db assets success
2025-9-2 16:08:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-2 16:08:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-2 16:08:45-debug: asset-db:refresh-all-database (121ms)
