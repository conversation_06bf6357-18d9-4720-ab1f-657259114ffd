export enum eEmitterCondition {
    Level_Duration = 1,     // 已持续时间
    Level_Distance,         // 已飞行距离
    Level_InfLevel,         // 无尽模式当前关卡等级
    Level_ChallengeLevel,   // 闯关模式当前等级

    Player_ActLevel,        // 玩家账号等级
    Player_PosX,            // 玩家当前坐标X
    Player_PosY,            // 玩家当前坐标Y
    Player_LifePercent,     // 玩家当前生命百分比
    Player_GainBuff,        // 玩家获得buff

    Unit_Life,              // 单位当前生命值
    Unit_LifePercent,       // 单位当前生命百分比
    Unit_Duration,          // 单位当前持续时间
    Unit_PosX,              // 单位当前坐标X
    Unit_PosY,              // 单位当前坐标Y
    Unit_Speed,             // 单位当前速度
    Unit_SpeedAngle,        // 单位当前速度角度
    Unit_Acceleration,      // 单位当前加速度
    Unit_AccelerationAngle, // 单位当前加速度角度
    Unit_DistanceToPlayer,  // 单位与玩家的距离
    Unit_AngleToPlayer,     // 单位与玩家的角度

    Emitter_Active,         // 发射器是否启用
    Emitter_InitialDelay,   // 发射器当前的初始延迟
    Emitter_Prewarm,        // 发射器是否启用预热
    Emitter_PrewarmDuration, // 发射器预热的持续时间
    Emitter_Duration,       // 发射器配置的持续时间
    Emitter_ElapsedTime,    // 发射器已运行的时间
    Emitter_Loop,           // 发射器是否循环
    Emitter_LoopInterval,   // 发射器循环的间隔时间

    Emitter_EmitInterval,   // 发射器开火间隔
    Emitter_EmitCount,      // 发射器开火次数
    Emitter_EmitOffsetX,    // 发射器开火偏移

    Emitter_Angle,          // 发射器弹道角度
    Emitter_Count,          // 发射器弹道数量

    Bullet_Sprite,          // 外观,这个需要id或者路径
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_Duration,
    Bullet_ElapsedTime,
    Bullet_Speed,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_FacingMoveDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export enum eBulletCondition {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_FacingMoveDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export enum eEmitterActionType {
    Emitter_Active = 1,         // 发射器是否启用
    Emitter_InitialDelay,       // 发射器当前的初始延迟
    Emitter_Prewarm,            // 发射器是否启用预热
    Emitter_PrewarmDuration,    // 发射器预热的持续时间
    Emitter_Duration,           // 发射器配置的持续时间
    Emitter_ElapsedTime,        // 发射器已运行的时间
    Emitter_Loop,               // 发射器是否循环
    Emitter_LoopInterval,       // 发射器循环的间隔时间

    Emitter_PerEmitCount,      // 发射器开火次数
    Emitter_PerEmitInterval,   // 发射器开火间隔
    Emitter_PerEmitOffsetX,    // 发射器开火偏移

    Emitter_Angle,             // 发射器弹道角度
    Emitter_Count,             // 发射器弹道数量

    Bullet_Duration,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_ColorA,
    Bullet_FacingMoveDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
    
    Unit_Life,
    Unit_LifePercent,
    Unit_PosX,
    Unit_PosY,
    Unit_Speed,
    Unit_SpeedAngle,
    Unit_Acceleration,
    Unit_AccelerationAngle,
}

/**
 * ActionType对应要修改的属性
 * 以下是子弹的行为
 */
export enum eBulletActionType {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_ColorA,
    Bullet_FacingMoveDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}