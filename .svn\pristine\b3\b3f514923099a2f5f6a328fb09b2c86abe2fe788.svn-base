"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventGroupDataManager = exports.ActionDefByCategory = exports.ConditionDefByCategory = exports.EventGroupCategory = exports.BulletActionDef = exports.EmitterActionDef = exports.BulletConditionDef = exports.EmitterConditionDef = exports.EasingEnum = exports.CompareOpEnum = exports.ConditionOpEnum = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const GameEnumDef_1 = require("./GameEnumDef");
;
// Enum definitions for UI display
exports.ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};
exports.CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};
// These should match your actual game enums defined in EventConditionType.ts
exports.EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};
exports.EmitterConditionDef = [
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_PrewarmDuration, label: "发射器预热时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Duration, label: "发射器持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_ElapsedTime, label: "发射器运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitCount, label: "发射器开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitInterval, label: "发射器开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitOffsetX, label: "发射器开火X偏移", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Angle, label: "发射器弹道角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Count, label: "发射器弹道数量", type: "number" },
    // 子弹
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Life, label: "单位生命", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_LifePercent, label: "单位生命百分比", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosX, label: "单位X坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosY, label: "单位Y坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Speed, label: "单位速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_SpeedAngle, label: "单位速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Acceleration, label: "单位加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_AccelerationAngle, label: "单位加速度角度", type: "number" },
];
exports.BulletConditionDef = [
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
exports.EmitterActionDef = [
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PrewarmDuration, label: "发射器预热持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Duration, label: "发射器持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_ElapsedTime, label: "发射器已运行时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitCount, label: "发射器每次开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitInterval, label: "发射器每次开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitOffsetX, label: "发射器每次开火偏移X", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Angle, label: "发射器角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Count, label: "发射器数量", type: "number" },
    // 子弹相关
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位相关
];
exports.BulletActionDef = [
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
var EventGroupCategory;
(function (EventGroupCategory) {
    EventGroupCategory["Emitter"] = "Emitter";
    EventGroupCategory["Bullet"] = "Bullet";
})(EventGroupCategory || (exports.EventGroupCategory = EventGroupCategory = {}));
// New definition-based mappings using ValueDecorator
exports.ConditionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterConditionDef,
    [EventGroupCategory.Bullet]: exports.BulletConditionDef,
};
exports.ActionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterActionDef,
    [EventGroupCategory.Bullet]: exports.BulletActionDef,
};
class EventGroupDataManager {
    constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = (0, path_1.join)(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }
    static getInstance() {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }
    /**
     * Get the full path for a category folder
     */
    getCategoryPath(category) {
        return (0, path_1.join)(this.eventsBasePath, category);
    }
    /**
     * Get the full file path for an EventGroupData
     */
    getFilePath(category, name) {
        return (0, path_1.join)(this.getCategoryPath(category), `${name}.json`);
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(dirPath) {
        if (!(0, fs_1.existsSync)(dirPath)) {
            (0, fs_1.mkdirSync)(dirPath, { recursive: true });
        }
    }
    /**
     * Load all EventGroupData files from a category
     */
    loadEventGroupsByCategory(category) {
        const categoryPath = this.getCategoryPath(category);
        if (!(0, fs_1.existsSync)(categoryPath)) {
            return [];
        }
        const files = (0, fs_1.readdirSync)(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups = [];
        for (const file of files) {
            try {
                const filePath = (0, path_1.join)(categoryPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
                const data = JSON.parse(content);
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            }
            catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }
        return eventGroups;
    }
    /**
     * Load a specific EventGroupData by name and category
     */
    loadEventGroup(category, name) {
        const filePath = this.getFilePath(category, name);
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        try {
            const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
            const data = JSON.parse(content);
            data.name = name; // Ensure name is correct
            return data;
        }
        catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }
    /**
     * Save an EventGroupData to file
     */
    saveEventGroup(category, eventGroup) {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
            return true;
        }
        catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }
    /**
     * Delete an EventGroupData file
     */
    deleteEventGroup(category, name) {
        try {
            const filePath = this.getFilePath(category, name);
            if ((0, fs_1.existsSync)(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }
    /**
     * Check if an EventGroupData file exists
     */
    eventGroupExists(category, name) {
        const filePath = this.getFilePath(category, name);
        return (0, fs_1.existsSync)(filePath);
    }
    /**
     * Find an EventGroupData by name across all categories
     */
    findEventGroup(name) {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }
    /**
     * Generate a unique name for a new EventGroupData
     */
    generateUniqueName(category, baseName = 'EventGroup') {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        return name;
    }
    /**
     * Create a new EventGroupData with default values
     */
    createNewEventGroup(category, name) {
        const finalName = name || this.generateUniqueName(category);
        var eventGroup = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };
        return eventGroup;
    }
    /**
     * Duplicate an existing EventGroupData
     */
    duplicateEventGroup(category, originalName, newName) {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }
        const finalName = newName || this.generateUniqueName(category, originalName);
        // Deep copy the original data
        const duplicate = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => (Object.assign({}, condition))),
            actions: original.actions.map(action => (Object.assign({}, action)))
        };
        return duplicate;
    }
    /**
     * Get all EventGroupData names from all categories
     */
    getAllEventGroupNames() {
        const result = {};
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        return result;
    }
}
exports.EventGroupDataManager = EventGroupDataManager;
//# sourceMappingURL=data:application/json;base64,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