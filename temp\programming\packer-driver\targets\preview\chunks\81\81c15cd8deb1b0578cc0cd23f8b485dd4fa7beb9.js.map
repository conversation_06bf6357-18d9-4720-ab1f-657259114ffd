{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "resources", "Prefab", "EDITOR", "BulletController", "EmitterData", "BulletData", "ObjectPool", "BulletSystem", "EventGroup", "EventGroupContext", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "playOnFocus", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "Emitter", "displayName", "type", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitBulletID", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "totalElapsedTime", "updateInEditor", "_status", "None", "_statusElapsedTime", "_isEmitting", "_nextEmitTime", "_timeAccumulator", "_fixedDel<PERSON>", "_perEmitBulletQueue", "_eventGroups", "isEmitting", "status", "statusElapsedTime", "start", "onCreateEmitter", "resetProperties", "createEventGroups", "update", "dt", "tick", "tickBullets", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "emitterData", "clear", "addProperty", "IsActive", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PreWarmDuration", "EmitBulletID", "bulletID", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "TotalElapsedTime", "on", "value", "notifyAll", "eventGroupData", "length", "ctx", "emitter", "dataName", "eventGroup", "loadEmitterEventGroup", "push", "changeStatus", "Emitting", "Prewarm", "for<PERSON>ach", "group", "stop", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "i", "j", "targetTime", "index", "perEmitIndex", "sort", "a", "b", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "console", "warn", "createBulletInEditor", "bullet", "instantiateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "defaultBullet", "load", "err", "prefab", "error", "bulletNode", "getNode", "getComponent", "destroy", "name", "kBulletNameInEditor", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "notify"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAmBC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;;AAChDC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,WAAxC;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA;AAAvE,O,GAAiFlB,U;OACjF;AAAEmB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCnB,I;;gCAEnCoB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;yBAcCC,O,WANZV,OAAO,CAAC,SAAD,C,UAEPK,IAAI,CAAC,UAAD,C,UAGJD,gBAAgB,CAAC,IAAD,C,UAKZF,QAAQ,CAAC;AAACS,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERT,QAAQ,CAAC;AAACU,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,UAERT,QAAQ,CAAC;AAACU,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBD,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,+BAXZV,iB,UACAE,W,+CAJD,MAMaO,OANb;AAAA;AAAA,oEAMsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWxDG,aAXwD,GAWhC,IAXgC;AAAA,eAYxDC,oBAZwD,GAYzB,IAZyB;AAAA,eAaxDC,iBAbwD,GAa5B,IAb4B;AAelE;AAfkE,eAgB3DC,QAhB2D;AAAA,eAiB3DC,cAjB2D;AAAA,eAkB3DC,SAlB2D;AAAA,eAmB3DC,MAnB2D;AAAA,eAoB3DC,YApB2D;AAAA,eAqB3DC,eArB2D;AAAA,eAsB3DC,YAtB2D;AAAA,eAuB3DC,YAvB2D;AAAA,eAwB3DC,YAxB2D;AAAA,eAyB3DC,SAzB2D;AAAA,eA0B3DC,YA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,eA5B2D;AAAA,eA6B3DC,cA7B2D;AAAA,eA8B3DC,KA9B2D;AAAA,eA+B3DC,KA/B2D;AAAA,eAgC3DC,GAhC2D;AAAA,eAiC3DC,MAjC2D;AAAA,eAkC3DC,gBAlC2D;AAAA,eAoC3DC,cApC2D,GAoChC,KApCgC;AAoCxB;AApCwB,eAqCxDC,OArCwD,GAqC9B5B,cAAc,CAAC6B,IArCe;AAAA,eAsCxDC,kBAtCwD,GAsC3B,CAtC2B;AAAA,eAuCxDC,WAvCwD,GAuCjC,KAvCiC;AAAA,eAwCxDC,aAxCwD,GAwChC,CAxCgC;AAAA,eAyCxDC,gBAzCwD,GAyC9B,CAzC8B;AAyC3B;AAzC2B,eA0CxDC,WA1CwD,GA0CnC,MA1CmC;AA0C3B;AAEvC;AA5CkE,eA6CxDC,mBA7CwD,GA6CgC,EA7ChC;AAAA,eA8CxDC,YA9CwD,GA8C3B,EA9C2B;AAAA;;AAgDpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKN,WAAZ;AAA0B;;AAC5C,YAANO,MAAM,GAAmB;AAAE,iBAAO,KAAKV,OAAZ;AAAsB;;AAChC,YAAjBW,iBAAiB,GAAW;AAAE,iBAAO,KAAKT,kBAAZ;AAAiC;;AAEzDU,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACA,eAAKC,eAAL;AACA,eAAKC,iBAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChC,cAAI9D,MAAM,IAAI,KAAK4C,cAAnB,EAAmC;AAC/B;AAEA;AACA;AAEI,iBAAKmB,IAAL,CAAU,KAAKZ,WAAf;AACA;AAAA;AAAA,8CAAaa,WAAb,CAAyB,KAAKb,WAA9B,EAP2B,CAQ3B;AACJ;AACH;AACJ;;AAEMc,QAAAA,aAAa,GAAG;AACnB,eAAKrB,cAAL,GAAsB,IAAtB;AACH;;AAEMsB,QAAAA,eAAe,GAAG;AACrB,eAAKtB,cAAL,GAAsB,IAAtB;AACA,eAAKe,eAAL;AACH;;AAEMQ,QAAAA,mBAAmB,GAAG;AACzB,eAAKvB,cAAL,GAAsB,KAAtB;;AACA,cAAI;AAAA;AAAA,4CAAawB,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ,SAtFiE,CAwFlE;;;AACUX,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKY,WAAV,EAAuB;AAEvB,eAAKC,KAAL;AACA,eAAK/C,QAAL,GAAgB,KAAKgD,WAAL,CAAiBvD,YAAY,CAACwD,QAA9B,EAAwC,IAAxC,CAAhB;AACA,eAAKhD,cAAL,GAAsB,KAAK+C,WAAL,CAAiBvD,YAAY,CAACyD,cAA9B,EAA8C,KAAKJ,WAAL,CAAiB7C,cAA/D,CAAtB;AACA,eAAKC,SAAL,GAAiB,KAAK8C,WAAL,CAAiBvD,YAAY,CAAC0D,SAA9B,EAAyC,KAAKL,WAAL,CAAiB5C,SAA1D,CAAjB;AACA,eAAKC,MAAL,GAAc,KAAK6C,WAAL,CAAiBvD,YAAY,CAAC2D,MAA9B,EAAsC,KAAKN,WAAL,CAAiB3C,MAAvD,CAAd;AACA,eAAKC,YAAL,GAAoB,KAAK4C,WAAL,CAAiBvD,YAAY,CAAC4D,YAA9B,EAA4C,KAAKP,WAAL,CAAiB1C,YAA7D,CAApB;AACA,eAAKC,eAAL,GAAuB,KAAK2C,WAAL,CAAiBvD,YAAY,CAAC6D,eAA9B,EAA+C,KAAKR,WAAL,CAAiBzC,eAAhE,CAAvB;AACA,eAAKC,YAAL,GAAoB,KAAK0C,WAAL,CAAiBvD,YAAY,CAAC8D,YAA9B,EAA4C,KAAKC,QAAjD,CAApB;AACA,eAAKjD,YAAL,GAAoB,KAAKyC,WAAL,CAAiBvD,YAAY,CAACgE,YAA9B,EAA4C,KAAKX,WAAL,CAAiBvC,YAA7D,CAApB;AACA,eAAKC,YAAL,GAAoB,KAAKwC,WAAL,CAAiBvD,YAAY,CAACiE,YAA9B,EAA4C,KAAKZ,WAAL,CAAiBtC,YAA7D,CAApB;AACA,eAAKC,SAAL,GAAiB,KAAKuC,WAAL,CAAiBvD,YAAY,CAACkE,SAA9B,EAAyC,KAAKb,WAAL,CAAiBrC,SAA1D,CAAjB;AACA,eAAKC,YAAL,GAAoB,KAAKsC,WAAL,CAAiBvD,YAAY,CAACmE,YAA9B,EAA4C,KAAKd,WAAL,CAAiBpC,YAA7D,CAApB;AACA,eAAKC,YAAL,GAAoB,KAAKqC,WAAL,CAAiBvD,YAAY,CAACoE,YAA9B,EAA4C,KAAKf,WAAL,CAAiBnC,YAA7D,CAApB;AACA,eAAKC,eAAL,GAAuB,KAAKoC,WAAL,CAAiBvD,YAAY,CAACqE,eAA9B,EAA+C,KAAKhB,WAAL,CAAiBlC,eAAhE,CAAvB;AACA,eAAKC,cAAL,GAAsB,KAAKmC,WAAL,CAAiBvD,YAAY,CAACsE,cAA9B,EAA8C,KAAKjB,WAAL,CAAiBjC,cAA/D,CAAtB;AACA,eAAKC,KAAL,GAAa,KAAKkC,WAAL,CAAiBvD,YAAY,CAACuE,KAA9B,EAAqC,KAAKlB,WAAL,CAAiBhC,KAAtD,CAAb;AACA,eAAKC,KAAL,GAAa,KAAKiC,WAAL,CAAiBvD,YAAY,CAACwE,KAA9B,EAAqC,KAAKnB,WAAL,CAAiB/B,KAAtD,CAAb;AACA,eAAKC,GAAL,GAAW,KAAKgC,WAAL,CAAiBvD,YAAY,CAACyE,GAA9B,EAAmC,KAAKpB,WAAL,CAAiB9B,GAApD,CAAX;AACA,eAAKC,MAAL,GAAc,KAAK+B,WAAL,CAAiBvD,YAAY,CAAC0E,MAA9B,EAAsC,KAAKrB,WAAL,CAAiB7B,MAAvD,CAAd;AACA,eAAKC,gBAAL,GAAwB,KAAK8B,WAAL,CAAiBvD,YAAY,CAAC2E,gBAA9B,EAAgD,CAAhD,CAAxB;AAEA,eAAK9D,YAAL,CAAkB+D,EAAlB,CAAsBC,KAAD,IAAW;AAC5B;AACA,iBAAKzE,aAAL,GAAqB,IAArB;AACH,WAHD;AAIA,eAAKG,QAAL,CAAcqE,EAAd,CAAkBC,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW,CAEV,CAFD,MAEO,CAEN;AACJ,WAND;AAQA,eAAKC,SAAL,CAAe,IAAf;AACH;;AAESpC,QAAAA,iBAAiB,GAAS;AAChC,cAAI,CAAC,KAAKW,WAAN,IAAqB,KAAKA,WAAL,CAAiB0B,cAAjB,CAAgCC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,cAAIC,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;;AACA,eAAK,IAAMC,QAAX,IAAuB,KAAK9B,WAAL,CAAiB0B,cAAxC,EAAwD;AACpD,gBAAMK,UAAU,GAAG;AAAA;AAAA,0CAAeH,GAAf,EAAoB;AAAA;AAAA,8CAAaI,qBAAb,CAAmCF,QAAnC,CAApB,CAAnB;;AACA,iBAAKhD,YAAL,CAAkBmD,IAAlB,CAAuBF,UAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACIG,QAAAA,YAAY,CAAClD,MAAD,EAAyB;AACjC,eAAKV,OAAL,GAAeU,MAAf;AACA,eAAKR,kBAAL,GAA0B,CAA1B;AACA,eAAKE,aAAL,GAAqB,CAArB,CAHiC,CAIjC;;AACA,eAAKG,mBAAL,GAA2B,EAA3B;;AACA,cAAIG,MAAM,KAAKtC,cAAc,CAACyF,QAA1B,IAAsCnD,MAAM,KAAKtC,cAAc,CAAC0F,OAApE,EAA6E;AACzE,gBAAI,KAAKtD,YAAL,CAAkB6C,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,mBAAK7C,YAAL,CAAkBuD,OAAlB,CAA0BC,KAAK,IAAIA,KAAK,CAACpD,KAAN,EAAnC;AACH;AACJ,WAJD,MAKK;AACD,gBAAI,KAAKJ,YAAL,CAAkB6C,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,mBAAK7C,YAAL,CAAkBuD,OAAlB,CAA0BC,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAnC;AACH;AACJ;AACJ;;AAESC,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAK9D,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,KAAKd,YAAL,CAAkB8D,KAAjE;AACH;;AAESiB,QAAAA,aAAa,GAAG;AACtB,eAAKhE,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAESiE,QAAAA,YAAY,GAAG;AACrB,eAAKjE,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKkE,sBAAL,GAHqB,CAIrB;;AACA,eAAK9D,mBAAL,GAA2B,EAA3B;AACH;;AAES+D,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,cAAI,KAAK/E,eAAL,CAAqB0D,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7E,KAAL,CAAWuD,KAA/B,EAAsCsB,CAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlF,YAAL,CAAkB2D,KAAtC,EAA6CuB,CAAC,EAA9C,EAAkD;AAC9C,oBAAMC,UAAU,GAAG,KAAKxE,kBAAL,GAA2B,KAAKV,eAAL,CAAqB0D,KAArB,GAA6BuB,CAA3E;;AACA,qBAAKlE,mBAAL,CAAyBoD,IAAzB,CAA8B;AAC1BgB,kBAAAA,KAAK,EAAEH,CADmB;AAE1BI,kBAAAA,YAAY,EAAEH,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ,aAX+B,CAahC;;;AACA,iBAAKnE,mBAAL,CAAyBsE,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACJ,UAAF,GAAeK,CAAC,CAACL,UAAzD;AACH,WAfD,MAgBK;AACD;AACA,iBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAK7E,KAAL,CAAWuD,KAA/B,EAAsCsB,EAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKlF,YAAL,CAAkB2D,KAAtC,EAA6CuB,EAAC,EAA9C,EAAkD;AAC9C,qBAAKO,UAAL,CAAgBR,EAAhB,EAAmBC,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESQ,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAK1E,mBAAL,CAAyB8C,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAM6B,UAAU,GAAG,KAAK3E,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKL,kBAAL,IAA2BgF,UAAU,CAACR,UAA1C,EAAsD;AAClD;AACA,mBAAKnE,mBAAL,CAAyB4E,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACP,KAA3B,EAAkCO,UAAU,CAACN,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESQ,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKd,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESS,QAAAA,UAAU,CAACL,KAAD,EAAeC,YAAf,EAAqC;AACrD,cAAMS,SAAS,GAAG,KAAKC,iBAAL,CAAuBX,KAAvB,CAAlB;AACA,cAAMY,QAAQ,GAAG,KAAKC,gBAAL,CAAsBb,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKa,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACX,KAAD,EAA0C;AACvD;AACA,cAAMe,WAAW,GAAG,KAAK/F,KAAL,CAAWuD,KAAX,GAAmB,CAAnB,GAAwB,KAAKtD,GAAL,CAASsD,KAAT,IAAkB,KAAKvD,KAAL,CAAWuD,KAAX,GAAmB,CAArC,CAAD,GAA4CyB,KAA5C,GAAoD,KAAK/E,GAAL,CAASsD,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,cAAMyC,MAAM,GAAGzH,gBAAgB,CAAC,KAAKwB,KAAL,CAAWwD,KAAX,GAAmBwC,WAApB,CAA/B;AACA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACb,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,cAAMnF,cAAc,GAAI,KAAKF,YAAL,CAAkB2D,KAAlB,GAA0B,CAA1B,GAA+B,KAAKzD,cAAL,CAAoByD,KAApB,IAA6B,KAAK3D,YAAL,CAAkB2D,KAAlB,GAA0B,CAAvD,CAAD,GAA8D0B,YAA9D,GAA6E,KAAKnF,cAAL,CAAoByD,KAApB,GAA4B,CAAvI,GAA2I,CAAnK;;AACA,cAAI,KAAKrD,MAAL,CAAYqD,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAE0C,cAAAA,CAAC,EAAEnG,cAAL;AAAqBsG,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBX,KAAvB,CAAlB;AACA,iBAAO;AACHiB,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAK/F,MAAL,CAAYqD,KAA1B,GAAkCzD,cADlC;AAEHsG,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKlG,MAAL,CAAYqD;AAF1B,WAAP;AAIH;;AAEDuC,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAK9G,aAAV,EAAyB;AACrBwH,YAAAA,OAAO,CAACC,IAAR,CAAa,oCAAb;;AACA,gBAAI/I,MAAJ,EAAY;AACR,mBAAKgJ,oBAAL,CAA0Bd,SAA1B,EAAqCE,QAArC;AACH;;AACD;AACH;;AAED,cAAMa,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa,OAV2E,CAYxF;;AACA,cAAME,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAJ,UAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACV,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIU,UAAU,CAACP,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIO,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4B,IAA5B,EAAkCP,MAAlC,EApBwF,CAsBxF;;AACAA,UAAAA,MAAM,CAACQ,KAAP,CAAaC,UAAb,GAA0B1I,gBAAgB,CAAC0H,IAAI,CAACiB,KAAL,CAAWzB,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAQ,UAAAA,MAAM,CAACQ,KAAP,CAAaG,KAAb,IAAsB,KAAK1H,SAAL,CAAe6D,KAArC,CAxBwF,CAyBxF;AACH;;AAESiD,QAAAA,oBAAoB,CAACd,SAAD,EAAsCE,QAAtC,EAA0E;AACpG;AACA,cAAMyB,aAAa,GAAG,yBAAtB,CAFoG,CAGpG;;AACA/J,UAAAA,SAAS,CAACgK,IAAV,CAAeD,aAAf,EAA8B9J,MAA9B,EAAsC,CAACgK,GAAD,EAAMC,MAAN,KAAiB;AACnD,gBAAID,GAAJ,EAAS;AACLjB,cAAAA,OAAO,CAACmB,KAAR,CAAcF,GAAd;AACA;AACH;;AACD,iBAAKzI,aAAL,GAAqB0I,MAArB;AACA,gBAAMf,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,gBAAI,CAACD,MAAL,EAAa,OAPsC,CASnD;;AACA,gBAAME,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAJ,YAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACV,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIU,UAAU,CAACP,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIO,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,8CAAaC,cAAb,CAA4B,IAA5B,EAAkCP,MAAlC,EAjBmD,CAmBnD;;AACAA,YAAAA,MAAM,CAACQ,KAAP,CAAaC,UAAb,GAA0B1I,gBAAgB,CAAC0H,IAAI,CAACiB,KAAL,CAAWzB,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAQ,YAAAA,MAAM,CAACQ,KAAP,CAAaG,KAAb,IAAsB,KAAK1H,SAAL,CAAe6D,KAArC;AACH,WAtBD;AAuBH;;AAESmD,QAAAA,iBAAiB,GAA4B;AACnD,cAAMgB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAa/F,YAAhC,EAA8C,KAAK9C,aAAnD,CAAnB;;AACA,cAAI,CAAC4I,UAAL,EAAiB;AACbpB,YAAAA,OAAO,CAACmB,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WALkD,CAOnD;;;AACA,cAAMhB,MAAM,GAAGiB,UAAU,CAACE,YAAX;AAAA;AAAA,mDAAf;;AACA,cAAI,CAACnB,MAAL,EAAa;AACTH,YAAAA,OAAO,CAACmB,KAAR,CAAc,uDAAd;AACAC,YAAAA,UAAU,CAACG,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIrK,MAAJ,EAAY;AACRkK,YAAAA,UAAU,CAACI,IAAX,GAAkBnJ,OAAO,CAACoJ,mBAA1B;AACH;;AAED,iBAAOtB,MAAP;AACH;;AAEDuB,QAAAA,UAAU,CAACR,MAAD,EAAiB5B,QAAjB,EAAiCqC,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACV,MAAL,EAAa;AAEb,cAAMW,UAAU,GAAG;AAAA;AAAA,wCAAWR,OAAX,CAAmB,KAAKf,IAAxB,EAA8BY,MAA9B,CAAnB;AACA,cAAI,CAACW,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAACrB,gBAAX,CAA4BlB,QAA5B;AACAuC,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMhH,QAAAA,IAAI,CAACiH,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKvJ,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAcsE,KAArC,EAA4C;AACxC;AACH;;AAED,eAAKhD,kBAAL,IAA2BiI,SAA3B;AACA,eAAKrI,gBAAL,CAAsBoD,KAAtB,IAA+BiF,SAA/B;;AAEA,kBAAQ,KAAKnI,OAAb;AAEI,iBAAK5B,cAAc,CAAC6B,IAApB;AACI,mBAAKmI,gBAAL;AACA;;AACJ,iBAAKhK,cAAc,CAAC0F,OAApB;AACI,mBAAKuE,mBAAL;AACA;;AACJ,iBAAKjK,cAAc,CAACyF,QAApB;AACI,mBAAKyE,oBAAL;AACA;;AACJ,iBAAKlK,cAAc,CAACmK,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKpK,cAAc,CAACqK,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESN,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKlI,kBAAL,IAA2B,KAAKlB,YAAL,CAAkBkE,KAAjD,EAAwD;AACpD,iBAAKU,YAAL,CAAkBxF,cAAc,CAAC0F,OAAjC;AACH;AACJ;;AAESuE,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKvJ,SAAL,CAAeoE,KAApB,EACI,KAAKU,YAAL,CAAkBxF,cAAc,CAACyF,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK3D,kBAAL,IAA2B,KAAKjB,eAAL,CAAqBiE,KAApD,EAA2D;AACvD,mBAAKU,YAAL,CAAkBxF,cAAc,CAACyF,QAAjC;AACH;AACJ;AACJ;;AAESyE,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKpI,kBAAL,GAA0B,KAAKf,YAAL,CAAkB+D,KAAhD,EAAuD;AACnD,iBAAKkB,YAAL;AACA,gBAAI,KAAKrF,MAAT,EACI,KAAK6E,YAAL,CAAkBxF,cAAc,CAACmK,cAAjC,EADJ,KAGI,KAAK3E,YAAL,CAAkBxF,cAAc,CAACqK,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKtI,WAAV,EAAuB;AACnB,iBAAKgE,aAAL;AACH,WAFD,MAGK,IAAI,KAAKhE,WAAL,IAAoB,KAAKD,kBAAL,IAA2B,KAAKE,aAAxD,EAAuE;AACxE;AACA,iBAAKgF,OAAL;AACA,iBAAKlB,gBAAL;AACH,WAlB4B,CAoB7B;;;AACA,eAAKe,mBAAL;AACH;;AAESuD,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKtI,kBAAL,IAA2B,KAAKZ,YAAL,CAAkB4D,KAAjD,EAAwD;AACpD,iBAAKU,YAAL,CAAkBxF,cAAc,CAACyF,QAAjC;AACH;AACJ;;AAES6E,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAK9J,QAAL,CAAcsE,KAAd,GAAsB,KAAtB;AACA,eAAKtE,QAAL,CAAc+J,MAAd;AACH;;AA3ciE,O,UAE3DjB,mB,GAA6B,U;;;;;iBAGV,C;;;;;;;iBAEU,I;;;;;;;iBAEF,I", "sourcesContent": ["import { _decorator, misc, instantiate, resources, Node, Prefab, Component, Vec3, Quat } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletController } from './BulletController';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\n\r\nconst { ccclass, executeInEditMode, property, playOnFocus, disallowMultiple, menu  } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, \r\n    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX, \r\n    Angle, Count, Arc, Radius,\r\n    TotalElapsedTime, \r\n}\r\n\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode\r\n@playOnFocus\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({displayName: \"子弹ID\"})\r\n    public bulletID: number = 0;\r\n    @property({type: EmitterData, displayName: \"发射器属性\"})\r\n    readonly emitterData: EmitterData = null;\r\n    @property({type: BulletData, displayName: \"子弹属性\"})\r\n    readonly bulletData: BulletData = null;\r\n    \r\n    protected _bulletPrefab: Prefab = null;\r\n    protected _prewarmEffectPrefab: Prefab = null;\r\n    protected _emitEffectPrefab: Prefab = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitBulletID!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public totalElapsedTime!: Property<number>;\r\n\r\n    public updateInEditor : boolean = false;  // 是否在编辑器中更新\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _timeAccumulator:number = 0; // Accumulate delta time here\r\n    protected _fixedDelta:number = 0.0167; // Fixed time step (e.g., 60 FPS)\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n    protected _eventGroups: EventGroup[] = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n        this.resetProperties();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        if (EDITOR && this.updateInEditor) {\r\n            // this._timeAccumulator += dt;\r\n\r\n            // while (this._timeAccumulator >= this._fixedDelta) {\r\n            //     this._timeAccumulator -= this._fixedDelta; \r\n                \r\n                this.tick(this._fixedDelta);\r\n                BulletSystem.tickBullets(this._fixedDelta);\r\n                //BulletSystem.tickActionRunners(this._fixedDelta);\r\n            // }\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.clear();\r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, true);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, this.emitterData.isOnlyInScreen);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, this.emitterData.isPreWarm);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, this.emitterData.isLoop);\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, this.emitterData.initialDelay);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, this.emitterData.preWarmDuration);\r\n        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, this.emitterData.emitDuration);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, this.emitterData.emitInterval);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, this.emitterData.emitPower);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, this.emitterData.loopInterval);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, this.emitterData.perEmitCount);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, this.emitterData.perEmitInterval);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, this.emitterData.perEmitOffsetX);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, this.emitterData.angle);\r\n        this.count = this.addProperty(eEmitterProp.Count, this.emitterData.count);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, this.emitterData.arc);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, this.emitterData.radius);\r\n        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);\r\n\r\n        this.emitBulletID.on((value) => {\r\n            // TODO: reload bullet prefab\r\n            this._bulletPrefab = null;\r\n        });\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                \r\n            } else {\r\n                \r\n            }\r\n        });\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    protected createEventGroups(): void {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        for (const dataName of this.emitterData.eventGroupData) {\r\n            const eventGroup = new EventGroup(ctx, BulletSystem.loadEmitterEventGroup(dataName));\r\n            this._eventGroups.push(eventGroup);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n        if (status === eEmitterStatus.Emitting || status === eEmitterStatus.Prewarm) {\r\n            if (this._eventGroups.length > 0) {\r\n                this._eventGroups.forEach(group => group.start());\r\n            }\r\n        }\r\n        else {\r\n            if (this._eventGroups.length > 0) {\r\n                this._eventGroups.forEach(group => group.stop());\r\n            }\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.perEmitInterval.value > 0) {\r\n            // Queue all bullets with their target emission times\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n\r\n            // Sort by target time to ensure proper order\r\n            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.perEmitCount.value > 1 ? (this.perEmitOffsetX.value / (this.perEmitCount.value - 1)) * perEmitIndex - this.perEmitOffsetX.value / 2 : 0);\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius.value + perEmitOffsetX,\r\n            y: direction.y * this.radius.value\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            console.warn(\"Emitter: No bullet prefab assigned\");\r\n            if (EDITOR) {\r\n                this.createBulletInEditor(direction, position);\r\n            }\r\n            return;\r\n        }\r\n        \r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitPower.value;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n    }\r\n\r\n    protected createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const defaultBullet = 'Game/prefabs/Bullet_New';\r\n        // load using resource\r\n        resources.load(defaultBullet, Prefab, (err, prefab) => {\r\n            if (err) {\r\n                console.error(err);\r\n                return;\r\n            }\r\n            this._bulletPrefab = prefab;\r\n            const bullet = this.instantiateBullet();\r\n            if (!bullet) return;\r\n\r\n            // Set bullet position relative to emitter\r\n            const emitterPos = this.node.getWorldPosition();\r\n            bullet.node.setWorldPosition(\r\n                emitterPos.x + position.x,\r\n                emitterPos.y + position.y,\r\n                emitterPos.z\r\n            );\r\n\r\n            BulletSystem.onCreateBullet(this, bullet);\r\n\r\n            // Post set bullet properties\r\n            bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n            bullet.mover.speed *= this.emitPower.value;\r\n        });\r\n    }\r\n\r\n    protected instantiateBullet(): BulletController | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(BulletController);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime.value += deltaTime;\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}