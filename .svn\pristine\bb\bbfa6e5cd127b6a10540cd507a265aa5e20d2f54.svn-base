'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.style = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const fs_1 = require("fs");
const path_1 = require("path");
const { dialog } = require('electron');
exports.template = `
<ui-section expand>
  <ui-prop slot="header">
    <ui-label slot="label">发射器属性</ui-label>
  </ui-prop>
    <ui-prop type="dump" class="isOnlyInScreen"></ui-prop>
    <ui-prop type="dump" class="isPreWarm"></ui-prop>
    <ui-prop type="dump" class="isLoop"></ui-prop>
    <ui-prop type="dump" class="initialDelay"></ui-prop>
    <ui-prop type="dump" class="preWarmDuration"></ui-prop>
    <ui-prop type="dump" class="preWarmEffect"></ui-prop>
    <ui-prop type="dump" class="emitDuration"></ui-prop>
    <ui-prop type="dump" class="emitInterval"></ui-prop>
    <ui-prop type="dump" class="emitPower"></ui-prop>
    <ui-prop type="dump" class="loopInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitCount"></ui-prop>
    <ui-prop type="dump" class="perEmitInterval"></ui-prop>
    <ui-prop type="dump" class="perEmitOffsetX"></ui-prop>
    <ui-prop type="dump" class="angle"></ui-prop>
    <ui-prop type="dump" class="count"></ui-prop>
    <ui-prop type="dump" class="arc"></ui-prop>
    <ui-prop type="dump" class="radius"></ui-prop>
    <ui-prop type="dump" class="emitEffect"></ui-prop>

    <!-- 自定义 eventGroupData -->
    <div class="eventGroupData"></div>
</ui-section>

<!-- 额外的 bullet 相关 -->
<ui-prop type="dump" class="bulletID"></ui-prop>
<ui-prop type="dump" class="bulletData"></ui-prop>
`;
exports.style = `
.eventGroupData {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  align-items: flex-start; /* avoid children stretching full width */
  width: 100%;
}
.eventGroupItem {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px;
  width: 100%;
}
.eventGroupItem ui-select {
  flex: 1;
  min-width: 240px;
  max-width: 100%;
}
.eventGroupItem button {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
}
.eventGroupItem input {
  flex: 1;
  padding: 2px 4px;
  border: 1px solid #555;
  border-radius: 2px;
  background: #1a1a1a;
  color: white;
}
.eventGroupItem.invalid input {
  border-color: #e74c3c;
}
.eventGroupItem.valid input {
  border-color: #2ecc71;
}
.addEventGroupBtn {
  align-self: flex-start;
}
/* Compact icon button used inside eventGroupItem */
.eventGroupItem .icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  padding: 0;
  border: none;
  border-radius: 2px;
}
.eventGroupItem .icon-btn ui-icon { width: 16px; height: 16px; }
.eventGroupItem .icon-btn[title="编辑"] { background: #4a90e2; }
.eventGroupItem .icon-btn[title="删除"] { background: #e74c3c; }
`;
exports.$ = {
    isOnlyInScreen: '.isOnlyInScreen',
    isPreWarm: '.isPreWarm',
    isLoop: '.isLoop',
    initialDelay: '.initialDelay',
    preWarmDuration: '.preWarmDuration',
    preWarmEffect: '.preWarmEffect',
    emitDuration: '.emitDuration',
    emitInterval: '.emitInterval',
    emitPower: '.emitPower',
    loopInterval: '.loopInterval',
    perEmitCount: '.perEmitCount',
    perEmitInterval: '.perEmitInterval',
    perEmitOffsetX: '.perEmitOffsetX',
    angle: '.angle',
    count: '.count',
    arc: '.arc',
    radius: '.radius',
    emitEffect: '.emitEffect',
    eventGroupData: '.eventGroupData',
    bulletID: '.bulletID',
    bulletData: '.bulletData',
};
const root_path = 'assets/resources/Game/emitter/events/';
function checkEventGroupFileExists(category, eventGroupName) {
    if (!eventGroupName)
        return false;
    const projectPath = Editor.Project.path;
    const filePath = (0, path_1.join)(projectPath, root_path, category, `${eventGroupName}.json`);
    return (0, fs_1.existsSync)(filePath);
}
async function listEventNames(category) {
    const pattern = (0, path_1.join)('db://', root_path, category, '**/*.json').replace(/\\/g, '/');
    try {
        const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        // res can be an array of asset infos (or nested in an array in some editor versions)
        const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
        const names = arr
            .filter((a) => a && !a.isDirectory)
            .map((a) => String(a.name || '').replace(/\.json$/i, ''))
            .filter(Boolean)
            .sort();
        return names;
    }
    catch (e) {
        console.warn('listEventNames failed', e);
        return [];
    }
}
function renderEventGroupDataArray(container, category, dump) {
    const build = (names) => {
        container.innerHTML = '';
        // 创建数组容器
        const arrayContainer = document.createElement('div');
        arrayContainer.className = 'eventGroupData';
        arrayContainer.title = '事件组';
        arrayContainer.style.marginLeft = '10px';
        dump.value.forEach((item, index) => {
            const itemContainer = document.createElement('div');
            itemContainer.className = 'eventGroupItem';
            const sel = document.createElement('ui-select');
            (names.length ? names : ['']).forEach(n => {
                const o = document.createElement('option');
                o.value = n;
                o.innerText = n;
                sel.appendChild(o);
            });
            sel.value = item || (names[0] || ''); // 取出当前项的值，如果没有则取第一个值作为默认值
            sel.addEventListener('change', () => {
                dump.value[index] = sel.value; // 设置当前项的值
                renderEventGroupDataArray(container, category, dump); // 重新渲染
            });
            const btnOpen = document.createElement('button');
            btnOpen.setAttribute('title', '编辑');
            btnOpen.classList.add('icon-btn');
            const icon = document.createElement('ui-icon');
            icon.value = 'edit';
            btnOpen.appendChild(icon);
            btnOpen.onclick = () => Editor.Message.send('bullet_editor', 'open-event-editor', sel.value);
            // Per-item remove button with trash icon
            const btnRemove = document.createElement('button');
            btnRemove.setAttribute('title', '删除');
            btnRemove.classList.add('icon-btn');
            const trash = document.createElement('ui-icon');
            trash.value = 'delete'; // fallback to 'delete' if 'trash' not available
            btnRemove.appendChild(trash);
            btnRemove.onclick = () => {
                dump.value.splice(index, 1);
                renderEventGroupDataArray(container, category, dump);
            };
            itemContainer.appendChild(sel);
            itemContainer.appendChild(btnOpen);
            itemContainer.appendChild(btnRemove);
            arrayContainer.appendChild(itemContainer);
        });
        container.appendChild(arrayContainer);
        // 添加按钮
        const btnAdd = document.createElement('button');
        btnAdd.textContent = '+ 添加事件组';
        btnAdd.className = 'addEventGroupBtn';
        btnAdd.style.cssText = 'background:#2ecc71;color:#fff;border:none;border-radius:2px;padding:2px 6px;margin-top:4px;align-self:flex-start;';
        btnAdd.onclick = () => {
            dump.value.push(names[0] || ''); // 默认值
            renderEventGroupDataArray(container, category, dump);
        };
        container.appendChild(btnAdd);
    };
    listEventNames(category).then(build).catch(() => build([]));
}
function update(dump) {
    this.dump = dump;
    const emitterData = dump.value.emitterData;
    // 普通字段直接走 render
    this.$.isOnlyInScreen.render(emitterData.value.isOnlyInScreen);
    this.$.isPreWarm.render(emitterData.value.isPreWarm);
    this.$.isLoop.render(emitterData.value.isLoop);
    this.$.initialDelay.render(emitterData.value.initialDelay);
    this.$.preWarmDuration.render(emitterData.value.preWarmDuration);
    this.$.preWarmEffect.render(emitterData.value.preWarmEffect);
    this.$.emitDuration.render(emitterData.value.emitDuration);
    this.$.emitInterval.render(emitterData.value.emitInterval);
    this.$.emitPower.render(emitterData.value.emitPower);
    this.$.loopInterval.render(emitterData.value.loopInterval);
    this.$.perEmitCount.render(emitterData.value.perEmitCount);
    this.$.perEmitInterval.render(emitterData.value.perEmitInterval);
    this.$.perEmitOffsetX.render(emitterData.value.perEmitOffsetX);
    this.$.angle.render(emitterData.value.angle);
    this.$.count.render(emitterData.value.count);
    this.$.arc.render(emitterData.value.arc);
    this.$.radius.render(emitterData.value.radius);
    this.$.emitEffect.render(emitterData.value.emitEffect);
    renderEventGroupDataArray(this.$.eventGroupData, "Emitter", emitterData.value.eventGroupData);
    // bullet 相关
    this.$.bulletID.render(dump.value.bulletID);
    this.$.bulletData.render(dump.value.bulletData);
}
function ready() { }
//# sourceMappingURL=data:application/json;base64,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