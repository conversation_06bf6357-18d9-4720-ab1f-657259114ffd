
import { _decorator, error, v2, Vec2, Prefab } from "cc";
import { EventGroupData } from "./EventGroupData";
const { ccclass, property } = _decorator;

/**
 * 发射器数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("EmitterData")
export class EmitterData {
    // @property({displayName: '发射器名称'})
    // name : string = '';                // uid 
    @property({displayName: '是否仅在屏幕内发射'})
    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射
    @property({displayName: '是否预热', tooltip: '预热xxx'})
    isPreWarm : boolean = false;       // 是否预热
    @property({displayName: '是否循环'})
    isLoop : boolean = true;           // 是否循环

    @property({displayName: '初始延迟'})
    initialDelay : number = 0.0;       // 初始延迟
    @property({displayName: '预热持续时长'})
    preWarmDuration : number = 0.0;    // 预热持续时长
    @property({type: Prefab, displayName: '预热特效'})
    preWarmEffect : Prefab;            // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)
    // @property({displayName: '预热音效'})
    // preWarmSound : string;             // 预热音效

    @property({displayName: '发射器持续时间'})
    emitDuration : number = 1.0;       // 发射器持续时间
    @property({displayName: '发射间隔'})
    emitInterval : number = 1.0;       // 发射间隔
    @property({displayName: '发射速度'})
    emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)
    @property({displayName: '循环间隔'})
    loopInterval : number = 0.0;       // 循环间隔

    @property({displayName: '单次发射数量'})
    perEmitCount : number = 1;         // 单次发射数量
    @property({displayName: '单次发射多个子弹时的间隔'})
    perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔
    @property({displayName: '单次发射多个子弹时的x偏移'})
    perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移

    @property({displayName: '发射角度'})
    angle : number = -90;              // 发射角度: -90朝下
    @property({displayName: '发射条数'})
    count : number = 1;                // 发射条数(弹道数量)
    @property({displayName: '发射范围'})
    arc   : number = 60;               // 发射范围(弧度范围)
    @property({displayName: '发射半径'})
    radius : number = 1.0;             // 发射半径
    // TODO: 参数随机

    @property({type:Prefab, displayName: '发射特效'})
    emitEffect : Prefab;               // 发射特效(多个的话建议做到prefab上?) 包含音效?

    @property({type:[String], displayName: '事件组'})
    eventGroupData: string[] = [];

    static fromJSON(json: any): EmitterData {
        const data = new EmitterData();
        if (json) {
            Object.assign(data, json);
            data.eventGroupData = (json.eventGroupData || []).map((id: string) => id);
        }

        return data;
    }
}