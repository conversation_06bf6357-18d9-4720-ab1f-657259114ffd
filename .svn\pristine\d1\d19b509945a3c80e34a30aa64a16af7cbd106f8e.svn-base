"use strict";
/* eslint-disable vue/one-component-per-file */
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = require("fs");
const path_1 = require("path");
// @ts-ignore
const vue_1 = require("vue");
const EventGroupDataManager_1 = require("../../utils/EventGroupDataManager");
const panelDataMap = new WeakMap();
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('Event Editor Panel shown'); },
        hide() { console.log('Event Editor Panel hidden'); },
    },
    messages: {
        'select-event-group'(eventGroupName) {
            // Call the selectEventGroup method defined in methods section
            const panel = this;
            if (panel.methods && panel.methods.selectEventGroup) {
                panel.methods.selectEventGroup.call(panel, eventGroupName);
            }
        },
    },
    template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * Open and select a specific event group
         */
        selectEventGroup(eventGroupName) {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                // Send message to Vue app to select the event group
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.selectEventGroup) {
                    vueInstance.selectEventGroup(eventGroupName);
                }
            }
        },
        /**
         * Reload all event group data
         */
        reloadEventGroups() {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.reloadEventGroups) {
                    vueInstance.reloadEventGroups();
                }
            }
        }
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // Main Event Editor Component
            app.component('EventEditor', (0, vue_1.defineComponent)({
                setup() {
                    const manager = EventGroupDataManager_1.EventGroupDataManager.getInstance();
                    // Reactive state
                    const state = (0, vue_1.reactive)({
                        selectedCategory: EventGroupDataManager_1.EventGroupCategory.Emitter,
                        selectedEventGroup: null,
                        eventGroups: {
                            [EventGroupDataManager_1.EventGroupCategory.Emitter]: [],
                            [EventGroupDataManager_1.EventGroupCategory.Bullet]: []
                        },
                        searchQuery: '',
                        isDirty: false,
                        undoStack: [],
                        redoStack: []
                    });
                    // Computed properties
                    const filteredEventGroups = (0, vue_1.computed)(() => {
                        const groups = state.eventGroups[state.selectedCategory];
                        if (!state.searchQuery)
                            return groups;
                        return groups.filter((group) => group.name.toLowerCase().includes(state.searchQuery.toLowerCase()));
                    });
                    const categories = (0, vue_1.computed)(() => Object.values(EventGroupDataManager_1.EventGroupCategory));
                    // Computed definitions based on selected category
                    const EventConditionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ConditionDefByCategory[state.selectedCategory];
                    });
                    const EventActionDef = (0, vue_1.computed)(() => {
                        return EventGroupDataManager_1.ActionDefByCategory[state.selectedCategory];
                    });
                    // Methods
                    const loadEventGroups = () => {
                        for (const category of Object.values(EventGroupDataManager_1.EventGroupCategory)) {
                            state.eventGroups[category] = manager.loadEventGroupsByCategory(category);
                        }
                    };
                    const selectEventGroup = (eventGroup) => {
                        if (state.isDirty) {
                            // TODO: Show confirmation dialog
                        }
                        // Save current state to undo stack
                        if (state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.redoStack = []; // Clear redo stack
                        }
                        state.selectedEventGroup = Object.assign({}, eventGroup);
                        state.isDirty = false;
                    };
                    const selectEventGroupByName = (name) => {
                        const found = manager.findEventGroup(name);
                        if (found) {
                            state.selectedCategory = found.category;
                            selectEventGroup(found.data);
                        }
                    };
                    const saveCurrentEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return false;
                        const success = manager.saveEventGroup(state.selectedCategory, state.selectedEventGroup);
                        if (success) {
                            state.isDirty = false;
                            loadEventGroups(); // Reload to reflect changes
                        }
                        return success;
                    };
                    const createNewEventGroup = () => {
                        const newName = manager.generateUniqueName(state.selectedCategory);
                        const existedEventGroup = state.eventGroups[state.selectedCategory].find(eg => eg.name === newName);
                        if (existedEventGroup) {
                            // If the event group already exists, select it
                            // But this also means we get a duplicate name
                            selectEventGroup(existedEventGroup);
                        }
                        else {
                            const newEventGroup = manager.createNewEventGroup(state.selectedCategory);
                            state.eventGroups[state.selectedCategory].push(newEventGroup);
                            selectEventGroup(newEventGroup);
                        }
                    };
                    const duplicateEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return;
                        const duplicate = manager.duplicateEventGroup(state.selectedCategory, state.selectedEventGroup.name);
                        if (duplicate) {
                            state.eventGroups[state.selectedCategory].push(duplicate);
                            selectEventGroup(duplicate);
                        }
                    };
                    const deleteEventGroup = (eventGroup) => {
                        var _a;
                        if (confirm(`Are you sure you want to delete "${eventGroup.name}"?`)) {
                            manager.deleteEventGroup(state.selectedCategory, eventGroup.name);
                            loadEventGroups();
                            if (((_a = state.selectedEventGroup) === null || _a === void 0 ? void 0 : _a.name) === eventGroup.name) {
                                state.selectedEventGroup = null;
                                state.isDirty = false;
                            }
                        }
                    };
                    const markDirty = () => {
                        state.isDirty = true;
                    };
                    const undo = () => {
                        if (state.undoStack.length > 0 && state.selectedEventGroup) {
                            state.redoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.undoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const redo = () => {
                        if (state.redoStack.length > 0 && state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.redoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const reloadEventGroups = () => {
                        loadEventGroups();
                        state.selectedEventGroup = null;
                        state.isDirty = false;
                    };
                    const addCondition = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.push({
                            op: 0, // And
                            type: 0,
                            compareOp: 0, // Equal
                            targetValue: 0
                        });
                        markDirty();
                    };
                    const removeCondition = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.splice(index, 1);
                        markDirty();
                    };
                    const addAction = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.push({
                            type: 0,
                            duration: 0,
                            targetValue: 0,
                            easing: 0 // Linear
                        });
                        markDirty();
                    };
                    const removeAction = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.splice(index, 1);
                        markDirty();
                    };
                    // Helper methods to get definition objects for type-aware inputs
                    const getConditionDef = (enumValue) => {
                        return EventConditionDef.value.find(def => def.enum === enumValue);
                    };
                    const getActionDef = (enumValue) => {
                        return EventActionDef.value.find(def => def.enum === enumValue);
                    };
                    // Lifecycle
                    (0, vue_1.onMounted)(() => {
                        loadEventGroups();
                    });
                    // Expose methods for external access
                    return {
                        state,
                        filteredEventGroups,
                        categories,
                        selectEventGroup,
                        selectEventGroupByName,
                        saveCurrentEventGroup,
                        createNewEventGroup,
                        duplicateEventGroup,
                        deleteEventGroup,
                        markDirty,
                        undo,
                        redo,
                        reloadEventGroups,
                        addCondition,
                        removeCondition,
                        addAction,
                        removeAction,
                        // Enums for dropdowns
                        ConditionOpEnum: EventGroupDataManager_1.ConditionOpEnum,
                        CompareOpEnum: EventGroupDataManager_1.CompareOpEnum,
                        EasingEnum: EventGroupDataManager_1.EasingEnum,
                        // New definition-based arrays
                        EventConditionDef,
                        EventActionDef,
                        // Helper methods for type-aware inputs
                        getConditionDef,
                        getActionDef
                    };
                },
                template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/event-editor.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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