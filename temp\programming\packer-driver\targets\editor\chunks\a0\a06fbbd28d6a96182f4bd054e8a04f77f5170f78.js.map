{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts"], "names": ["Property", "PropertyContainer", "PropertyContainerComponent", "_decorator", "Component", "ccclass", "property", "constructor", "value", "_value", "_isDirty", "_listeners", "isDirty", "set<PERSON>irty", "newValue", "on", "listener", "push", "off", "filter", "l", "notify", "force", "for<PERSON>ach", "_properties", "Map", "addProperty", "key", "get", "set", "removeProperty", "delete", "getProperty", "getPropertyValue", "setProperty", "notifyAll", "clear", "_propertyContainer"], "mappings": ";;;yFAcaA,Q,EA8CAC,iB,EA8CAC,0B;;;;;;;;;;;;;AA1GJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U,GAE9B;;0BAWaH,Q,GAAN,MAAMA,QAAN,CAAuC;AAMnCO,QAAAA,WAAW,CAACC,KAAD,EAAW;AAAA,eALrBC,MAKqB;AAAA,eAJrBC,QAIqB,GAJD,KAIC;AAH7B;AAG6B,eAFrBC,UAEqB,GAFmB,EAEnB;AACzB,eAAKF,MAAL,GAAcD,KAAd;AACA,eAAKE,QAAL,GAAgB,KAAhB;AACH;;AAEU,YAAPE,OAAO,GAAY;AACnB,iBAAO,KAAKF,QAAZ;AACH;;AAEDG,QAAAA,QAAQ,CAACL,KAAD,EAAuB;AAC3B,eAAKE,QAAL,GAAgBF,KAAhB;AACH;;AAEQ,YAALA,KAAK,GAAM;AACX,iBAAO,KAAKC,MAAZ;AACH;;AAEQ,YAALD,KAAK,CAACM,QAAD,EAAc;AACnB,cAAI,KAAKL,MAAL,KAAgBK,QAApB,EAA8B;AAE9B,eAAKL,MAAL,GAAcK,QAAd;AACA,eAAKD,QAAL,CAAc,IAAd;AACH;;AAEME,QAAAA,EAAE,CAACC,QAAD,EAAqC;AAC1C,eAAKL,UAAL,CAAgBM,IAAhB,CAAqBD,QAArB;AACH;;AAEME,QAAAA,GAAG,CAACF,QAAD,EAAqC;AAC3C,eAAKL,UAAL,GAAkB,KAAKA,UAAL,CAAgBQ,MAAhB,CAAuBC,CAAC,IAAIA,CAAC,KAAKJ,QAAlC,CAAlB;AACH;;AAEMK,QAAAA,MAAM,CAACC,KAAc,GAAG,KAAlB,EAA+B;AACxC,cAAIA,KAAK,IAAI,KAAKV,OAAlB,EAA2B;AACvB,iBAAKD,UAAL,CAAgBY,OAAhB,CAAwBP,QAAQ,IAAIA,QAAQ,CAAC,KAAKP,MAAN,CAA5C;;AACA,iBAAKI,QAAL,CAAc,KAAd;AACH;AACJ;;AA3CyC,O;;mCA8CjCZ,iB,GAAN,MAAMA,iBAAN,CAA2B;AAAA;AAAA,eACtBuB,WADsB,GACW,IAAIC,GAAJ,EADX;AAAA;;AAGvBC,QAAAA,WAAW,CAAIC,GAAJ,EAAYnB,KAAZ,EAAmC;AACjD,cAAIF,QAAQ,GAAG,KAAKkB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAf;;AACA,cAAIrB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH,WAFD,MAEO;AACHF,YAAAA,QAAQ,GAAG,IAAIN,QAAJ,CAAgBQ,KAAhB,CAAX;;AACA,iBAAKgB,WAAL,CAAiBK,GAAjB,CAAqBF,GAArB,EAA0BrB,QAA1B;AACH;;AAED,iBAAOA,QAAP;AACH;;AAEMwB,QAAAA,cAAc,CAAIH,GAAJ,EAAkB;AACnC,eAAKH,WAAL,CAAiBO,MAAjB,CAAwBJ,GAAxB;AACH;;AAEMK,QAAAA,WAAW,CAAIL,GAAJ,EAAqC;AACnD,iBAAO,KAAKH,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAP;AACH;;AAEMM,QAAAA,gBAAgB,CAAIN,GAAJ,EAA2B;AAC9C;AACA,gBAAMrB,QAAQ,GAAG,KAAKkB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,iBAAOrB,QAAP,oBAAOA,QAAQ,CAAEE,KAAjB;AACH;;AAEM0B,QAAAA,WAAW,CAAIP,GAAJ,EAAYnB,KAAZ,EAA4B;AAC1C,gBAAMF,QAAQ,GAAG,KAAKkB,WAAL,CAAiBI,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,cAAIrB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH;AACJ;;AAEM2B,QAAAA,SAAS,CAACb,KAAc,GAAG,KAAlB,EAA+B;AAC3C,eAAKE,WAAL,CAAiBD,OAAjB,CAAyBjB,QAAQ,IAAIA,QAAQ,CAACe,MAAT,CAAgBC,KAAhB,CAArC;AACH;;AAEMc,QAAAA,KAAK,GAAG;AACX,eAAKZ,WAAL,CAAiBY,KAAjB;AACH;;AA1C6B,O,GA6ClC;;;4CACalC,0B,GAAN,MAAMA,0BAAN,SAA4CE,SAA5C,CAAsD;AAAA;AAAA;AAAA,eACjDiC,kBADiD,GACN,IAAIpC,iBAAJ,EADM;AAAA;;AAGlDyB,QAAAA,WAAW,CAAIC,GAAJ,EAAYnB,KAAZ,EAAmC;AACjD,iBAAO,KAAK6B,kBAAL,CAAwBX,WAAxB,CAAoCC,GAApC,EAAyCnB,KAAzC,CAAP;AACH;;AAEMsB,QAAAA,cAAc,CAAIH,GAAJ,EAAkB;AACnC,eAAKU,kBAAL,CAAwBP,cAAxB,CAAuCH,GAAvC;AACH;;AAEMK,QAAAA,WAAW,CAAIL,GAAJ,EAAqC;AACnD,iBAAO,KAAKU,kBAAL,CAAwBL,WAAxB,CAAoCL,GAApC,CAAP;AACH;;AAEMM,QAAAA,gBAAgB,CAAIN,GAAJ,EAA2B;AAC9C,iBAAO,KAAKU,kBAAL,CAAwBJ,gBAAxB,CAAyCN,GAAzC,CAAP;AACH;;AAEMO,QAAAA,WAAW,CAAIP,GAAJ,EAAYnB,KAAZ,EAA4B;AAC1C,eAAK6B,kBAAL,CAAwBH,WAAxB,CAAoCP,GAApC,EAAyCnB,KAAzC;AACH;;AAEM2B,QAAAA,SAAS,CAACb,KAAc,GAAG,KAAlB,EAA+B;AAC3C,eAAKe,kBAAL,CAAwBF,SAAxB,CAAkCb,KAAlC;AACH;;AAEMc,QAAAA,KAAK,GAAG;AACX,eAAKC,kBAAL,CAAwBD,KAAxB;AACH;;AA7BwD,O", "sourcesContent": ["import { _decorator, Component } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n// Expression wrapper\r\ninterface Expression {\r\n    raw: string;             // the original expression text\r\n    compiled?: Function;     // compiled JS function\r\n}\r\n\r\nexport interface IProperty {\r\n    get isDirty(): boolean;\r\n    notify(force?: boolean): void;\r\n}\r\n\r\nexport class Property<T> implements IProperty {\r\n    private _value: T;\r\n    private _isDirty: boolean = false;\r\n    // listeners\r\n    private _listeners: Array<(value: T) => void> = [];\r\n\r\n    public constructor(value: T) {\r\n        this._value = value;\r\n        this._isDirty = false;\r\n    }\r\n\r\n    get isDirty(): boolean {\r\n        return this._isDirty;\r\n    }\r\n\r\n    setDirty(value: boolean): void {\r\n        this._isDirty = value;\r\n    }\r\n\r\n    get value(): T {\r\n        return this._value;\r\n    }\r\n\r\n    set value(newValue: T) {\r\n        if (this._value === newValue) return;\r\n\r\n        this._value = newValue;\r\n        this.setDirty(true);\r\n    }\r\n\r\n    public on(listener: (value: T) => void): void {\r\n        this._listeners.push(listener);\r\n    }\r\n\r\n    public off(listener: (value: T) => void): void {\r\n        this._listeners = this._listeners.filter(l => l !== listener);\r\n    }\r\n\r\n    public notify(force: boolean = false): void {\r\n        if (force || this.isDirty) {\r\n            this._listeners.forEach(listener => listener(this._value));\r\n            this.setDirty(false);\r\n        }\r\n    }\r\n}\r\n\r\nexport class PropertyContainer<K> {\r\n    private _properties: Map<K, IProperty> = new Map();\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        let property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        } else {\r\n            property = new Property<T>(value);\r\n            this._properties.set(key, property);\r\n        }\r\n\r\n        return property;\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._properties.delete(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._properties.get(key) as Property<T> | undefined;\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        // get property as PropertyValue<T>\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        return property?.value; \r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        }\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._properties.forEach(property => property.notify(force));\r\n    }\r\n\r\n    public clear() {\r\n        this._properties.clear();\r\n    }\r\n}\r\n\r\n// Use this one to simplify class hierarchy\r\nexport class PropertyContainerComponent<K> extends Component {\r\n    private _propertyContainer: PropertyContainer<K> = new PropertyContainer<K>();\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        return this._propertyContainer.addProperty(key, value);\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._propertyContainer.removeProperty(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._propertyContainer.getProperty(key);\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        return this._propertyContainer.getPropertyValue(key);\r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        this._propertyContainer.setProperty(key, value);\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._propertyContainer.notifyAll(force);\r\n    }\r\n    \r\n    public clear() {\r\n        this._propertyContainer.clear();\r\n    }\r\n}"]}