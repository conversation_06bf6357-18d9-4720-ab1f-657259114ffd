System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, EDITOR, ObjectPool, Movable, BulletSystem, EventGroup, EventGroupContext, PropertyContainerComponent, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, eBulletProp, BulletController;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../data/bullet/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMovable(extras) {
    _reporterNs.report("Movable", "../move/Movable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropertyContainerComponent(extras) {
    _reporterNs.report("PropertyContainerComponent", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      ObjectPool = _unresolved_2.ObjectPool;
    }, function (_unresolved_3) {
      Movable = _unresolved_3.Movable;
    }, function (_unresolved_4) {
      BulletSystem = _unresolved_4.BulletSystem;
    }, function (_unresolved_5) {
      EventGroup = _unresolved_5.EventGroup;
      EventGroupContext = _unresolved_5.EventGroupContext;
    }, function (_unresolved_6) {
      PropertyContainerComponent = _unresolved_6.PropertyContainerComponent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "21904rFEfNCRbgYx1LE+M6P", "BulletController", undefined);

      __checkObsolete__(['_decorator', 'misc', 'Component', 'Node', 'Sprite', 'Color']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator); // 用枚举定义属性

      _export("eBulletProp", eBulletProp = /*#__PURE__*/function (eBulletProp) {
        eBulletProp[eBulletProp["IsDestructive"] = 0] = "IsDestructive";
        eBulletProp[eBulletProp["IsDestructiveOnHit"] = 1] = "IsDestructiveOnHit";
        eBulletProp[eBulletProp["Duration"] = 2] = "Duration";
        eBulletProp[eBulletProp["Damage"] = 3] = "Damage";
        eBulletProp[eBulletProp["DelayDestroy"] = 4] = "DelayDestroy";
        eBulletProp[eBulletProp["IsFacingMoveDir"] = 5] = "IsFacingMoveDir";
        eBulletProp[eBulletProp["IsTrackingTarget"] = 6] = "IsTrackingTarget";
        eBulletProp[eBulletProp["Speed"] = 7] = "Speed";
        eBulletProp[eBulletProp["SpeedAngle"] = 8] = "SpeedAngle";
        eBulletProp[eBulletProp["Acceleration"] = 9] = "Acceleration";
        eBulletProp[eBulletProp["AccelerationAngle"] = 10] = "AccelerationAngle";
        return eBulletProp;
      }({})); // 子弹 Bullet 伤害计算 
      // Weapon -> 发射器, 喷火, 技能武器, 激光
      // WeaponSlot -> SetWeapon
      // 如何集成到项目里? 考虑把这个类改为BulletController, 控制移动等属性, 作为一个component加入到Bullet:Entity里去


      _export("BulletController", BulletController = (_dec = ccclass('BulletController'), _dec2 = property({
        type: _crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
          error: Error()
        }), Movable) : Movable,
        displayName: "移动组件"
      }), _dec3 = property({
        type: Sprite,
        displayName: "子弹精灵"
      }), _dec(_class = executeInEditMode(_class = (_class2 = class BulletController extends (_crd && PropertyContainerComponent === void 0 ? (_reportPossibleCrUseOfPropertyContainerComponent({
        error: Error()
      }), PropertyContainerComponent) : PropertyContainerComponent) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "mover", _descriptor, this);

          // TODO: 这里后续不处理子弹的sprite显示
          _initializerDefineProperty(this, "bulletSprite", _descriptor2, this);

          // 由发射器传进来
          // @property({type: BulletData})
          this.bulletData = void 0;
          this.isRunning = false;
          this.elapsedTime = 0;
          this.emitter = void 0;
          // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
          // public isDestructive : Property<boolean>;          // 是否可被破坏
          // public isDestructiveOnHit : Property<boolean>;     // 命中时是否被销毁
          this.duration = void 0;
          // 子弹持续时间(超出后销毁回收)
          this.delayDestroy = void 0;
          // 延迟销毁时间
          this.isFacingMoveDir = void 0;
          // 是否面向移动方向
          this.isTrackingTarget = void 0;
          // 是否追踪目标
          this.speed = void 0;
          // 子弹速度
          this.speedAngle = void 0;
          // 子弹速度角度
          this.acceleration = void 0;
          // 子弹加速度
          this.accelerationAngle = void 0;
          // 子弹加速度角度
          this._eventGroups = [];
        }

        onLoad() {
          if (!this.mover) {
            var _this$getComponent;

            this.mover = (_this$getComponent = this.getComponent(_crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
              error: Error()
            }), Movable) : Movable)) == null ? void 0 : _this$getComponent.addComponent(_crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
              error: Error()
            }), Movable) : Movable);
          }

          this.mover.onBecomeInvisible = () => {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).onDestroyBullet(this);
          };
        }
        /**
         * TODO: 如果后续自己写碰撞, 这里要相应进行替换
         */


        onCollisionEnter(other, self) {
          // 判断另一个node也是子弹或者非子弹, 进行相应处理
          // 根据this.isDestructive 和 this.isDestructiveOnHit
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onDestroyBullet(this);
        }

        onCreate(emitter) {
          this.isRunning = true;
          this.elapsedTime = 0;
          this.emitter = emitter;
          this.bulletData = emitter.bulletData;
          this.resetProperties();

          if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            let ctx = new (_crd && EventGroupContext === void 0 ? (_reportPossibleCrUseOfEventGroupContext({
              error: Error()
            }), EventGroupContext) : EventGroupContext)();
            ctx.emitter = this.emitter;
            ctx.bullet = this;

            for (const dataName of this.bulletData.eventGroupData) {
              const eventGroup = new (_crd && EventGroup === void 0 ? (_reportPossibleCrUseOfEventGroup({
                error: Error()
              }), EventGroup) : EventGroup)(ctx, (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).loadEmitterEventGroup(dataName));
              eventGroup.start();

              this._eventGroups.push(eventGroup);
            }
          }
        }

        resetProperties() {
          this.clear();
          if (!this.bulletData) return;
          this.duration = this.addProperty(eBulletProp.Duration, this.bulletData.duration);
          this.delayDestroy = this.addProperty(eBulletProp.DelayDestroy, this.bulletData.delayDestroy); // this.isDestructive = this.addProperty(eBulletProp.IsDestructive, this.bulletData.isDestructive);
          // this.isDestructiveOnHit = this.addProperty(eBulletProp.IsDestructiveOnHit, this.bulletData.isDestructiveOnHit);
          // this.damage = this.addProperty(eBulletProp.Damage, this.bulletData.damage);

          this.isFacingMoveDir = this.addProperty(eBulletProp.IsFacingMoveDir, this.bulletData.isFacingMoveDir);
          this.isTrackingTarget = this.addProperty(eBulletProp.IsTrackingTarget, this.bulletData.isTrackingTarget);
          this.speed = this.addProperty(eBulletProp.Speed, this.bulletData.speed);
          this.speedAngle = this.addProperty(eBulletProp.SpeedAngle, 0);
          this.acceleration = this.addProperty(eBulletProp.Acceleration, this.bulletData.acceleration);
          this.accelerationAngle = this.addProperty(eBulletProp.AccelerationAngle, this.bulletData.accelerationAngle); // listen to property changes

          this.isFacingMoveDir.on(value => {
            this.mover.isFacingMoveDir = value;
          });
          this.isTrackingTarget.on(value => {
            this.mover.isTrackingTarget = value;
          });
          this.speed.on(value => {
            this.mover.speed = value;
          });
          this.speedAngle.on(value => {
            this.mover.speedAngle = value;
          });
          this.acceleration.on(value => {
            this.mover.acceleration = value;
          });
          this.accelerationAngle.on(value => {
            this.mover.accelerationAngle = value;
          });
          this.notifyAll(true);
        }

        tick(dt) {
          var _this$mover;

          if (!this.isRunning) return;
          this.elapsedTime += dt;

          if (this.elapsedTime > this.duration.value) {
            this.destroySelf();
            return;
          }

          (_this$mover = this.mover) == null || _this$mover.tick(dt);
        }

        destroySelf() {
          this.isRunning = false;

          this._eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.


          this._eventGroups = []; // clear the event groups array

          const cb = () => {
            if (!this.node || !this.node.isValid) return;

            if (EDITOR) {
              this.node.destroy();
            } else {
              (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
                error: Error()
              }), ObjectPool) : ObjectPool).returnNode(this.node);
            }
          };

          if (this.delayDestroy && this.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
              cb();
            }, this.delayDestroy.value);
          } else {
            cb();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "mover", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "bulletSprite", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4fe20834a71efd0e629f6533bc5f5e9095845b29.js.map