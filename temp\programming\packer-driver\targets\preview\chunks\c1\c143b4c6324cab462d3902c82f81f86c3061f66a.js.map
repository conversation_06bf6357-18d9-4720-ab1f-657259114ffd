{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EmitterData.ts"], "names": ["_decorator", "Prefab", "ccclass", "property", "EmitterData", "displayName", "tooltip", "type", "String", "fromJSON", "json", "data", "Object", "assign", "eventGroupData", "map", "id"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,M,OAAAA,M;;;;;;;;;OAEhC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;AAE9B;AACA;AACA;AACA;;6BAEaI,W,WADZF,OAAO,CAAC,aAAD,C,UAIHC,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE,MAAd;AAAsBC,QAAAA,OAAO,EAAE;AAA/B,OAAD,C,UAERH,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEN,MAAP;AAAeI,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAKRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERF,QAAQ,CAAC;AAACE,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRF,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAACN,MAAN;AAAcI,QAAAA,WAAW,EAAE;AAA3B,OAAD,C,WAGRF,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAACC,MAAN;AAAcH,QAAAA,WAAW,EAAE;AAA3B,OAAD,C,2BAjDb,MACaD,WADb,CACyB;AAAA;AACrB;AACA;AAFqB;;AAIc;AAJd;;AAMc;AANd;;AAQc;AARd;;AAWc;AAXd;;AAac;AAbd;;AAec;AACnC;AACA;AAjBqB;;AAoBc;AApBd;;AAsBc;AAtBd;;AAwBc;AAxBd;;AA0Bc;AA1Bd;;AA6Bc;AA7Bd;;AA+Bc;AA/Bd;;AAiCc;AAjCd;;AAoCc;AApCd;;AAsCc;AAtCd;;AAwCc;AAxCd;;AA0Cc;AACnC;AA3CqB;;AA8Cc;AA9Cd;AAAA;;AAmDN,eAARK,QAAQ,CAACC,IAAD,EAAyB;AACpC,cAAMC,IAAI,GAAG,IAAIP,WAAJ,EAAb;;AACA,cAAIM,IAAJ,EAAU;AACNE,YAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACAC,YAAAA,IAAI,CAACG,cAAL,GAAsB,CAACJ,IAAI,CAACI,cAAL,IAAuB,EAAxB,EAA4BC,GAA5B,CAAiCC,EAAD,IAAgBA,EAAhD,CAAtB;AACH;;AAED,iBAAOL,IAAP;AACH;;AA3DoB,O;;;;;iBAIM,I;;;;;;;iBAEL,K;;;;;;;iBAEH,I;;;;;;;iBAGK,G;;;;;;;iBAEG,G;;;;;;;;;;;;iBAOH,G;;;;;;;iBAEA,G;;;;;;;iBAEH,G;;;;;;;iBAEG,G;;;;;;;iBAGA,C;;;;;;;iBAEG,G;;;;;;;iBAED,G;;;;;;;iBAGT,CAAC,E;;;;;;;iBAED,C;;;;;;;iBAEA,E;;;;;;;iBAEC,G;;;;;;;;;;;;iBAOS,E", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2, Prefab } from \"cc\";\r\nimport { EventGroupData } from \"./EventGroupData\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 发射器数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"EmitterData\")\r\nexport class EmitterData {\r\n    // @property({displayName: '发射器名称'})\r\n    // name : string = '';                // uid \r\n    @property({displayName: '是否仅在屏幕内发射'})\r\n    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n    @property({displayName: '是否预热', tooltip: '预热xxx'})\r\n    isPreWarm : boolean = false;       // 是否预热\r\n    @property({displayName: '是否循环'})\r\n    isLoop : boolean = true;           // 是否循环\r\n\r\n    @property({displayName: '初始延迟'})\r\n    initialDelay : number = 0.0;       // 初始延迟\r\n    @property({displayName: '预热持续时长'})\r\n    preWarmDuration : number = 0.0;    // 预热持续时长\r\n    @property({type: Prefab, displayName: '预热特效'})\r\n    preWarmEffect : Prefab;            // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)\r\n    // @property({displayName: '预热音效'})\r\n    // preWarmSound : string;             // 预热音效\r\n\r\n    @property({displayName: '发射器持续时间'})\r\n    emitDuration : number = 1.0;       // 发射器持续时间\r\n    @property({displayName: '发射间隔'})\r\n    emitInterval : number = 1.0;       // 发射间隔\r\n    @property({displayName: '发射速度'})\r\n    emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)\r\n    @property({displayName: '循环间隔'})\r\n    loopInterval : number = 0.0;       // 循环间隔\r\n\r\n    @property({displayName: '单次发射数量'})\r\n    perEmitCount : number = 1;         // 单次发射数量\r\n    @property({displayName: '单次发射多个子弹时的间隔'})\r\n    perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔\r\n    @property({displayName: '单次发射多个子弹时的x偏移'})\r\n    perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移\r\n\r\n    @property({displayName: '发射角度'})\r\n    angle : number = -90;              // 发射角度: -90朝下\r\n    @property({displayName: '发射条数'})\r\n    count : number = 1;                // 发射条数(弹道数量)\r\n    @property({displayName: '发射范围'})\r\n    arc   : number = 60;               // 发射范围(弧度范围)\r\n    @property({displayName: '发射半径'})\r\n    radius : number = 1.0;             // 发射半径\r\n    // TODO: 参数随机\r\n\r\n    @property({type:Prefab, displayName: '发射特效'})\r\n    emitEffect : Prefab;               // 发射特效(多个的话建议做到prefab上?) 包含音效?\r\n\r\n    @property({type:String, displayName: '事件组'})\r\n    eventGroupData: string[] = [];\r\n\r\n    static fromJSON(json: any): EmitterData {\r\n        const data = new EmitterData();\r\n        if (json) {\r\n            Object.assign(data, json);\r\n            data.eventGroupData = (json.eventGroupData || []).map((id: string) => id);\r\n        }\r\n\r\n        return data;\r\n    }\r\n}"]}