import { existsSync, readFileSync, writeFileSync, mkdirSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { eEmitterCondition, eBulletCondition, eEmitterActionType, eBulletActionType } from './GameEnumDef';

export interface ValueDecorator {
    enum: number;  // the value corresponds to the enum index in the game
    label: string;  // the display label for the enum value
    type: string;   // the value type of this Value: string | number | boolean
};

// Enum definitions for UI display
export const ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};

export const CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};

// These should match your actual game enums defined in EventConditionType.ts
export const EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};

export const EmitterConditionDef = [
    { enum: eEmitterCondition.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: eEmitterCondition.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: eEmitterCondition.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: eEmitterCondition.Emitter_PrewarmDuration, label: "发射器预热时长", type: "number" },
    { enum: eEmitterCondition.Emitter_Duration, label: "发射器持续时长", type: "number" },
    { enum: eEmitterCondition.Emitter_ElapsedTime, label: "发射器运行时长", type: "number" },
    { enum: eEmitterCondition.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: eEmitterCondition.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: eEmitterCondition.Emitter_EmitCount, label: "发射器开火次数", type: "number" },
    { enum: eEmitterCondition.Emitter_EmitInterval, label: "发射器开火间隔", type: "number" },
    { enum: eEmitterCondition.Emitter_EmitOffsetX, label: "发射器开火X偏移", type: "number" },
    { enum: eEmitterCondition.Emitter_Angle, label: "发射器弹道角度", type: "number" },
    { enum: eEmitterCondition.Emitter_Count, label: "发射器弹道数量", type: "number" },

    // 子弹
    { enum: eEmitterCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: eEmitterCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: eEmitterCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: eEmitterCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: eEmitterCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: eEmitterCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: eEmitterCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: eEmitterCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: eEmitterCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: eEmitterCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: eEmitterCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: eEmitterCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: eEmitterCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },

    // 单位
    { enum: eEmitterCondition.Unit_Life, label: "单位生命", type: "number" },
    { enum: eEmitterCondition.Unit_LifePercent, label: "单位生命百分比", type: "number" },
    { enum: eEmitterCondition.Unit_PosX, label: "单位X坐标", type: "number" },
    { enum: eEmitterCondition.Unit_PosY, label: "单位Y坐标", type: "number" },
    { enum: eEmitterCondition.Unit_Speed, label: "单位速度", type: "number" },
    { enum: eEmitterCondition.Unit_SpeedAngle, label: "单位速度角度", type: "number" },
    { enum: eEmitterCondition.Unit_Acceleration, label: "单位加速度", type: "number" },
    { enum: eEmitterCondition.Unit_AccelerationAngle, label: "单位加速度角度", type: "number" },
];

export const BulletConditionDef = [
    { enum: eBulletCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: eBulletCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: eBulletCondition.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: eBulletCondition.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: eBulletCondition.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: eBulletCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: eBulletCondition.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: eBulletCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: eBulletCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: eBulletCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: eBulletCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: eBulletCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: eBulletCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: eBulletCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: eBulletCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: eBulletCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: eBulletCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];

export const EmitterActionDef = [
    { enum: eEmitterActionType.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: eEmitterActionType.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: eEmitterActionType.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: eEmitterActionType.Emitter_PrewarmDuration, label: "发射器预热持续时间", type: "number" },
    { enum: eEmitterActionType.Emitter_Duration, label: "发射器持续时间", type: "number" },
    { enum: eEmitterActionType.Emitter_ElapsedTime, label: "发射器已运行时间", type: "number" },
    { enum: eEmitterActionType.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: eEmitterActionType.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: eEmitterActionType.Emitter_PerEmitCount, label: "发射器每次开火次数", type: "number" },
    { enum: eEmitterActionType.Emitter_PerEmitInterval, label: "发射器每次开火间隔", type: "number" },
    { enum: eEmitterActionType.Emitter_PerEmitOffsetX, label: "发射器每次开火偏移X", type: "number" },
    { enum: eEmitterActionType.Emitter_Angle, label: "发射器角度", type: "number" },
    { enum: eEmitterActionType.Emitter_Count, label: "发射器数量", type: "number" },

    // 子弹相关
    { enum: eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },

    // 单位相关
];

export const BulletActionDef = [
    { enum: eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];

export enum EventGroupCategory {
    Emitter = 'Emitter',
    Bullet = 'Bullet',
}

// New definition-based mappings using ValueDecorator
export const ConditionDefByCategory = {
    [EventGroupCategory.Emitter]: EmitterConditionDef,
    [EventGroupCategory.Bullet]: BulletConditionDef,
};

export const ActionDefByCategory = {
    [EventGroupCategory.Emitter]: EmitterActionDef,
    [EventGroupCategory.Bullet]: BulletActionDef,
};

// EventGroupData interfaces (matching the game code structure)
export interface EventConditionData {
    op: number; // eConditionOp
    type: number; // eEventConditionType
    compareOp: number; // eCompareOp
    targetValue: number;
}

export interface EventActionData {
    type: number; // eEventActionType
    duration: number;
    targetValue: number;
    easing: number; // eEasing
}

export interface EventGroupData {
    name: string;
    triggerCount: number;
    conditions: EventConditionData[];
    actions: EventActionData[];
}

export class EventGroupDataManager {
    private static instance: EventGroupDataManager;
    private projectPath: string;
    private eventsBasePath: string;

    private constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = join(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }

    public static getInstance(): EventGroupDataManager {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }

    /**
     * Get the full path for a category folder
     */
    private getCategoryPath(category: EventGroupCategory): string {
        return join(this.eventsBasePath, category);
    }

    /**
     * Get the full file path for an EventGroupData
     */
    private getFilePath(category: EventGroupCategory, name: string): string {
        return join(this.getCategoryPath(category), `${name}.json`);
    }

    /**
     * Ensure directory exists
     */
    private ensureDirectoryExists(dirPath: string): void {
        if (!existsSync(dirPath)) {
            mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * Load all EventGroupData files from a category
     */
    public loadEventGroupsByCategory(category: EventGroupCategory): EventGroupData[] {
        const categoryPath = this.getCategoryPath(category);
        
        if (!existsSync(categoryPath)) {
            return [];
        }

        const files = readdirSync(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups: EventGroupData[] = [];

        for (const file of files) {
            try {
                const filePath = join(categoryPath, file);
                const content = readFileSync(filePath, 'utf-8');
                const data = JSON.parse(content) as EventGroupData;
                
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            } catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }

        return eventGroups;
    }

    /**
     * Load a specific EventGroupData by name and category
     */
    public loadEventGroup(category: EventGroupCategory, name: string): EventGroupData | null {
        const filePath = this.getFilePath(category, name);
        
        if (!existsSync(filePath)) {
            return null;
        }

        try {
            const content = readFileSync(filePath, 'utf-8');
            const data = JSON.parse(content) as EventGroupData;
            data.name = name; // Ensure name is correct
            return data;
        } catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }

    /**
     * Save an EventGroupData to file
     */
    public saveEventGroup(category: EventGroupCategory, eventGroup: EventGroupData): boolean {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            
            writeFileSync(filePath, content, 'utf-8');
            return true;
        } catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }

    /**
     * Delete an EventGroupData file
     */
    public deleteEventGroup(category: EventGroupCategory, name: string): boolean {
        try {
            const filePath = this.getFilePath(category, name);
            
            if (existsSync(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }

    /**
     * Check if an EventGroupData file exists
     */
    public eventGroupExists(category: EventGroupCategory, name: string): boolean {
        const filePath = this.getFilePath(category, name);
        return existsSync(filePath);
    }

    /**
     * Find an EventGroupData by name across all categories
     */
    public findEventGroup(name: string): { category: EventGroupCategory; data: EventGroupData } | null {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }

    /**
     * Generate a unique name for a new EventGroupData
     */
    public generateUniqueName(category: EventGroupCategory, baseName: string = 'EventGroup'): string {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        
        return name;
    }

    /**
     * Create a new EventGroupData with default values
     */
    public createNewEventGroup(category: EventGroupCategory, name?: string): EventGroupData {
        const finalName = name || this.generateUniqueName(category);

        var eventGroup: EventGroupData = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };

        return eventGroup;
    }

    /**
     * Duplicate an existing EventGroupData
     */
    public duplicateEventGroup(category: EventGroupCategory, originalName: string, newName?: string): EventGroupData | null {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }

        const finalName = newName || this.generateUniqueName(category, originalName);
        
        // Deep copy the original data
        const duplicate: EventGroupData = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => ({ ...condition })),
            actions: original.actions.map(action => ({ ...action }))
        };

        return duplicate;
    }

    /**
     * Get all EventGroupData names from all categories
     */
    public getAllEventGroupNames(): { [category: string]: string[] } {
        const result: { [category: string]: string[] } = {};
        
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        
        return result;
    }
}
