"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventGroupDataManager = exports.ActionDefByCategory = exports.ConditionDefByCategory = exports.ActionTypeByCategory = exports.ConditionTypeByCategory = exports.EventGroupCategory = exports.BulletActionDef = exports.EmitterActionDef = exports.BulletConditionDef = exports.EmitterConditionDef = exports.EasingEnum = exports.BulletActionTypeEnum = exports.EmitterActionTypeEnum = exports.BulletConditionTypeEnum = exports.EmitterConditionTypeEnum = exports.CompareOpEnum = exports.ConditionOpEnum = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const GameEnumDef_1 = require("./GameEnumDef");
;
// Enum definitions for UI display
exports.ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};
exports.CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};
// These should match your actual game enums defined in EventConditionType.ts
exports.EmitterConditionTypeEnum = {
    0: 'Level_Duration',
    1: 'Level_Distance',
    2: 'Level_InfLevel',
    3: 'Level_ChallengeLevel',
    4: 'Player_ActLevel',
    5: 'Player_PosX',
    6: 'Player_PosY',
    7: 'Player_LifePercent',
    8: 'Player_GainBuff',
    9: 'Unit_Life',
    10: 'Unit_LifePercent',
    11: 'Unit_Duration',
    12: 'Unit_PosX',
    13: 'Unit_PosY',
    14: 'Unit_Speed',
    15: 'Unit_SpeedAngle',
    16: 'Unit_Acceleration',
    17: 'Unit_AccelerationAngle',
    18: 'Unit_DistanceToPlayer',
    19: 'Unit_AngleToPlayer',
    20: 'Emitter_Active',
    21: 'Emitter_InitialDelay',
    22: 'Emitter_Prewarm',
    23: 'Emitter_PrewarmDuration',
    24: 'Emitter_Duration',
    25: 'Emitter_ElapsedTime',
    26: 'Emitter_Loop',
    27: 'Emitter_LoopInterval',
    28: 'Emitter_EmitInterval',
    29: 'Emitter_EmitCount',
    30: 'Emitter_EmitOffsetX',
    31: 'Emitter_Angle',
    32: 'Emitter_Count',
    33: 'Bullet_Sprite',
    34: 'Bullet_Scale',
    35: 'Bullet_ColorR',
    36: 'Bullet_ColorG',
    37: 'Bullet_ColorB',
    38: 'Bullet_Duration',
    39: 'Bullet_ElapsedTime',
    40: 'Bullet_Speed',
    41: 'Bullet_Acceleration',
    42: 'Bullet_AccelerationAngle',
    43: 'Bullet_FacingMoveDir',
    44: 'Bullet_Destructive',
    45: 'Bullet_DestructiveOnHit',
};
exports.BulletConditionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_FacingMoveDir',
    14: 'Bullet_Destructive',
    15: 'Bullet_DestructiveOnHit',
};
exports.EmitterActionTypeEnum = {
    0: 'Emitter_Active',
    1: 'Emitter_InitialDelay',
    2: 'Emitter_Prewarm',
    3: 'Emitter_PrewarmDuration',
    4: 'Emitter_Duration',
    5: 'Emitter_ElapsedTime',
    6: 'Emitter_Loop',
    7: 'Emitter_LoopInterval',
    8: 'Emitter_PerEmitInterval',
    9: 'Emitter_PerEmitCount',
    10: 'Emitter_PerEmitOffsetX',
    11: 'Emitter_Angle',
    12: 'Emitter_Count',
    13: 'Bullet_Duration',
    14: 'Bullet_ElapsedTime',
    15: 'Bullet_PosX',
    16: 'Bullet_PosY',
    17: 'Bullet_Damage',
    18: 'Bullet_Speed',
    19: 'Bullet_SpeedAngle',
    20: 'Bullet_Acceleration',
    21: 'Bullet_AccelerationAngle',
    22: 'Bullet_Scale',
    23: 'Bullet_ColorR',
    24: 'Bullet_ColorG',
    25: 'Bullet_ColorB',
    26: 'Bullet_ColorA',
    27: 'Bullet_FaceMovingDir',
    28: 'Bullet_TrackingTarget',
    29: 'Bullet_Destructive',
    30: 'Bullet_DestructiveOnHit',
    31: 'Unit_Life',
    32: 'Unit_LifePercent',
    33: 'Unit_PosX',
    34: 'Unit_PosY',
    35: 'Unit_Speed',
    36: 'Unit_SpeedAngle',
    37: 'Unit_Acceleration',
    38: 'Unit_AccelerationAngle',
};
exports.BulletActionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_ColorA',
    14: 'Bullet_FaceMovingDir',
    15: 'Bullet_TrackingTarget',
    16: 'Bullet_Destructive',
    17: 'Bullet_DestructiveOnHit',
};
exports.EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};
exports.EmitterConditionDef = [
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_PrewarmDuration, label: "发射器预热时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Duration, label: "发射器持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_ElapsedTime, label: "发射器运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitCount, label: "发射器开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitInterval, label: "发射器开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitOffsetX, label: "发射器开火X偏移", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Angle, label: "发射器弹道角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Count, label: "发射器弹道数量", type: "number" },
    // 子弹
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Life, label: "单位生命", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_LifePercent, label: "单位生命百分比", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosX, label: "单位X坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosY, label: "单位Y坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Speed, label: "单位速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_SpeedAngle, label: "单位速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Acceleration, label: "单位加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_AccelerationAngle, label: "单位加速度角度", type: "number" },
];
exports.BulletConditionDef = [
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
exports.EmitterActionDef = [
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PrewarmDuration, label: "发射器预热持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Duration, label: "发射器持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_ElapsedTime, label: "发射器已运行时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitCount, label: "发射器每次开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitInterval, label: "发射器每次开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitOffsetX, label: "发射器每次开火偏移X", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Angle, label: "发射器角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Count, label: "发射器数量", type: "number" },
    // 子弹相关
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位相关
];
exports.BulletActionDef = [
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
var EventGroupCategory;
(function (EventGroupCategory) {
    EventGroupCategory["Emitter"] = "Emitter";
    EventGroupCategory["Bullet"] = "Bullet";
})(EventGroupCategory || (exports.EventGroupCategory = EventGroupCategory = {}));
exports.ConditionTypeByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterConditionTypeEnum,
    [EventGroupCategory.Bullet]: exports.BulletConditionTypeEnum,
};
exports.ActionTypeByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterActionTypeEnum,
    [EventGroupCategory.Bullet]: exports.BulletActionTypeEnum,
};
// New definition-based mappings using ValueDecorator
exports.ConditionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterConditionDef,
    [EventGroupCategory.Bullet]: exports.BulletConditionDef,
};
exports.ActionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterActionDef,
    [EventGroupCategory.Bullet]: exports.BulletActionDef,
};
class EventGroupDataManager {
    constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = (0, path_1.join)(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }
    static getInstance() {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }
    /**
     * Get the full path for a category folder
     */
    getCategoryPath(category) {
        return (0, path_1.join)(this.eventsBasePath, category);
    }
    /**
     * Get the full file path for an EventGroupData
     */
    getFilePath(category, name) {
        return (0, path_1.join)(this.getCategoryPath(category), `${name}.json`);
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(dirPath) {
        if (!(0, fs_1.existsSync)(dirPath)) {
            (0, fs_1.mkdirSync)(dirPath, { recursive: true });
        }
    }
    /**
     * Load all EventGroupData files from a category
     */
    loadEventGroupsByCategory(category) {
        const categoryPath = this.getCategoryPath(category);
        if (!(0, fs_1.existsSync)(categoryPath)) {
            return [];
        }
        const files = (0, fs_1.readdirSync)(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups = [];
        for (const file of files) {
            try {
                const filePath = (0, path_1.join)(categoryPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
                const data = JSON.parse(content);
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            }
            catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }
        return eventGroups;
    }
    /**
     * Load a specific EventGroupData by name and category
     */
    loadEventGroup(category, name) {
        const filePath = this.getFilePath(category, name);
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        try {
            const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
            const data = JSON.parse(content);
            data.name = name; // Ensure name is correct
            return data;
        }
        catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }
    /**
     * Save an EventGroupData to file
     */
    saveEventGroup(category, eventGroup) {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
            return true;
        }
        catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }
    /**
     * Delete an EventGroupData file
     */
    deleteEventGroup(category, name) {
        try {
            const filePath = this.getFilePath(category, name);
            if ((0, fs_1.existsSync)(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }
    /**
     * Check if an EventGroupData file exists
     */
    eventGroupExists(category, name) {
        const filePath = this.getFilePath(category, name);
        return (0, fs_1.existsSync)(filePath);
    }
    /**
     * Find an EventGroupData by name across all categories
     */
    findEventGroup(name) {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }
    /**
     * Generate a unique name for a new EventGroupData
     */
    generateUniqueName(category, baseName = 'EventGroup') {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        return name;
    }
    /**
     * Create a new EventGroupData with default values
     */
    createNewEventGroup(category, name) {
        const finalName = name || this.generateUniqueName(category);
        var eventGroup = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };
        return eventGroup;
    }
    /**
     * Duplicate an existing EventGroupData
     */
    duplicateEventGroup(category, originalName, newName) {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }
        const finalName = newName || this.generateUniqueName(category, originalName);
        // Deep copy the original data
        const duplicate = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => (Object.assign({}, condition))),
            actions: original.actions.map(action => (Object.assign({}, action)))
        };
        return duplicate;
    }
    /**
     * Get all EventGroupData names from all categories
     */
    getAllEventGroupNames() {
        const result = {};
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        return result;
    }
}
exports.EventGroupDataManager = EventGroupDataManager;
//# sourceMappingURL=data:application/json;base64,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