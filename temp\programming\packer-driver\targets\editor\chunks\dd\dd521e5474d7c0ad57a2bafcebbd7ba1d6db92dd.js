System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, find, resources, JsonAsset, path, eEventGroupStatus, EventGroupData, BulletSystem, _crd, ccclass, join;

  function _reportPossibleCrUseOfBulletController(extras) {
    _reporterNs.report("BulletController", "./BulletController", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventGroupStatus(extras) {
    _reporterNs.report("eEventGroupStatus", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export("BulletSystem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      find = _cc.find;
      resources = _cc.resources;
      JsonAsset = _cc.JsonAsset;
      path = _cc.path;
    }, function (_unresolved_2) {
      eEventGroupStatus = _unresolved_2.eEventGroupStatus;
    }, function (_unresolved_3) {
      EventGroupData = _unresolved_3.EventGroupData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'find', 'Vec3', 'Node', 'resources', 'JsonAsset', 'path']);

      ({
        ccclass
      } = _decorator);
      ({
        join
      } = path);
      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */

      _export("BulletSystem", BulletSystem = class BulletSystem {
        /**
         * Main update loop
         */
        static tick(dt) {
          this.tickEmitters(dt);
          this.tickBullets(dt);
          this.tickEventGroups(dt);
        }

        static tickEmitters(dt) {
          for (const emitter of this.allEmitters) {
            emitter.tick(dt);
          }
        }

        static tickBullets(dt) {
          for (const bullet of this.allBullets) {
            bullet.tick(dt);
          }
        }

        static tickEventGroups(dt) {
          for (let i = this.allEventGroups.length - 1; i >= 0; i--) {
            const group = this.allEventGroups[i];
            group.tick(dt);

            if (group.status === (_crd && eEventGroupStatus === void 0 ? (_reportPossibleCrUseOfeEventGroupStatus({
              error: Error()
            }), eEventGroupStatus) : eEventGroupStatus).Stopped) {
              this.allEventGroups.splice(i, 1);
            }
          }
        }

        static onCreateEmitter(emitter) {
          for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
              return;
            }
          }

          this.allEmitters.push(emitter);

          if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
              const foundNode = find(this.bulletParentPath);

              if (foundNode) {
                this.bulletParent = foundNode;
              } else {
                console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                this.bulletParent = emitter.node;
              }
            }
          }
        }

        static onDestroyEmitter(emitter) {
          this.allEmitters = this.allEmitters.filter(e => e !== emitter);
        }

        static onCreateBullet(emitter, bullet) {
          // 这个检查是否会比较冗余
          // for (let i = 0; i < this.allBullets.length; i++) {
          //     if (this.allBullets[i] === bullet) {
          //         return;
          //     }
          // }
          bullet.onCreate(emitter);
          this.allBullets.push(bullet);
          bullet.node.setParent(this.bulletParent, true);
        }

        static onDestroyBullet(bullet) {
          bullet.destroySelf();
          this.allBullets = this.allBullets.filter(b => b !== bullet);
        }

        static destroyAllBullets() {
          for (const bullet of this.allBullets) {
            bullet.destroySelf();
          }

          this.allBullets = [];
        }

        static loadEmitterEventGroup(name) {
          // the name is the json file name
          let finalPath = join(this.emitterEventGroupPath, name + '.json');
          let json = resources.load(finalPath, JsonAsset);
          let data = (_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
            error: Error()
          }), EventGroupData) : EventGroupData).fromJSON(json);
          return data;
        }

        static loadBulletEventGroup(name) {
          // the name is the json file name
          let finalPath = join(this.bulletEventGroupPath, name + '.json');
          let json = resources.load(finalPath, JsonAsset);
          let data = (_crd && EventGroupData === void 0 ? (_reportPossibleCrUseOfEventGroupData({
            error: Error()
          }), EventGroupData) : EventGroupData).fromJSON(json);
          return data;
        }
        /**
         * Called when a new event group is created or turn active
         */


        static onCreateEventGroup(eventGroup) {
          this.allEventGroups.push(eventGroup);
        }

        static onDestroyEventGroup(eventGroup) {
          this.allEventGroups = this.allEventGroups.filter(g => g !== eventGroup);
        }

      });

      BulletSystem.bulletParentPath = 'Canvas/GameUI/bullet_root';
      BulletSystem.emitterEventGroupPath = 'emitter/events/Emitter';
      BulletSystem.bulletEventGroupPath = 'emitter/events/Bullet';

      /**
       * All active bullets
       */
      BulletSystem.allBullets = [];

      /**
       * All active emitters
       */
      BulletSystem.allEmitters = [];

      /**
       * All active action groups
       */
      BulletSystem.allEventGroups = [];
      // public static isEmitterEnabled: boolean = true;
      // public static isBulletEnabled: boolean = true;
      BulletSystem.bulletParent = void 0;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=dd521e5474d7c0ad57a2bafcebbd7ba1d6db92dd.js.map