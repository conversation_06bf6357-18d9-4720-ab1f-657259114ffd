{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/BulletEventActions.ts"], "names": ["BulletActionBase", "BulletAction_Duration", "BulletAction_ElapsedTime", "BulletAction_PosX", "BulletAction_PosY", "BulletAction_Speed", "BulletAction_SpeedAngle", "BulletAction_Acceleration", "BulletAction_AccelerationAngle", "BulletAction_Scale", "BulletAction_ColorR", "BulletAction_ColorG", "BulletAction_ColorB", "BulletAction_FacingMoveDir", "BulletAction_Destructive", "BulletAction_DestructiveOnHit", "Easing", "constructor", "data", "_isCompleted", "_elapsedTime", "_startValue", "isCompleted", "canLerp", "onLoad", "context", "onExecute", "dt", "duration", "executeInternal", "targetValue", "lerp<PERSON><PERSON>ue", "startValue", "lerp", "easing", "Math", "min", "value", "bullet", "elapsedTime", "position", "node", "setPosition", "y", "x", "speed", "speedAngle", "acceleration", "accelerationAngle", "isFacingMoveDir"], "mappings": ";;;sCAMaA,gB,EA8CAC,qB,EAMAC,wB,EAMAC,iB,EAOAC,iB,EAOAC,kB,EAMAC,uB,EAMAC,yB,EAMAC,8B,EAMAC,kB,EAMAC,mB,EAMAC,mB,EAMAC,mB,EAMAC,0B,EAMAC,wB,EAMAC,6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxIKC,MAAAA,M,iBAAAA,M;;;;;;;kCAILhB,gB,GAAN,MAAMA,gBAAN,CAA+C;AAOlDiB,QAAAA,WAAW,CAACC,IAAD,EAAwB;AAAA,eAN1BA,IAM0B;AAAA,eAJzBC,YAIyB,GAJD,KAIC;AAAA,eAHzBC,YAGyB,GAHF,CAGE;AAAA,eAFzBC,WAEyB,GAFH,CAEG;AAC/B,eAAKH,IAAL,GAAYA,IAAZ;AACH;;AAEDI,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKH,YAAZ;AACH;;AAEDI,QAAAA,OAAO,GAAY;AACf,iBAAO,IAAP;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,eAAKN,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,CAApB,CAFqC,CAGrC;;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACH;;AAEDK,QAAAA,SAAS,CAACD,OAAD,EAA6BE,EAA7B,EAA+C;AACpD,eAAKP,YAAL,IAAqBO,EAArB;;AACA,cAAI,KAAKP,YAAL,IAAqB,KAAKF,IAAL,CAAUU,QAAnC,EAA6C;AACzC,iBAAKC,eAAL,CAAqBJ,OAArB,EAA8B,KAAKP,IAAL,CAAUY,WAAxC;AACA,iBAAKX,YAAL,GAAoB,IAApB;AACH,WAHD,MAIK,IAAI,KAAKI,OAAL,EAAJ,EAAoB;AACrB,iBAAKM,eAAL,CAAqBJ,OAArB,EAA8B,KAAKM,SAAL,CAAe,KAAKV,WAApB,EAAiC,KAAKH,IAAL,CAAUY,WAA3C,CAA9B;AACH;AACJ;;AAEDC,QAAAA,SAAS,CAACC,UAAD,EAAqBF,WAArB,EAAkD;AACvD,iBAAO;AAAA;AAAA,gCAAOG,IAAP,CAAY,KAAKf,IAAL,CAAUgB,MAAtB,EAA8BF,UAA9B,EAA0CF,WAA1C,EAAuDK,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKhB,YAAL,GAAoB,KAAKF,IAAL,CAAUU,QAA5C,CAAvD,CAAP;AACH;;AAESC,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AA3CiD,O;;uCA8CzCpC,qB,GAAN,MAAMA,qBAAN,SAAoCD,gBAApC,CAAqD;AAC9C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBV,QAAhB,CAAyBS,KAAzB,GAAiCA,KAAjC;AACH;;AAHuD,O;;0CAM/CnC,wB,GAAN,MAAMA,wBAAN,SAAuCF,gBAAvC,CAAwD;AACjD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBC,WAAhB,GAA8BF,KAA9B;AACH;;AAH0D,O;;mCAMlDlC,iB,GAAN,MAAMA,iBAAN,SAAgCH,gBAAhC,CAAiD;AAC1C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvE,cAAMG,QAAQ,GAAGf,OAAO,CAACa,MAAR,CAAgBG,IAAhB,CAAqBD,QAAtC;AACAf,UAAAA,OAAO,CAACa,MAAR,CAAgBG,IAAhB,CAAqBC,WAArB,CAAiCL,KAAjC,EAAwCG,QAAQ,CAACG,CAAjD;AACH;;AAJmD,O;;mCAO3CvC,iB,GAAN,MAAMA,iBAAN,SAAgCJ,gBAAhC,CAAiD;AAC1C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvE,cAAMG,QAAQ,GAAGf,OAAO,CAACa,MAAR,CAAgBG,IAAhB,CAAqBD,QAAtC;AACAf,UAAAA,OAAO,CAACa,MAAR,CAAgBG,IAAhB,CAAqBC,WAArB,CAAiCF,QAAQ,CAACI,CAA1C,EAA6CP,KAA7C;AACH;;AAJmD,O;;oCAO3ChC,kB,GAAN,MAAMA,kBAAN,SAAiCL,gBAAjC,CAAkD;AAC3C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBO,KAAhB,CAAsBR,KAAtB,GAA8BA,KAA9B;AACH;;AAHoD,O;;yCAM5C/B,uB,GAAN,MAAMA,uBAAN,SAAsCN,gBAAtC,CAAuD;AAChD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBQ,UAAhB,CAA2BT,KAA3B,GAAmCA,KAAnC;AACH;;AAHyD,O;;2CAMjD9B,yB,GAAN,MAAMA,yBAAN,SAAwCP,gBAAxC,CAAyD;AAClD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBS,YAAhB,CAA6BV,KAA7B,GAAqCA,KAArC;AACH;;AAH2D,O;;gDAMnD7B,8B,GAAN,MAAMA,8BAAN,SAA6CR,gBAA7C,CAA8D;AACvD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBU,iBAAhB,CAAkCX,KAAlC,GAA0CA,KAA1C;AACH;;AAHgE,O;;oCAMxD5B,kB,GAAN,MAAMA,kBAAN,SAAiCT,gBAAjC,CAAkD;AAC3C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAHoD,O;;qCAM5C3B,mB,GAAN,MAAMA,mBAAN,SAAkCV,gBAAlC,CAAmD;AAC5C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAHqD,O;;qCAM7C1B,mB,GAAN,MAAMA,mBAAN,SAAkCX,gBAAlC,CAAmD;AAC5C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAHqD,O;;qCAM7CzB,mB,GAAN,MAAMA,mBAAN,SAAkCZ,gBAAlC,CAAmD;AAC5C6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAHqD,O;;4CAM7CxB,0B,GAAN,MAAMA,0BAAN,SAAyCb,gBAAzC,CAA0D;AACnD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,MAAR,CAAgBW,eAAhB,CAAgCZ,KAAhC,GAAyCA,KAAK,KAAK,CAAnD;AACH;;AAH4D,O;;0CAMpDvB,wB,GAAN,MAAMA,wBAAN,SAAuCd,gBAAvC,CAAwD;AACjD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAH0D,O;;+CAMlDtB,6B,GAAN,MAAMA,6BAAN,SAA4Cf,gBAA5C,CAA6D;AACtD6B,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AAH+D,O", "sourcesContent": ["import { IEventAction } from \"./IEventAction\";\r\nimport { eEmitterProp } from \"../Emitter\";\r\nimport { eEasing, Easing} from \"../Easing\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\nimport { EventActionData } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class BulletActionBase implements IEventAction {\r\n    readonly data: EventActionData;\r\n\r\n    protected _isCompleted: boolean = false;\r\n    protected _elapsedTime: number = 0;\r\n    protected _startValue: number = 0;\r\n\r\n    constructor(data: EventActionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n\r\n    canLerp(): boolean {\r\n        return true;\r\n    }\r\n\r\n    onLoad(context: EventGroupContext): void {\r\n        this._isCompleted = false;\r\n        this._elapsedTime = 0;\r\n        // override this to get the correct start value\r\n        this._startValue = 0;\r\n    }\r\n\r\n    onExecute(context: EventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        if (this._elapsedTime >= this.data.duration) {\r\n            this.executeInternal(context, this.data.targetValue);\r\n            this._isCompleted = true;\r\n        }\r\n        else if (this.canLerp()) {\r\n            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));\r\n        }\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        return Easing.lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // Default implementation does nothing\r\n    }\r\n}\r\n\r\nexport class BulletAction_Duration extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.duration.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ElapsedTime extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.elapsedTime = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_PosX extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        const position = context.bullet!.node.position;\r\n        context.bullet!.node.setPosition(value, position.y);\r\n    }\r\n}\r\n\r\nexport class BulletAction_PosY extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        const position = context.bullet!.node.position;\r\n        context.bullet!.node.setPosition(position.x, value);\r\n    }\r\n}\r\n\r\nexport class BulletAction_Speed extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.speed.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_SpeedAngle extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.speedAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_Acceleration extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.acceleration.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_AccelerationAngle extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.accelerationAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_Scale extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.node.scale.x = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorR extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.bulletSprite.color.r = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorG extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.bulletSprite.color.g = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_ColorB extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.bulletSprite.color.b = value;\r\n    }\r\n}\r\n\r\nexport class BulletAction_FacingMoveDir extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.bullet!.isFacingMoveDir.value = (value === 1);\r\n    }\r\n}\r\n\r\nexport class BulletAction_Destructive extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.isDestructive.value = (value === 1);\r\n    }\r\n}\r\n\r\nexport class BulletAction_DestructiveOnHit extends BulletActionBase {\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.bullet!.isPreWarm.value = (value === 1);\r\n    }\r\n}"]}