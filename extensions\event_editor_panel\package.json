{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "event_editor_panel", "version": "1.0.0", "author": "Cocos Creator", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "npx tsc"}, "description": "i18n:event_editor_panel.description", "main": "./dist/main.js", "dependencies": {"vue": "^3.1.4", "fs-extra": "^10.0.0"}, "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/fs-extra": "^9.0.5", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "compilerOptions": {"paths": {"@project/*": ["../../assets/scripts/*"]}}, "panels": {"default": {"title": "event_editor_panel Default Panel", "type": "dockable", "main": "dist/panels/default", "size": {"min-width": 400, "min-height": 300, "width": 1024, "height": 600}}}, "contributions": {"menu": [{"path": "i18n:menu.panel/event_editor_panel", "label": "i18n:event_editor_panel.open_panel", "message": "open-panel"}, {"path": "i18n:menu.develop/event_editor_panel", "label": "i18n:event_editor_panel.send_to_panel", "message": "send-to-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}, "open-event-group": {"methods": ["openEventGroup"]}, "select-event-group": {"methods": ["selectEventGroup"]}, "send-to-panel": {"methods": ["default.hello"]}}}}