// Basic type declarations for Cocos Creator Editor API
declare namespace Editor {
    namespace Project {
        const path: string;
    }
    
    namespace Message {
        function send(extension: string, message: string, ...args: any[]): void;
        function request(extension: string, message: string, ...args: any[]): Promise<any>;
    }
    
    namespace Panel {
        function open(panelName: string): void;
    }

    namespace Dialog {
        function open(filePath: string): void;
        function save(filePath: string, content: string): void;
        function close(): void;

        // info
        function showInfo(message: string): void;
    }
}

declare const EDITOR: boolean;
