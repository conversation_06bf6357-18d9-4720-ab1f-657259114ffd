<div class="event-editor">
  <!-- Header with toolbar -->
  <div class="toolbar">
    <div class="toolbar-left">
      <button @click="saveCurrentEventGroup" :disabled="!state.selectedEventGroup || !state.isDirty" class="btn btn-primary">
        💾 Save
      </button>
      <button @click="reloadEventGroups" class="btn btn-secondary">
        🔄 Reload
      </button>
      <button @click="undo" :disabled="state.undoStack.length === 0" class="btn btn-secondary">
        ↶ Undo
      </button>
      <button @click="redo" :disabled="state.redoStack.length === 0" class="btn btn-secondary">
        ↷ Redo
      </button>
    </div>
    <div class="toolbar-right">
      <input 
        v-model="state.searchQuery" 
        placeholder="Search event groups..." 
        class="search-input"
      />
    </div>
  </div>

  <div class="main-content">
    <!-- Left panel: Category tabs and event group list -->
    <div class="left-panel">
      <!-- Category tabs -->
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category"
          @click="state.selectedCategory = category"
          :class="['tab', { active: state.selectedCategory === category }]"
        >
          {{ category }}
        </button>
      </div>

      <!-- Event group list -->
      <div class="event-group-list">
        <div class="list-header">
          <span>Event Groups</span>
          <div class="list-actions">
            <button @click="createNewEventGroup" class="btn btn-small btn-primary" title="New Event Group">
              ➕
            </button>
            <button @click="duplicateEventGroup" :disabled="!state.selectedEventGroup" class="btn btn-small btn-secondary" title="Duplicate">
              📋
            </button>
          </div>
        </div>
        
        <div class="list-content">
          <div 
            v-for="eventGroup in filteredEventGroups" 
            :key="eventGroup.name"
            @click="selectEventGroup(eventGroup)"
            :class="['list-item', { 
              active: state.selectedEventGroup?.name === eventGroup.name,
              dirty: state.selectedEventGroup?.name === eventGroup.name && state.isDirty
            }]"
          >
            <span class="item-name">
              {{ state.selectedEventGroup?.name === eventGroup.name && state.isDirty ? '*' : '' }}{{ eventGroup.name }}
            </span>
            <button 
              @click.stop="deleteEventGroup(eventGroup)" 
              class="btn btn-small btn-danger delete-btn"
              title="Delete"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Right panel: Event group editor -->
    <div class="right-panel">
      <div v-if="!state.selectedEventGroup" class="no-selection">
        <p>Select an event group to edit</p>
      </div>
      
      <div v-else class="event-group-editor">
        <div class="editor-header">
          <h3>{{ state.selectedEventGroup.name }}</h3>
        </div>
        
        <div class="editor-content">
          <!-- Basic Properties -->
          <div class="property-group">
            <label class="property-label">Name:</label>
            <input 
              v-model="state.selectedEventGroup.name" 
              @input="markDirty"
              class="property-input"
              type="text"
            />
          </div>
          
          <div class="property-group">
            <label class="property-label">Trigger Count:</label>
            <input 
              v-model.number="state.selectedEventGroup.triggerCount" 
              @input="markDirty"
              class="property-input"
              type="number"
              min="-1"
            />
            <span class="property-hint">(-1 for infinite)</span>
          </div>

          <!-- Conditions Section -->
          <div class="section">
            <div class="section-header">
              <h4>Conditions</h4>
              <button @click="addCondition" class="btn btn-small btn-primary">Add Condition</button>
            </div>
            <div class="conditions-list">
              <div 
                v-for="(condition, index) in state.selectedEventGroup.conditions" 
                :key="index"
                class="condition-item"
              >
                <div class="property-row">
                  <label>Operation:</label>
                  <select v-model.number="condition.op" @change="markDirty" class="property-select">
                    <option v-for="(label, value) in ConditionOpEnum" :key="value" :value="parseInt(value)">
                      {{ label }}
                    </option>
                  </select>
                </div>

                <div class="property-row">
                  <label>Type:</label>
                  <select v-model.number="condition.type" @change="markDirty" class="property-select">
                    <option v-for="def in EventConditionDef" :key="def.enum" :value="def.enum">
                      {{ def.label }}
                    </option>
                  </select>
                </div>

                <div class="property-row">
                  <label>Compare Op:</label>
                  <select v-model.number="condition.compareOp" @change="markDirty" class="property-select">
                    <option v-for="(label, value) in CompareOpEnum" :key="value" :value="parseInt(value)">
                      {{ label }}
                    </option>
                  </select>
                </div>
                
                <div class="property-row">
                  <label>Target Value:</label>
                  <!-- Type-aware input based on condition type -->
                  <template v-if="getConditionDef(condition.type)?.type === 'boolean'">
                    <select v-model.number="condition.targetValue" @change="markDirty" class="property-select">
                      <option :value="0">False</option>
                      <option :value="1">True</option>
                    </select>
                  </template>
                  <template v-else-if="getConditionDef(condition.type)?.type === 'string'">
                    <input v-model="condition.targetValue" @input="markDirty" class="property-input" type="text" />
                  </template>
                  <template v-else>
                    <input v-model.number="condition.targetValue" @input="markDirty" class="property-input" type="number" step="0.01" />
                  </template>
                </div>
                
                <button @click="removeCondition(index)" class="btn btn-small btn-danger">Remove</button>
              </div>
            </div>
          </div>

          <!-- Actions Section -->
          <div class="section">
            <div class="section-header">
              <h4>Actions</h4>
              <button @click="addAction" class="btn btn-small btn-primary">Add Action</button>
            </div>
            <div class="actions-list">
              <div 
                v-for="(action, index) in state.selectedEventGroup.actions" 
                :key="index"
                class="action-item"
              >
                <div class="property-row">
                  <label>Type:</label>
                  <select v-model.number="action.type" @change="markDirty" class="property-select">
                    <option v-for="def in EventActionDef" :key="def.enum" :value="def.enum">
                      {{ def.label }}
                    </option>
                  </select>
                </div>

                <div class="property-row">
                  <label>Duration:</label>
                  <input v-model.number="action.duration" @input="markDirty" class="property-input" type="number" step="0.01" />
                </div>

                <div class="property-row">
                  <label>Target Value:</label>
                  <!-- Type-aware input based on action type -->
                  <template v-if="getActionDef(action.type)?.type === 'boolean'">
                    <select v-model.number="action.targetValue" @change="markDirty" class="property-select">
                      <option :value="0">False</option>
                      <option :value="1">True</option>
                    </select>
                  </template>
                  <template v-else-if="getActionDef(action.type)?.type === 'string'">
                    <input v-model="action.targetValue" @input="markDirty" class="property-input" type="text" />
                  </template>
                  <template v-else>
                    <input v-model.number="action.targetValue" @input="markDirty" class="property-input" type="number" step="0.01" />
                  </template>
                </div>

                <div class="property-row">
                  <label>Easing:</label>
                  <select v-model.number="action.easing" @change="markDirty" class="property-select">
                    <option v-for="(label, value) in EasingEnum" :key="value" :value="parseInt(value)">
                      {{ label }}
                    </option>
                  </select>
                </div>
                
                <button @click="removeAction(index)" class="btn btn-small btn-danger">Remove</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
