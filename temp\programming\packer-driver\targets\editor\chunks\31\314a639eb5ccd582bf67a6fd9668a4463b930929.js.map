{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/BulletEventConditions.ts"], "names": ["BulletConditionBase", "BulletCondition_Duration", "BulletCondition_ElapsedTime", "BulletCondition_PosX", "BulletCondition_PosY", "BulletCondition_Damage", "BulletCondition_Speed", "BulletCondition_SpeedAngle", "BulletCondition_Acceleration", "BulletCondition_AccelerationAngle", "BulletCondition_Scale", "BulletCondition_ColorR", "BulletCondition_ColorG", "BulletCondition_ColorB", "BulletCondition_FacingMoveDir", "BulletCondition_Destructive", "BulletCondition_DestructiveOnHit", "Comparer", "eCompareOp", "constructor", "data", "evaluate", "context", "compare", "bullet", "duration", "value", "targetValue", "compareOp", "elapsedTime", "node", "position", "x", "y", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "bulletSprite", "color", "r", "g", "b", "Equal", "isFacingMoveDir", "NotEqual"], "mappings": ";;;oDAIaA,mB,EAYAC,wB,EAOAC,2B,EAMAC,oB,EAMAC,oB,EAMAC,sB,EAOAC,qB,EAMAC,0B,EAMAC,4B,EAMAC,iC,EAMAC,qB,EAMAC,sB,EAMAC,sB,EAMAC,sB,EAMAC,6B,EAaAC,2B,EAaAC,gC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzHeC,MAAAA,Q,iBAAAA,Q;;AACCC,MAAAA,U,iBAAAA,U;;;;;;;qCAEhBlB,mB,GAAN,MAAMA,mBAAN,CAAqD;AAGxDmB,QAAAA,WAAW,CAACC,IAAD,EAA2B;AAAA,eAF7BA,IAE6B;AAClC,eAAKA,IAAL,GAAYA,IAAZ;AACH;;AAEMC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO,IAAP;AACH;;AATuD,O;;0CAY/CrB,wB,GAAN,MAAMA,wBAAN,SAAuCD,mBAAvC,CAA2D;AACvDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBC,QAAhB,CAAyBC,KAA1C,EAAiD,KAAKN,IAAL,CAAUO,WAA3D,EAAwE,KAAKP,IAAL,CAAUQ,SAAlF,CAAP;AACH;;AAJ6D,O;;6CAOrD1B,2B,GAAN,MAAMA,2BAAN,SAA0CF,mBAA1C,CAA8D;AAC1DqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBK,WAAjC,EAA8C,KAAKT,IAAL,CAAUO,WAAxD,EAAqE,KAAKP,IAAL,CAAUQ,SAA/E,CAAP;AACH;;AAHgE,O;;sCAMxDzB,oB,GAAN,MAAMA,oBAAN,SAAmCH,mBAAnC,CAAuD;AACnDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAArB,CAA8BC,CAA/C,EAAkD,KAAKZ,IAAL,CAAUO,WAA5D,EAAyE,KAAKP,IAAL,CAAUQ,SAAnF,CAAP;AACH;;AAHyD,O;;sCAMjDxB,oB,GAAN,MAAMA,oBAAN,SAAmCJ,mBAAnC,CAAuD;AACnDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBC,QAArB,CAA8BE,CAA/C,EAAkD,KAAKb,IAAL,CAAUO,WAA5D,EAAyE,KAAKP,IAAL,CAAUQ,SAAnF,CAAP;AACH;;AAHyD,O;;wCAMjDvB,sB,GAAN,MAAMA,sBAAN,SAAqCL,mBAArC,CAAyD;AACrDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAO,KAAP;AACH;;AAJ2D,O;;uCAOnDhB,qB,GAAN,MAAMA,qBAAN,SAAoCN,mBAApC,CAAwD;AACpDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBU,KAAhB,CAAsBR,KAAvC,EAA8C,KAAKN,IAAL,CAAUO,WAAxD,EAAqE,KAAKP,IAAL,CAAUQ,SAA/E,CAAP;AACH;;AAH0D,O;;4CAMlDrB,0B,GAAN,MAAMA,0BAAN,SAAyCP,mBAAzC,CAA6D;AACzDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBW,UAAhB,CAA2BT,KAA5C,EAAmD,KAAKN,IAAL,CAAUO,WAA7D,EAA0E,KAAKP,IAAL,CAAUQ,SAApF,CAAP;AACH;;AAH+D,O;;8CAMvDpB,4B,GAAN,MAAMA,4BAAN,SAA2CR,mBAA3C,CAA+D;AAC3DqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBY,YAAhB,CAA6BV,KAA9C,EAAqD,KAAKN,IAAL,CAAUO,WAA/D,EAA4E,KAAKP,IAAL,CAAUQ,SAAtF,CAAP;AACH;;AAHiE,O;;mDAMzDnB,iC,GAAN,MAAMA,iCAAN,SAAgDT,mBAAhD,CAAoE;AAChEqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBa,iBAAhB,CAAkCX,KAAnD,EAA0D,KAAKN,IAAL,CAAUO,WAApE,EAAiF,KAAKP,IAAL,CAAUQ,SAA3F,CAAP;AACH;;AAHsE,O;;uCAM9DlB,qB,GAAN,MAAMA,qBAAN,SAAoCV,mBAApC,CAAwD;AACpDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBM,IAAhB,CAAqBQ,KAArB,CAA2BN,CAA5C,EAA+C,KAAKZ,IAAL,CAAUO,WAAzD,EAAsE,KAAKP,IAAL,CAAUQ,SAAhF,CAAP;AACH;;AAH0D,O;;wCAMlDjB,sB,GAAN,MAAMA,sBAAN,SAAqCX,mBAArC,CAAyD;AACrDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBe,YAAhB,CAA6BC,KAA7B,CAAmCC,CAApD,EAAuD,KAAKrB,IAAL,CAAUO,WAAjE,EAA8E,KAAKP,IAAL,CAAUQ,SAAxF,CAAP;AACH;;AAH2D,O;;wCAMnDhB,sB,GAAN,MAAMA,sBAAN,SAAqCZ,mBAArC,CAAyD;AACrDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBe,YAAhB,CAA6BC,KAA7B,CAAmCE,CAApD,EAAuD,KAAKtB,IAAL,CAAUO,WAAjE,EAA8E,KAAKP,IAAL,CAAUQ,SAAxF,CAAP;AACH;;AAH2D,O;;wCAMnDf,sB,GAAN,MAAMA,sBAAN,SAAqCb,mBAArC,CAAyD;AACrDqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASC,OAAT,CAAiBD,OAAO,CAACE,MAAR,CAAgBe,YAAhB,CAA6BC,KAA7B,CAAmCG,CAApD,EAAuD,KAAKvB,IAAL,CAAUO,WAAjE,EAA8E,KAAKP,IAAL,CAAUQ,SAAxF,CAAP;AACH;;AAH2D,O;;+CAMnDd,6B,GAAN,MAAMA,6BAAN,SAA4Cd,mBAA5C,CAAgE;AAC5DqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKF,IAAL,CAAUQ,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWgB,KAAhB;AACI,qBAAOtB,OAAO,CAACE,MAAR,CAAgBqB,eAAhB,CAAgCnB,KAAhC,MAA2C,KAAKN,IAAL,CAAUO,WAAV,KAA0B,CAArE,IAA0E,IAA1E,GAAiF,KAAxF;;AACJ,iBAAK;AAAA;AAAA,0CAAWmB,QAAhB;AACI,qBAAOxB,OAAO,CAACE,MAAR,CAAgBqB,eAAhB,CAAgCnB,KAAhC,MAA2C,KAAKN,IAAL,CAAUO,WAAV,KAA0B,CAArE,IAA0E,IAA1E,GAAiF,KAAxF;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVkE,O;;6CAa1DZ,2B,GAAN,MAAMA,2BAAN,SAA0Cf,mBAA1C,CAA8D;AAC1DqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKF,IAAL,CAAUQ,SAAlB;AACI;AACA;AACA;AACA;AACA;AACI,qBAAO,KAAP;AANR;AAQH;;AAVgE,O;;kDAaxDZ,gC,GAAN,MAAMA,gCAAN,SAA+ChB,mBAA/C,CAAmE;AAC/DqB,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKF,IAAL,CAAUQ,SAAlB;AACI;AACA;AACA;AACA;AACA;AACI,qBAAO,KAAP;AANR;AAQH;;AAVqE,O", "sourcesContent": ["import { IEventCondition } from \"./IEventCondition\";\r\nimport { EventGroupContext, Comparer } from \"../EventGroup\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class BulletConditionBase implements IEventCondition {\r\n    readonly data: EventConditionData;\r\n\r\n    constructor(data: EventConditionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return true;\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Duration extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // Custom evaluation logic for active condition\r\n        return Comparer.compare(context.bullet!.duration.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ElapsedTime extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.elapsedTime, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_PosX extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.position.x, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_PosY extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.position.y, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Damage extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // return Comparer.compare(context.bullet!.damage.value, this.data.targetValue, this.data.compareOp);\r\n        return false;\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Speed extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.speed.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_SpeedAngle extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.speedAngle.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Acceleration extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.acceleration.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_AccelerationAngle extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.accelerationAngle.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Scale extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.node.scale.x, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorR extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.bulletSprite.color.r, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorG extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.bulletSprite.color.g, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_ColorB extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.bullet!.bulletSprite.color.b, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class BulletCondition_FacingMoveDir extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {        \r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.bullet!.isFacingMoveDir.value === (this.data.targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.bullet!.isFacingMoveDir.value !== (this.data.targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class BulletCondition_Destructive extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            // case eCompareOp.Equal:\r\n            //     return context.bullet!.isDestructive.value === (this.data.targetValue === 1) ? true : false;\r\n            // case eCompareOp.NotEqual:\r\n            //     return context.bullet!.isDestructive.value !== (this.data.targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class BulletCondition_DestructiveOnHit extends BulletConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            // case eCompareOp.Equal:\r\n            //     return context.bullet!.isPreWarm.value === (this.data.targetValue === 1) ? true : false;\r\n            // case eCompareOp.NotEqual:\r\n            //     return context.bullet!.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}"]}