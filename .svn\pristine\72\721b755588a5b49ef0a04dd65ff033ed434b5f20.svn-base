/* eslint-disable vue/one-component-per-file */

import { readFileSync } from 'fs';
import { join } from 'path';
// @ts-ignore
import { createApp, defineComponent, reactive, computed, onMounted } from 'vue';
import {
    EventGroupDataManager,
    EventGroupData,
    EventGroupCategory,
    ConditionOpEnum,
    CompareOpEnum,
    EasingEnum,
    ConditionDefByCategory,
    ActionDefByCategory,
    ValueDecorator
} from '../../utils/EventGroupDataManager';

const panelDataMap = new WeakMap<any, any>();

/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('Event Editor Panel shown'); },
        hide() { console.log('Event Editor Panel hidden'); },
    },
    messages: {
        'select-event-group'(eventGroupName: string) {
            // Call the selectEventGroup method defined in methods section
            const panel = this as any;
            if (panel.methods && panel.methods.selectEventGroup) {
                panel.methods.selectEventGroup.call(panel, eventGroupName);
            }
        },
    },
    template: readFileSync(join(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: readFileSync(join(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * Open and select a specific event group
         */
        selectEventGroup(eventGroupName: string) {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                // Send message to Vue app to select the event group
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.selectEventGroup) {
                    vueInstance.selectEventGroup(eventGroupName);
                }
            }
        },

        /**
         * Reload all event group data
         */
        reloadEventGroups() {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.reloadEventGroups) {
                    vueInstance.reloadEventGroups();
                }
            }
        }
    },
    ready() {
        if (this.$.app) {
            const app = createApp({});
            app.config.compilerOptions.isCustomElement = (tag: string) => tag.startsWith('ui-');

            // Main Event Editor Component
            app.component('EventEditor', defineComponent({
                setup() {
                    const manager = EventGroupDataManager.getInstance();

                    // Reactive state
                    const state = reactive({
                        selectedCategory: EventGroupCategory.Emitter as EventGroupCategory,
                        selectedEventGroup: null as EventGroupData | null,
                        eventGroups: {
                            [EventGroupCategory.Emitter]: [] as EventGroupData[],
                            [EventGroupCategory.Bullet]: [] as EventGroupData[]
                        },
                        searchQuery: '',
                        isDirty: false,
                        undoStack: [] as EventGroupData[],
                        redoStack: [] as EventGroupData[]
                    });

                    // Computed properties
                    const filteredEventGroups = computed(() => {
                        const groups = state.eventGroups[state.selectedCategory];
                        if (!state.searchQuery) return groups;

                        return groups.filter((group: EventGroupData) =>
                            group.name.toLowerCase().includes(state.searchQuery.toLowerCase())
                        );
                    });

                    const categories = computed(() => Object.values(EventGroupCategory));

                    // Computed definitions based on selected category
                    const EventConditionDef = computed(() => {
                        return ConditionDefByCategory[state.selectedCategory];
                    });

                    const EventActionDef = computed(() => {
                        return ActionDefByCategory[state.selectedCategory];
                    });

                    // Methods
                    const loadEventGroups = () => {
                        for (const category of Object.values(EventGroupCategory)) {
                            state.eventGroups[category] = manager.loadEventGroupsByCategory(category);
                        }
                    };

                    const selectEventGroup = (eventGroup: EventGroupData) => {
                        if (state.isDirty) {
                            // TODO: Show confirmation dialog
                        }

                        // Save current state to undo stack
                        if (state.selectedEventGroup) {
                            state.undoStack.push({ ...state.selectedEventGroup });
                            state.redoStack = []; // Clear redo stack
                        }

                        state.selectedEventGroup = { ...eventGroup };
                        state.isDirty = false;
                    };

                    const selectEventGroupByName = (name: string) => {
                        const found = manager.findEventGroup(name);
                        if (found) {
                            state.selectedCategory = found.category;
                            selectEventGroup(found.data);
                        }
                    };

                    const saveEventGroups = () => {
                        if (!state.isDirty) return false;
                        
                        const group = state.eventGroups[state.selectedCategory];
                        group.forEach(eventGroup => {
                            manager.saveEventGroup(state.selectedCategory, eventGroup);
                        });
                        
                        state.isDirty = false;
                        loadEventGroups(); // Reload to reflect changes

                        return true;
                    };

                    const createNewEventGroup = () => {
                        const newName = manager.generateUniqueName(state.selectedCategory);
                        const existedEventGroup = state.eventGroups[state.selectedCategory].find(eg => eg.name === newName);
                        if (existedEventGroup) {
                            // If the event group already exists, select it
                            // But this also means we get a duplicate name
                            selectEventGroup(existedEventGroup);
                        } else {
                            const newEventGroup = manager.createNewEventGroup(state.selectedCategory);
                            state.eventGroups[state.selectedCategory].push(newEventGroup);
                            state.isDirty = true;
                            selectEventGroup(newEventGroup);
                        }
                    };

                    const duplicateEventGroup = () => {
                        if (!state.selectedEventGroup) return;

                        const duplicate = manager.duplicateEventGroup(
                            state.selectedCategory,
                            state.selectedEventGroup.name
                        );

                        if (duplicate) {
                            state.eventGroups[state.selectedCategory].push(duplicate);
                            state.isDirty = true;
                            selectEventGroup(duplicate);
                        }
                    };

                    const deleteEventGroup = (eventGroup: EventGroupData) => {
                        if (confirm(`Are you sure you want to delete "${eventGroup.name}"?`)) {
                            manager.deleteEventGroup(state.selectedCategory, eventGroup.name);
                            loadEventGroups();

                            if (state.selectedEventGroup?.name === eventGroup.name) {
                                state.selectedEventGroup = null;
                                state.isDirty = false;
                            }
                        }
                    };

                    const markDirty = () => {
                        state.isDirty = true;
                    };

                    const undo = () => {
                        if (state.undoStack.length > 0 && state.selectedEventGroup) {
                            state.redoStack.push({ ...state.selectedEventGroup });
                            state.selectedEventGroup = state.undoStack.pop()!;
                            state.isDirty = true;
                        }
                    };

                    const redo = () => {
                        if (state.redoStack.length > 0 && state.selectedEventGroup) {
                            state.undoStack.push({ ...state.selectedEventGroup });
                            state.selectedEventGroup = state.redoStack.pop()!;
                            state.isDirty = true;
                        }
                    };

                    const reloadEventGroups = () => {
                        loadEventGroups();
                        state.selectedEventGroup = null;
                        state.isDirty = false;
                    };

                    const addCondition = () => {
                        if (!state.selectedEventGroup) return;

                        state.selectedEventGroup.conditions.push({
                            op: 0, // And
                            type: 0,
                            compareOp: 0, // Equal
                            targetValue: 0
                        });
                        markDirty();
                    };

                    const removeCondition = (index: number) => {
                        if (!state.selectedEventGroup) return;

                        state.selectedEventGroup.conditions.splice(index, 1);
                        markDirty();
                    };

                    const addAction = () => {
                        if (!state.selectedEventGroup) return;

                        state.selectedEventGroup.actions.push({
                            type: 0,
                            duration: 0,
                            targetValue: 0,
                            easing: 0 // Linear
                        });
                        markDirty();
                    };

                    const removeAction = (index: number) => {
                        if (!state.selectedEventGroup) return;

                        state.selectedEventGroup.actions.splice(index, 1);
                        markDirty();
                    };

                    // Helper methods to get definition objects for type-aware inputs
                    const getConditionDef = (enumValue: number): ValueDecorator | undefined => {
                        return EventConditionDef.value.find(def => def.enum === enumValue);
                    };

                    const getActionDef = (enumValue: number): ValueDecorator | undefined => {
                        return EventActionDef.value.find(def => def.enum === enumValue);
                    };

                    // Lifecycle
                    onMounted(() => {
                        loadEventGroups();
                    });

                    // Expose methods for external access
                    return {
                        state,
                        filteredEventGroups,
                        categories,
                        selectEventGroup,
                        selectEventGroupByName,
                        saveEventGroups,
                        createNewEventGroup,
                        duplicateEventGroup,
                        deleteEventGroup,
                        markDirty,
                        undo,
                        redo,
                        reloadEventGroups,
                        addCondition,
                        removeCondition,
                        addAction,
                        removeAction,
                        // Enums for dropdowns
                        ConditionOpEnum,
                        CompareOpEnum,
                        EasingEnum,
                        // New definition-based arrays
                        EventConditionDef,
                        EventActionDef,
                        // Helper methods for type-aware inputs
                        getConditionDef,
                        getActionDef
                    };
                },
                template: readFileSync(join(__dirname, '../../../static/template/vue/event-editor.html'), 'utf-8'),
            }));

            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
