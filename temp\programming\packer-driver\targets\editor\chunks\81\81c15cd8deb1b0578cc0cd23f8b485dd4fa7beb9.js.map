{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "EDITOR", "Bullet", "EmitterData", "BulletData", "ObjectPool", "BulletSystem", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "playOnFocus", "disallowMultiple", "menu", "inspector", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "Emitter", "displayName", "type", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "totalElapsedTime", "updateInEditor", "_status", "None", "_statusElapsedTime", "_isEmitting", "_nextEmitTime", "_timeAccumulator", "_fixedDel<PERSON>", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "start", "onCreateEmitter", "resetProperties", "update", "dt", "tick", "tickBullets", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "emitterData", "clear", "addProperty", "IsActive", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PreWarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "TotalElapsedTime", "changeStatus", "scheduleNextEmit", "value", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "i", "j", "targetTime", "push", "index", "perEmitIndex", "sort", "a", "b", "emitSingle", "processPerEmitQueue", "length", "nextBullet", "shift", "tryEmit", "console", "log", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "playEffect", "prefab", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACUC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,WAAxC;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA,IAAvE;AAA6EC,QAAAA;AAA7E,O,GAA4Ff,U;OAC5F;AAAEgB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyChB,I;;gCAEnCiB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;yBAcCC,O,WANZX,OAAO,CAAC,SAAD,C,UAEPK,IAAI,CAAC,UAAD,C,UAGJD,gBAAgB,CAAC,IAAD,C,UAKZF,QAAQ,CAAC;AAACU,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAERV,QAAQ,CAAC;AAACW,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,UAERV,QAAQ,CAAC;AAACW,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBD,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,+BAXZX,iB,UACAE,W,+CAJD,MAMaQ,OANb;AAAA;AAAA,oEAMsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWxDG,aAXwD,GAWhC,IAXgC;AAAA,eAYxDC,oBAZwD,GAYzB,IAZyB;AAAA,eAaxDC,iBAbwD,GAa5B,IAb4B;AAelE;AAfkE,eAgB3DC,QAhB2D;AAAA,eAiB3DC,cAjB2D;AAAA,eAkB3DC,SAlB2D;AAAA,eAmB3DC,MAnB2D;AAAA,eAoB3DC,YApB2D;AAAA,eAqB3DC,eArB2D;AAAA,eAsB3DC,YAtB2D;AAAA,eAuB3DC,YAvB2D;AAAA,eAwB3DC,SAxB2D;AAAA,eAyB3DC,YAzB2D;AAAA,eA0B3DC,YA1B2D;AAAA,eA2B3DC,eA3B2D;AAAA,eA4B3DC,cA5B2D;AAAA,eA6B3DC,KA7B2D;AAAA,eA8B3DC,KA9B2D;AAAA,eA+B3DC,GA/B2D;AAAA,eAgC3DC,MAhC2D;AAAA,eAiC3DC,gBAjC2D;AAAA,eAmC3DC,cAnC2D,GAmChC,KAnCgC;AAmCxB;AAnCwB,eAoCxDC,OApCwD,GAoC9B3B,cAAc,CAAC4B,IApCe;AAAA,eAqCxDC,kBArCwD,GAqC3B,CArC2B;AAAA,eAsCxDC,WAtCwD,GAsCjC,KAtCiC;AAAA,eAuCxDC,aAvCwD,GAuChC,CAvCgC;AAAA,eAwCxDC,gBAxCwD,GAwC9B,CAxC8B;AAwC3B;AAxC2B,eAyCxDC,WAzCwD,GAyCnC,MAzCmC;AAyC3B;AAEvC;AA3CkE,eA4CxDC,mBA5CwD,GA4CgC,EA5ChC;AAAA;;AA8CpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKL,WAAZ;AAA0B;;AAC5C,YAANM,MAAM,GAAmB;AAAE,iBAAO,KAAKT,OAAZ;AAAsB;;AAChC,YAAjBU,iBAAiB,GAAW;AAAE,iBAAO,KAAKR,kBAAZ;AAAiC;;AAEzDS,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACA,eAAKC,eAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChC,cAAI1D,MAAM,IAAI,KAAK0C,cAAnB,EAAmC;AAC/B,iBAAKM,gBAAL,IAAyBU,EAAzB;;AAEA,mBAAO,KAAKV,gBAAL,IAAyB,KAAKC,WAArC,EAAkD;AAC9C,mBAAKD,gBAAL,IAAyB,KAAKC,WAA9B;AAEA,mBAAKU,IAAL,CAAU,KAAKV,WAAf;AACA;AAAA;AAAA,gDAAaW,WAAb,CAAyB,KAAKX,WAA9B,EAJ8C,CAK9C;AACH;AACJ;AACJ;;AAEMY,QAAAA,aAAa,GAAG;AACnB,eAAKnB,cAAL,GAAsB,IAAtB;AACH;;AAEMoB,QAAAA,eAAe,GAAG;AACrB,eAAKpB,cAAL,GAAsB,IAAtB;AACA,eAAKc,eAAL;AACH;;AAEMO,QAAAA,mBAAmB,GAAG;AACzB,eAAKrB,cAAL,GAAsB,KAAtB;;AACA,cAAI;AAAA;AAAA,4CAAasB,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ,SAnFiE,CAqFlE;;;AACUV,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKW,WAAV,EAAuB;AAEvB,eAAKC,KAAL;AACA,eAAK5C,QAAL,GAAgB,KAAK6C,WAAL,CAAiBpD,YAAY,CAACqD,QAA9B,EAAwC,IAAxC,CAAhB;AACA,eAAK7C,cAAL,GAAsB,KAAK4C,WAAL,CAAiBpD,YAAY,CAACsD,cAA9B,EAA8C,KAAKJ,WAAL,CAAiB1C,cAA/D,CAAtB;AACA,eAAKC,SAAL,GAAiB,KAAK2C,WAAL,CAAiBpD,YAAY,CAACuD,SAA9B,EAAyC,KAAKL,WAAL,CAAiBzC,SAA1D,CAAjB;AACA,eAAKC,MAAL,GAAc,KAAK0C,WAAL,CAAiBpD,YAAY,CAACwD,MAA9B,EAAsC,KAAKN,WAAL,CAAiBxC,MAAvD,CAAd;AACA,eAAKC,YAAL,GAAoB,KAAKyC,WAAL,CAAiBpD,YAAY,CAACyD,YAA9B,EAA4C,KAAKP,WAAL,CAAiBvC,YAA7D,CAApB;AACA,eAAKC,eAAL,GAAuB,KAAKwC,WAAL,CAAiBpD,YAAY,CAAC0D,eAA9B,EAA+C,KAAKR,WAAL,CAAiBtC,eAAhE,CAAvB;AACA,eAAKC,YAAL,GAAoB,KAAKuC,WAAL,CAAiBpD,YAAY,CAAC2D,YAA9B,EAA4C,KAAKT,WAAL,CAAiBrC,YAA7D,CAApB;AACA,eAAKC,YAAL,GAAoB,KAAKsC,WAAL,CAAiBpD,YAAY,CAAC4D,YAA9B,EAA4C,KAAKV,WAAL,CAAiBpC,YAA7D,CAApB;AACA,eAAKC,SAAL,GAAiB,KAAKqC,WAAL,CAAiBpD,YAAY,CAAC6D,SAA9B,EAAyC,KAAKX,WAAL,CAAiBnC,SAA1D,CAAjB;AACA,eAAKC,YAAL,GAAoB,KAAKoC,WAAL,CAAiBpD,YAAY,CAAC8D,YAA9B,EAA4C,KAAKZ,WAAL,CAAiBlC,YAA7D,CAApB;AACA,eAAKC,YAAL,GAAoB,KAAKmC,WAAL,CAAiBpD,YAAY,CAAC+D,YAA9B,EAA4C,KAAKb,WAAL,CAAiBjC,YAA7D,CAApB;AACA,eAAKC,eAAL,GAAuB,KAAKkC,WAAL,CAAiBpD,YAAY,CAACgE,eAA9B,EAA+C,KAAKd,WAAL,CAAiBhC,eAAhE,CAAvB;AACA,eAAKC,cAAL,GAAsB,KAAKiC,WAAL,CAAiBpD,YAAY,CAACiE,cAA9B,EAA8C,KAAKf,WAAL,CAAiB/B,cAA/D,CAAtB;AACA,eAAKC,KAAL,GAAa,KAAKgC,WAAL,CAAiBpD,YAAY,CAACkE,KAA9B,EAAqC,KAAKhB,WAAL,CAAiB9B,KAAtD,CAAb;AACA,eAAKC,KAAL,GAAa,KAAK+B,WAAL,CAAiBpD,YAAY,CAACmE,KAA9B,EAAqC,KAAKjB,WAAL,CAAiB7B,KAAtD,CAAb;AACA,eAAKC,GAAL,GAAW,KAAK8B,WAAL,CAAiBpD,YAAY,CAACoE,GAA9B,EAAmC,KAAKlB,WAAL,CAAiB5B,GAApD,CAAX;AACA,eAAKC,MAAL,GAAc,KAAK6B,WAAL,CAAiBpD,YAAY,CAACqE,MAA9B,EAAsC,KAAKnB,WAAL,CAAiB3B,MAAvD,CAAd;AACA,eAAKC,gBAAL,GAAwB,KAAK4B,WAAL,CAAiBpD,YAAY,CAACsE,gBAA9B,EAAgD,CAAhD,CAAxB;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,YAAY,CAACpC,MAAD,EAAyB;AACjC,eAAKT,OAAL,GAAeS,MAAf;AACA,eAAKP,kBAAL,GAA0B,CAA1B,CAFiC,CAGjC;;AACA,eAAKK,mBAAL,GAA2B,EAA3B;AACH;;AAESuC,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAK1C,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,KAAKd,YAAL,CAAkB2D,KAAjE;AACH;;AAESC,QAAAA,aAAa,GAAG;AACtB,eAAK7C,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAES8C,QAAAA,YAAY,GAAG;AACrB,eAAK9C,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAK+C,sBAAL,GAHqB,CAIrB;;AACA,eAAK3C,mBAAL,GAA2B,EAA3B;AACH;;AAES4C,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,cAAI,KAAK5D,eAAL,CAAqBuD,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1D,KAAL,CAAWoD,KAA/B,EAAsCM,CAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/D,YAAL,CAAkBwD,KAAtC,EAA6CO,CAAC,EAA9C,EAAkD;AAC9C,sBAAMC,UAAU,GAAG,KAAKrD,kBAAL,GAA2B,KAAKV,eAAL,CAAqBuD,KAArB,GAA6BO,CAA3E;;AACA,qBAAK/C,mBAAL,CAAyBiD,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEJ,CADmB;AAE1BK,kBAAAA,YAAY,EAAEJ,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ,aAX+B,CAahC;;;AACA,iBAAKhD,mBAAL,CAAyBoD,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACL,UAAF,GAAeM,CAAC,CAACN,UAAzD;AACH,WAfD,MAgBK;AACD;AACA,iBAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1D,KAAL,CAAWoD,KAA/B,EAAsCM,CAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/D,YAAL,CAAkBwD,KAAtC,EAA6CO,CAAC,EAA9C,EAAkD;AAC9C,qBAAKQ,UAAL,CAAgBT,CAAhB,EAAmBC,CAAnB;AACH;AACJ;AACJ;AACJ;;AAESS,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKxD,mBAAL,CAAyByD,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,kBAAMC,UAAU,GAAG,KAAK1D,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKL,kBAAL,IAA2B+D,UAAU,CAACV,UAA1C,EAAsD;AAClD;AACA,mBAAKhD,mBAAL,CAAyB2D,KAAzB;;AACA,mBAAKJ,UAAL,CAAgBG,UAAU,CAACR,KAA3B,EAAkCQ,UAAU,CAACP,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESS,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKhB,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESU,QAAAA,UAAU,CAACL,KAAD,EAAeC,YAAf,EAAqC;AACrDU,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA,gBAAMC,SAAS,GAAG,KAAKC,iBAAL,CAAuBd,KAAvB,CAAlB;AACA,gBAAMe,QAAQ,GAAG,KAAKC,gBAAL,CAAsBhB,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKgB,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACd,KAAD,EAA0C;AACvD;AACA,gBAAMkB,WAAW,GAAG,KAAKhF,KAAL,CAAWoD,KAAX,GAAmB,CAAnB,GAAwB,KAAKnD,GAAL,CAASmD,KAAT,IAAkB,KAAKpD,KAAL,CAAWoD,KAAX,GAAmB,CAArC,CAAD,GAA4CU,KAA5C,GAAoD,KAAK7D,GAAL,CAASmD,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,gBAAM6B,MAAM,GAAGzG,gBAAgB,CAAC,KAAKuB,KAAL,CAAWqD,KAAX,GAAmB4B,WAApB,CAA/B;AACA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAAChB,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,gBAAMjE,cAAc,GAAI,KAAKF,YAAL,CAAkBwD,KAAlB,GAA0B,CAA1B,GAA+B,KAAKtD,cAAL,CAAoBsD,KAApB,IAA6B,KAAKxD,YAAL,CAAkBwD,KAAlB,GAA0B,CAAvD,CAAD,GAA8DW,YAA9D,GAA6E,KAAKjE,cAAL,CAAoBsD,KAApB,GAA4B,CAAvI,GAA2I,CAAnK;;AACA,cAAI,KAAKlD,MAAL,CAAYkD,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAE8B,cAAAA,CAAC,EAAEpF,cAAL;AAAqBuF,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBd,KAAvB,CAAlB;AACA,iBAAO;AACHoB,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKhF,MAAL,CAAYkD,KAA1B,GAAkCtD,cADlC;AAEHuF,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKnF,MAAL,CAAYkD;AAF1B,WAAP;AAIH;;AAED2B,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAK9F,aAAV,EAAyB;AACrB0F,YAAAA,OAAO,CAACc,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,gBAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAa/D,YAAhC,EAA8C,KAAK3C,aAAnD,CAAnB;;AACA,cAAI,CAACyG,UAAL,EAAiB;AACbf,YAAAA,OAAO,CAACiB,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,gBAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACTlB,YAAAA,OAAO,CAACiB,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAInI,MAAJ,EAAY;AACR8H,YAAAA,UAAU,CAACM,IAAX,GAAkBlH,OAAO,CAACmH,mBAA1B;AACH,WAtBiF,CAwBlF;;;AACA,gBAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAV,UAAAA,UAAU,CAACW,gBAAX,CACIH,UAAU,CAACd,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIc,UAAU,CAACX,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIW,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4BV,MAA5B,EAhCkF,CAkClF;;AACAA,UAAAA,MAAM,CAACW,KAAP,CAAaC,UAAb,GAA0B9H,gBAAgB,CAAC0G,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAS,UAAAA,MAAM,CAACW,KAAP,CAAaG,KAAb,IAAsB,KAAK/G,SAAL,CAAe0D,KAArC,CApCkF,CAqClF;;AAEA,iBAAOoC,UAAP;AACH;;AAEDkB,QAAAA,UAAU,CAACC,MAAD,EAAiB9B,QAAjB,EAAiC+B,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACF,MAAL,EAAa;AAEb,gBAAMG,UAAU,GAAG;AAAA;AAAA,wCAAWrB,OAAX,CAAmB,KAAKQ,IAAxB,EAA8BU,MAA9B,CAAnB;AACA,cAAI,CAACG,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAACX,gBAAX,CAA4BtB,QAA5B;AACAiC,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEM7F,QAAAA,IAAI,CAAC8F,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKjI,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAckE,KAArC,EAA4C;AACxC;AACH;;AAED,eAAK7C,kBAAL,IAA2B4G,SAA3B;AACA,eAAKhH,gBAAL,CAAsBiD,KAAtB,IAA+B+D,SAA/B;;AAEA,kBAAQ,KAAK9G,OAAb;AAEI,iBAAK3B,cAAc,CAAC4B,IAApB;AACI,mBAAK8G,gBAAL;AACA;;AACJ,iBAAK1I,cAAc,CAAC2I,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAK5I,cAAc,CAAC6I,QAApB;AACI,mBAAKC,oBAAL;AACA;;AACJ,iBAAK9I,cAAc,CAAC+I,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKhJ,cAAc,CAACiJ,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK7G,kBAAL,IAA2B,KAAKjB,YAAL,CAAkB8D,KAAjD,EAAwD;AACpD,iBAAKF,YAAL,CAAkBxE,cAAc,CAAC2I,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKlI,SAAL,CAAegE,KAApB,EACI,KAAKF,YAAL,CAAkBxE,cAAc,CAAC6I,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKhH,kBAAL,IAA2B,KAAKhB,eAAL,CAAqB6D,KAApD,EAA2D;AACvD,mBAAKF,YAAL,CAAkBxE,cAAc,CAAC6I,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKjH,kBAAL,GAA0B,KAAKf,YAAL,CAAkB4D,KAAhD,EAAuD;AACnD,iBAAKE,YAAL;AACA,gBAAI,KAAKjE,MAAT,EACI,KAAK6D,YAAL,CAAkBxE,cAAc,CAAC+I,cAAjC,EADJ,KAGI,KAAKvE,YAAL,CAAkBxE,cAAc,CAACiJ,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKnH,WAAV,EAAuB;AACnB,iBAAK6C,aAAL;AACH,WAFD,MAGK,IAAI,KAAK7C,WAAL,IAAoB,KAAKD,kBAAL,IAA2B,KAAKE,aAAxD,EAAuE;AACxE;AACA,iBAAK+D,OAAL;AACA,iBAAKrB,gBAAL;AACH,WAlB4B,CAoB7B;;;AACA,eAAKiB,mBAAL;AACH;;AAESsD,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKnH,kBAAL,IAA2B,KAAKZ,YAAL,CAAkByD,KAAjD,EAAwD;AACpD,iBAAKF,YAAL,CAAkBxE,cAAc,CAAC6I,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AA5XiE,O,UAE3D7B,mB,GAA6B,U;;;;;iBAGV,C;;;;;;;iBAEU,I;;;;;;;iBAEF,I", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component, Vec3, Quat } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\n\r\nconst { ccclass, executeInEditMode, property, playOnFocus, disallowMultiple, menu, inspector  } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, \r\n    InitialDelay, PreWarmDuration, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX, \r\n    Angle, Count, Arc, Radius,\r\n    TotalElapsedTime, \r\n}\r\n\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode\r\n@playOnFocus\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({displayName: \"子弹ID\"})\r\n    public bulletID: number = 0;\r\n    @property({type: EmitterData, displayName: \"发射器属性\"})\r\n    readonly emitterData: EmitterData = null;\r\n    @property({type: BulletData, displayName: \"子弹属性\"})\r\n    readonly bulletData: BulletData = null;\r\n    \r\n    protected _bulletPrefab: Prefab = null;\r\n    protected _prewarmEffectPrefab: Prefab = null;\r\n    protected _emitEffectPrefab: Prefab = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive: Property<boolean>;\r\n    public isOnlyInScreen: Property<boolean>;\r\n    public isPreWarm: Property<boolean>;\r\n    public isLoop: Property<boolean>;\r\n    public initialDelay: Property<number>;\r\n    public preWarmDuration: Property<number>;\r\n    public emitDuration: Property<number>;\r\n    public emitInterval: Property<number>;\r\n    public emitPower: Property<number>;\r\n    public loopInterval: Property<number>;\r\n    public perEmitCount: Property<number>;\r\n    public perEmitInterval: Property<number>;\r\n    public perEmitOffsetX: Property<number>;\r\n    public angle: Property<number>;\r\n    public count: Property<number>;\r\n    public arc: Property<number>;\r\n    public radius: Property<number>;\r\n    public totalElapsedTime: Property<number>;\r\n\r\n    public updateInEditor : boolean = false;  // 是否在编辑器中更新\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _timeAccumulator:number = 0; // Accumulate delta time here\r\n    protected _fixedDelta:number = 0.0167; // Fixed time step (e.g., 60 FPS)\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n        this.resetProperties();\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        if (EDITOR && this.updateInEditor) {\r\n            this._timeAccumulator += dt;\r\n\r\n            while (this._timeAccumulator >= this._fixedDelta) {\r\n                this._timeAccumulator -= this._fixedDelta; \r\n                \r\n                this.tick(this._fixedDelta);\r\n                BulletSystem.tickBullets(this._fixedDelta);\r\n                //BulletSystem.tickActionRunners(this._fixedDelta);\r\n            }\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.clear();\r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, true);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, this.emitterData.isOnlyInScreen);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, this.emitterData.isPreWarm);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, this.emitterData.isLoop);\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, this.emitterData.initialDelay);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, this.emitterData.preWarmDuration);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, this.emitterData.emitDuration);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, this.emitterData.emitInterval);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, this.emitterData.emitPower);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, this.emitterData.loopInterval);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, this.emitterData.perEmitCount);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, this.emitterData.perEmitInterval);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, this.emitterData.perEmitOffsetX);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, this.emitterData.angle);\r\n        this.count = this.addProperty(eEmitterProp.Count, this.emitterData.count);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, this.emitterData.arc);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, this.emitterData.radius);\r\n        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);\r\n    }\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.perEmitInterval.value > 0) {\r\n            // Queue all bullets with their target emission times\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n\r\n            // Sort by target time to ensure proper order\r\n            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        console.log(\"emit a bullet\");\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.perEmitCount.value > 1 ? (this.perEmitOffsetX.value / (this.perEmitCount.value - 1)) * perEmitIndex - this.perEmitOffsetX.value / 2 : 0);\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius.value + perEmitOffsetX,\r\n            y: direction.y * this.radius.value\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this._bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitPower.value;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime.value += deltaTime;\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}