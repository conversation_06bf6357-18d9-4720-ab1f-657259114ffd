System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, EDITOR, Bullet, EmitterData, BulletData, ObjectPool, BulletSystem, PropertyContainerComponent, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _class3, _crd, ccclass, executeInEditMode, property, playOnFocus, disallowMultiple, menu, inspector, degreesToRadians, radiansToDegrees, eEmitterStatus, eEmitterProp, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterData(extras) {
    _reporterNs.report("EmitterData", "../data/bullet/EmitterData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../data/bullet/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropertyContainerComponent(extras) {
    _reporterNs.report("PropertyContainerComponent", "./PropertyContainer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Bullet = _unresolved_2.Bullet;
    }, function (_unresolved_3) {
      EmitterData = _unresolved_3.EmitterData;
    }, function (_unresolved_4) {
      BulletData = _unresolved_4.BulletData;
    }, function (_unresolved_5) {
      ObjectPool = _unresolved_5.ObjectPool;
    }, function (_unresolved_6) {
      BulletSystem = _unresolved_6.BulletSystem;
    }, function (_unresolved_7) {
      PropertyContainerComponent = _unresolved_7.PropertyContainerComponent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'misc', 'instantiate', 'Node', 'Prefab', 'Component', 'Vec3', 'Quat']);

      ({
        ccclass,
        executeInEditMode,
        property,
        playOnFocus,
        disallowMultiple,
        menu,
        inspector
      } = _decorator);
      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);

      _export("eEmitterStatus", eEmitterStatus = /*#__PURE__*/function (eEmitterStatus) {
        eEmitterStatus[eEmitterStatus["None"] = 0] = "None";
        eEmitterStatus[eEmitterStatus["Prewarm"] = 1] = "Prewarm";
        eEmitterStatus[eEmitterStatus["Emitting"] = 2] = "Emitting";
        eEmitterStatus[eEmitterStatus["LoopEndReached"] = 3] = "LoopEndReached";
        eEmitterStatus[eEmitterStatus["Completed"] = 4] = "Completed";
        return eEmitterStatus;
      }({})); // 用枚举定义属性


      _export("eEmitterProp", eEmitterProp = /*#__PURE__*/function (eEmitterProp) {
        eEmitterProp[eEmitterProp["IsActive"] = 1] = "IsActive";
        eEmitterProp[eEmitterProp["IsOnlyInScreen"] = 2] = "IsOnlyInScreen";
        eEmitterProp[eEmitterProp["IsPreWarm"] = 3] = "IsPreWarm";
        eEmitterProp[eEmitterProp["IsLoop"] = 4] = "IsLoop";
        eEmitterProp[eEmitterProp["InitialDelay"] = 5] = "InitialDelay";
        eEmitterProp[eEmitterProp["PreWarmDuration"] = 6] = "PreWarmDuration";
        eEmitterProp[eEmitterProp["EmitDuration"] = 7] = "EmitDuration";
        eEmitterProp[eEmitterProp["EmitInterval"] = 8] = "EmitInterval";
        eEmitterProp[eEmitterProp["EmitPower"] = 9] = "EmitPower";
        eEmitterProp[eEmitterProp["LoopInterval"] = 10] = "LoopInterval";
        eEmitterProp[eEmitterProp["PerEmitCount"] = 11] = "PerEmitCount";
        eEmitterProp[eEmitterProp["PerEmitInterval"] = 12] = "PerEmitInterval";
        eEmitterProp[eEmitterProp["PerEmitOffsetX"] = 13] = "PerEmitOffsetX";
        eEmitterProp[eEmitterProp["Angle"] = 14] = "Angle";
        eEmitterProp[eEmitterProp["Count"] = 15] = "Count";
        eEmitterProp[eEmitterProp["Arc"] = 16] = "Arc";
        eEmitterProp[eEmitterProp["Radius"] = 17] = "Radius";
        eEmitterProp[eEmitterProp["TotalElapsedTime"] = 18] = "TotalElapsedTime";
        return eEmitterProp;
      }({}));

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = menu('子弹系统/发射器'), _dec3 = disallowMultiple(true), _dec4 = property({
        displayName: "子弹ID"
      }), _dec5 = property({
        type: _crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
          error: Error()
        }), EmitterData) : EmitterData,
        displayName: "发射器属性"
      }), _dec6 = property({
        type: _crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
          error: Error()
        }), BulletData) : BulletData,
        displayName: "子弹属性"
      }), _dec(_class = _dec2(_class = executeInEditMode(_class = playOnFocus(_class = _dec3(_class = (_class2 = (_class3 = class Emitter extends (_crd && PropertyContainerComponent === void 0 ? (_reportPossibleCrUseOfPropertyContainerComponent({
        error: Error()
      }), PropertyContainerComponent) : PropertyContainerComponent) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletID", _descriptor, this);

          _initializerDefineProperty(this, "emitterData", _descriptor2, this);

          _initializerDefineProperty(this, "bulletData", _descriptor3, this);

          this._bulletPrefab = null;
          this._prewarmEffectPrefab = null;
          this._emitEffectPrefab = null;
          // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
          this.isActive = void 0;
          this.isOnlyInScreen = void 0;
          this.isPreWarm = void 0;
          this.isLoop = void 0;
          this.initialDelay = void 0;
          this.preWarmDuration = void 0;
          this.emitDuration = void 0;
          this.emitInterval = void 0;
          this.emitPower = void 0;
          this.loopInterval = void 0;
          this.perEmitCount = void 0;
          this.perEmitInterval = void 0;
          this.perEmitOffsetX = void 0;
          this.angle = void 0;
          this.count = void 0;
          this.arc = void 0;
          this.radius = void 0;
          this.totalElapsedTime = void 0;
          this.updateInEditor = false;
          // 是否在编辑器中更新
          this._status = eEmitterStatus.None;
          this._statusElapsedTime = 0;
          this._isEmitting = false;
          this._nextEmitTime = 0;
          this._timeAccumulator = 0;
          // Accumulate delta time here
          this._fixedDelta = 0.0167;
          // Fixed time step (e.g., 60 FPS)
          // Per-emit timing tracking
          this._perEmitBulletQueue = [];
        }

        get isEmitting() {
          return this._isEmitting;
        }

        get status() {
          return this._status;
        }

        get statusElapsedTime() {
          return this._statusElapsedTime;
        }

        start() {
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateEmitter(this);
          this.resetProperties();
        }

        update(dt) {
          if (EDITOR && this.updateInEditor) {
            this._timeAccumulator += dt;

            while (this._timeAccumulator >= this._fixedDelta) {
              this._timeAccumulator -= this._fixedDelta;
              this.tick(this._fixedDelta);
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).tickBullets(this._fixedDelta); //BulletSystem.tickActionRunners(this._fixedDelta);
            }
          }
        }

        resetInEditor() {
          this.updateInEditor = true;
        }

        onFocusInEditor() {
          this.updateInEditor = true;
          this.resetProperties();
        }

        onLostFocusInEditor() {
          this.updateInEditor = false;

          if ((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent && (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent.isValid) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).destroyAllBullets();
          }
        } // reset properties from emitterData


        resetProperties() {
          if (!this.emitterData) return;
          this.clear();
          this.isActive = this.addProperty(eEmitterProp.IsActive, true);
          this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, this.emitterData.isOnlyInScreen);
          this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, this.emitterData.isPreWarm);
          this.isLoop = this.addProperty(eEmitterProp.IsLoop, this.emitterData.isLoop);
          this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, this.emitterData.initialDelay);
          this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, this.emitterData.preWarmDuration);
          this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, this.emitterData.emitDuration);
          this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, this.emitterData.emitInterval);
          this.emitPower = this.addProperty(eEmitterProp.EmitPower, this.emitterData.emitPower);
          this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, this.emitterData.loopInterval);
          this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, this.emitterData.perEmitCount);
          this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, this.emitterData.perEmitInterval);
          this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, this.emitterData.perEmitOffsetX);
          this.angle = this.addProperty(eEmitterProp.Angle, this.emitterData.angle);
          this.count = this.addProperty(eEmitterProp.Count, this.emitterData.count);
          this.arc = this.addProperty(eEmitterProp.Arc, this.emitterData.arc);
          this.radius = this.addProperty(eEmitterProp.Radius, this.emitterData.radius);
          this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);
        }
        /**
         * public apis
         */


        changeStatus(status) {
          this._status = status;
          this._statusElapsedTime = 0; // Clear per-emit queue when changing status

          this._perEmitBulletQueue = [];
        }

        scheduleNextEmit() {
          // Schedule the next emit after emitInterval
          this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;
        }

        startEmitting() {
          this._isEmitting = true; // 下一次update时触发发射
          // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
        }

        stopEmitting() {
          this._isEmitting = false; // Clear any scheduled per-emit bullets

          this.unscheduleAllCallbacks(); // Clear the per-emit bullet queue

          this._perEmitBulletQueue = [];
        }

        canEmit() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }

        emit() {
          if (this.perEmitInterval.value > 0) {
            // Queue all bullets with their target emission times
            for (let i = 0; i < this.count.value; i++) {
              for (let j = 0; j < this.perEmitCount.value; j++) {
                const targetTime = this._statusElapsedTime + this.perEmitInterval.value * j;

                this._perEmitBulletQueue.push({
                  index: i,
                  perEmitIndex: j,
                  targetTime: targetTime
                });
              }
            } // Sort by target time to ensure proper order


            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);
          } else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count.value; i++) {
              for (let j = 0; j < this.perEmitCount.value; j++) {
                this.emitSingle(i, j);
              }
            }
          }
        }

        processPerEmitQueue() {
          // Process bullets that should be emitted based on current time
          while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0]; // Check if it's time to emit this bullet

            if (this._statusElapsedTime >= nextBullet.targetTime) {
              // Remove from queue and emit
              this._perEmitBulletQueue.shift();

              this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
              // No more bullets ready to emit yet
              break;
            }
          }
        }

        tryEmit() {
          if (this.canEmit()) {
            this.emit();
            return true;
          }

          return false;
        }

        emitSingle(index, perEmitIndex) {
          console.log("emit a bullet");
          const direction = this.getSpawnDirection(index);
          const position = this.getSpawnPosition(index, perEmitIndex);
          this.createBullet(direction, position);
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 计算发射方向
          const angleOffset = this.count.value > 1 ? this.arc.value / (this.count.value - 1) * index - this.arc.value / 2 : 0;
          const radian = degreesToRadians(this.angle.value + angleOffset);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index, perEmitIndex) {
          // add perEmitOffsetX by perEmitIndex
          const perEmitOffsetX = this.perEmitCount.value > 1 ? this.perEmitOffsetX.value / (this.perEmitCount.value - 1) * perEmitIndex - this.perEmitOffsetX.value / 2 : 0;

          if (this.radius.value <= 0) {
            return {
              x: perEmitOffsetX,
              y: 0
            };
          }

          const direction = this.getSpawnDirection(index);
          return {
            x: direction.x * this.radius.value + perEmitOffsetX,
            y: direction.y * this.radius.value
          };
        }

        createBullet(direction, position) {
          if (!this._bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
          }

          const bulletNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent, this._bulletPrefab);

          if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component


          const bullet = bulletNode.getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);

          if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
          }

          if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
          } // Set bullet position relative to emitter


          const emitterPos = this.node.getWorldPosition();
          bulletNode.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateBullet(bullet); // Post set bullet properties

          bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));
          bullet.mover.speed *= this.emitPower.value; // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));

          return bulletNode;
        }

        playEffect(prefab, position, rotation, duration) {
          if (!prefab) return;
          const effectNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode(this.node, prefab);
          if (!effectNode) return;
          effectNode.setWorldPosition(position);
          effectNode.setWorldRotation(rotation); // Play the effect and destroy it after duration
          // effectNode.getComponent(ParticleSystem)?.play();

          this.scheduleOnce(() => {
            (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
              error: Error()
            }), ObjectPool) : ObjectPool).returnNode(effectNode);
          }, duration);
        }
        /**
         * Return true if this.node is in screen
         */


        isInScreen() {
          // TODO: Get mainCamera.containsNode(this.node)
          return true;
        }

        tick(deltaTime) {
          if (!this.isActive || !this.isActive.value) {
            return;
          }

          this._statusElapsedTime += deltaTime;
          this.totalElapsedTime.value += deltaTime;

          switch (this._status) {
            case eEmitterStatus.None:
              this.updateStatusNone();
              break;

            case eEmitterStatus.Prewarm:
              this.updateStatusPrewarm();
              break;

            case eEmitterStatus.Emitting:
              this.updateStatusEmitting();
              break;

            case eEmitterStatus.LoopEndReached:
              this.updateStatusLoopEndReached();
              break;

            case eEmitterStatus.Completed:
              this.updateStatusCompleted();
              break;

            default:
              break;
          }
        }

        updateStatusNone() {
          if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusPrewarm() {
          if (!this.isPreWarm.value) this.changeStatus(eEmitterStatus.Emitting);else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
              this.changeStatus(eEmitterStatus.Emitting);
            }
          }
        }

        updateStatusEmitting() {
          if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop) this.changeStatus(eEmitterStatus.LoopEndReached);else this.changeStatus(eEmitterStatus.Completed);
            return;
          } // Start emitting if not already started


          if (!this._isEmitting) {
            this.startEmitting();
          } else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {
            // Check if it's time for the next emit
            this.tryEmit();
            this.scheduleNextEmit();
          } // Process per-emit bullet queue based on precise timing


          this.processPerEmitQueue();
        }

        updateStatusLoopEndReached() {
          if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Emitting);
          }
        }

        updateStatusCompleted() {// Do nothing or cleanup if needed
        }

      }, _class3.kBulletNameInEditor = "_bullet_", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletID", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "emitterData", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "bulletData", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=81c15cd8deb1b0578cc0cd23f8b485dd4fa7beb9.js.map