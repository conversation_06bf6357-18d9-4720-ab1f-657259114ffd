2025-9-1 11:20:25-debug: start **** info
2025-9-1 11:20:25-log: Cannot access game frame or container.
2025-9-1 11:20:25-debug: asset-db:require-engine-code (402ms)
2025-9-1 11:20:25-log: [box2d]:box2d wasm lib loaded.
2025-9-1 11:20:25-log: meshopt wasm decoder initialized
2025-9-1 11:20:25-log: [bullet]:bullet wasm lib loaded.
2025-9-1 11:20:25-log: Cocos Creator v3.8.6
2025-9-1 11:20:25-log: Using legacy pipeline
2025-9-1 11:20:25-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.92MB, end 84.33MB, increase: 3.41MB
2025-9-1 11:20:25-log: Forward render pipeline initialized.
2025-9-1 11:20:25-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.87MB, end 80.00MB, increase: 49.14MB
2025-9-1 11:20:26-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.36MB, end 290.55MB, increase: 206.19MB
2025-9-1 11:20:26-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.76MB, end 288.91MB, increase: 208.15MB
2025-9-1 11:20:26-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.03MB, end 288.94MB, increase: 208.92MB
2025-9-1 11:20:26-debug: run package(google-play) handler(enable) start
2025-9-1 11:20:26-debug: run package(google-play) handler(enable) success!
2025-9-1 11:20:26-debug: run package(harmonyos-next) handler(enable) start
2025-9-1 11:20:26-debug: run package(honor-mini-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(huawei-agc) handler(enable) start
2025-9-1 11:20:26-debug: run package(honor-mini-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(huawei-agc) handler(enable) success!
2025-9-1 11:20:26-debug: run package(huawei-quick-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(harmonyos-next) handler(enable) success!
2025-9-1 11:20:26-debug: run package(ios) handler(enable) start
2025-9-1 11:20:26-debug: run package(linux) handler(enable) start
2025-9-1 11:20:26-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(mac) handler(enable) start
2025-9-1 11:20:26-debug: run package(linux) handler(enable) success!
2025-9-1 11:20:26-debug: run package(ios) handler(enable) success!
2025-9-1 11:20:26-debug: run package(mac) handler(enable) success!
2025-9-1 11:20:26-debug: run package(migu-mini-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(migu-mini-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(native) handler(enable) start
2025-9-1 11:20:26-debug: run package(native) handler(enable) success!
2025-9-1 11:20:26-debug: run package(ohos) handler(enable) start
2025-9-1 11:20:26-debug: run package(ohos) handler(enable) success!
2025-9-1 11:20:26-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(oppo-mini-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(taobao-mini-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-1 11:20:26-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-1 11:20:26-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(vivo-mini-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(web-desktop) handler(enable) start
2025-9-1 11:20:26-debug: run package(web-desktop) handler(enable) success!
2025-9-1 11:20:26-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(web-mobile) handler(enable) success!
2025-9-1 11:20:26-debug: run package(wechatgame) handler(enable) start
2025-9-1 11:20:26-debug: run package(wechatprogram) handler(enable) start
2025-9-1 11:20:26-debug: run package(web-mobile) handler(enable) start
2025-9-1 11:20:26-debug: run package(wechatgame) handler(enable) success!
2025-9-1 11:20:26-debug: run package(windows) handler(enable) success!
2025-9-1 11:20:26-debug: run package(windows) handler(enable) start
2025-9-1 11:20:26-debug: run package(wechatprogram) handler(enable) success!
2025-9-1 11:20:26-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-1 11:20:26-debug: run package(cocos-service) handler(enable) success!
2025-9-1 11:20:26-debug: run package(cocos-service) handler(enable) start
2025-9-1 11:20:26-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-1 11:20:26-debug: run package(im-plugin) handler(enable) start
2025-9-1 11:20:26-debug: run package(im-plugin) handler(enable) success!
2025-9-1 11:20:26-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-1 11:20:26-debug: run package(bullet_editor) handler(enable) success!
2025-9-1 11:20:26-debug: run package(bullet_editor) handler(enable) start
2025-9-1 11:20:26-debug: run package(event_editor_panel) handler(enable) success!
2025-9-1 11:20:26-debug: run package(level-editor) handler(enable) start
2025-9-1 11:20:26-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-1 11:20:26-debug: run package(level-editor) handler(enable) success!
2025-9-1 11:20:26-debug: run package(event_editor_panel) handler(enable) start
2025-9-1 11:20:26-debug: asset-db:worker-init: initPlugin (995ms)
2025-9-1 11:20:26-debug: [Assets Memory track]: asset-db:worker-init start:30.86MB, end 291.26MB, increase: 260.40MB
2025-9-1 11:20:26-debug: Run asset db hook programming:beforePreStart ...
2025-9-1 11:20:26-debug: Run asset db hook programming:beforePreStart success!
2025-9-1 11:20:26-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-1 11:20:26-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-1 11:20:26-debug: run package(localization-editor) handler(enable) start
2025-9-1 11:20:26-debug: Preimport db internal success
2025-9-1 11:20:26-debug: run package(localization-editor) handler(enable) success!
2025-9-1 11:20:26-debug: asset-db:worker-init (1563ms)
2025-9-1 11:20:26-debug: asset-db-hook-programming-beforePreStart (91ms)
2025-9-1 11:20:26-debug: asset-db-hook-engine-extends-beforePreStart (91ms)
2025-9-1 11:20:26-debug: run package(placeholder) handler(enable) start
2025-9-1 11:20:26-debug: run package(placeholder) handler(enable) success!
2025-9-1 11:20:26-debug: Preimport db assets success
2025-9-1 11:20:26-debug: Run asset db hook programming:afterPreStart ...
2025-9-1 11:20:26-debug: starting packer-driver...
2025-9-1 11:20:31-debug: initialize scripting environment...
2025-9-1 11:20:31-debug: [[Executor]] prepare before lock
2025-9-1 11:20:31-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-1 11:20:31-debug: [[Executor]] prepare after unlock
2025-9-1 11:20:31-debug: Run asset db hook programming:afterPreStart success!
2025-9-1 11:20:31-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-1 11:20:31-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-1 11:20:31-debug: Start up the 'internal' database...
2025-9-1 11:20:31-debug: asset-db-hook-programming-afterPreStart (4853ms)
2025-9-1 11:20:31-debug: asset-db:worker-effect-data-processing (198ms)
2025-9-1 11:20:31-debug: asset-db-hook-engine-extends-afterPreStart (198ms)
2025-9-1 11:20:31-debug: Start up the 'assets' database...
2025-9-1 11:20:31-debug: asset-db:worker-startup-database[internal] (5070ms)
2025-9-1 11:20:31-debug: [Assets Memory track]: asset-db:worker-init: startup start:177.55MB, end 193.16MB, increase: 15.60MB
2025-9-1 11:20:31-debug: lazy register asset handler directory
2025-9-1 11:20:31-debug: lazy register asset handler json
2025-9-1 11:20:31-debug: lazy register asset handler text
2025-9-1 11:20:31-debug: lazy register asset handler spine-data
2025-9-1 11:20:31-debug: lazy register asset handler dragonbones
2025-9-1 11:20:31-debug: lazy register asset handler dragonbones-atlas
2025-9-1 11:20:31-debug: lazy register asset handler terrain
2025-9-1 11:20:31-debug: lazy register asset handler *
2025-9-1 11:20:31-debug: lazy register asset handler typescript
2025-9-1 11:20:31-debug: lazy register asset handler javascript
2025-9-1 11:20:31-debug: lazy register asset handler sprite-frame
2025-9-1 11:20:31-debug: lazy register asset handler prefab
2025-9-1 11:20:31-debug: lazy register asset handler tiled-map
2025-9-1 11:20:31-debug: lazy register asset handler scene
2025-9-1 11:20:31-debug: lazy register asset handler buffer
2025-9-1 11:20:31-debug: lazy register asset handler image
2025-9-1 11:20:31-debug: lazy register asset handler sign-image
2025-9-1 11:20:31-debug: lazy register asset handler alpha-image
2025-9-1 11:20:31-debug: lazy register asset handler texture-cube
2025-9-1 11:20:31-debug: lazy register asset handler texture
2025-9-1 11:20:31-debug: lazy register asset handler erp-texture-cube
2025-9-1 11:20:31-debug: lazy register asset handler texture-cube-face
2025-9-1 11:20:31-debug: lazy register asset handler render-texture
2025-9-1 11:20:31-debug: lazy register asset handler rt-sprite-frame
2025-9-1 11:20:31-debug: lazy register asset handler gltf-animation
2025-9-1 11:20:31-debug: lazy register asset handler gltf-mesh
2025-9-1 11:20:31-debug: lazy register asset handler gltf-skeleton
2025-9-1 11:20:31-debug: lazy register asset handler gltf
2025-9-1 11:20:31-debug: lazy register asset handler gltf-material
2025-9-1 11:20:31-debug: lazy register asset handler material
2025-9-1 11:20:31-debug: lazy register asset handler gltf-scene
2025-9-1 11:20:31-debug: lazy register asset handler gltf-embeded-image
2025-9-1 11:20:31-debug: lazy register asset handler effect
2025-9-1 11:20:31-debug: lazy register asset handler fbx
2025-9-1 11:20:31-debug: lazy register asset handler audio-clip
2025-9-1 11:20:31-debug: lazy register asset handler physics-material
2025-9-1 11:20:31-debug: lazy register asset handler animation-graph
2025-9-1 11:20:31-debug: lazy register asset handler effect-header
2025-9-1 11:20:31-debug: lazy register asset handler animation-clip
2025-9-1 11:20:31-debug: lazy register asset handler animation-graph-variant
2025-9-1 11:20:31-debug: lazy register asset handler ttf-font
2025-9-1 11:20:31-debug: lazy register asset handler sprite-atlas
2025-9-1 11:20:31-debug: lazy register asset handler bitmap-font
2025-9-1 11:20:31-debug: lazy register asset handler auto-atlas
2025-9-1 11:20:31-debug: lazy register asset handler animation-mask
2025-9-1 11:20:31-debug: lazy register asset handler particle
2025-9-1 11:20:31-debug: lazy register asset handler render-flow
2025-9-1 11:20:31-debug: lazy register asset handler render-stage
2025-9-1 11:20:31-debug: lazy register asset handler instantiation-material
2025-9-1 11:20:31-debug: lazy register asset handler render-pipeline
2025-9-1 11:20:31-debug: lazy register asset handler instantiation-mesh
2025-9-1 11:20:31-debug: lazy register asset handler label-atlas
2025-9-1 11:20:31-debug: lazy register asset handler instantiation-skeleton
2025-9-1 11:20:31-debug: lazy register asset handler video-clip
2025-9-1 11:20:31-debug: lazy register asset handler instantiation-animation
2025-9-1 11:20:31-debug: asset-db:worker-startup-database[assets] (5060ms)
2025-9-1 11:20:31-debug: asset-db:ready (8065ms)
2025-9-1 11:20:31-debug: fix the bug of updateDefaultUserData
2025-9-1 11:20:31-debug: asset-db:start-database (5144ms)
2025-9-1 11:20:31-debug: init worker message success
2025-9-1 11:20:31-debug: programming:execute-script (2ms)
2025-9-1 11:20:32-debug: [Build Memory track]: builder:worker-init start:197.81MB, end 210.22MB, increase: 12.41MB
2025-9-1 11:20:32-debug: builder:worker-init (268ms)
2025-9-1 11:20:36-debug: refresh db internal success
2025-9-1 11:20:36-debug: refresh db assets success
2025-9-1 11:20:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:20:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:20:36-debug: asset-db:refresh-all-database (120ms)
2025-9-1 11:21:08-debug: refresh db internal success
2025-9-1 11:21:08-debug: refresh db assets success
2025-9-1 11:21:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:21:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:21:08-debug: asset-db:refresh-all-database (136ms)
2025-9-1 11:21:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:26:37-debug: refresh db internal success
2025-9-1 11:26:37-debug: refresh db assets success
2025-9-1 11:26:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:26:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:26:37-debug: asset-db:refresh-all-database (135ms)
2025-9-1 11:26:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:26:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:33:18-debug: refresh db internal success
2025-9-1 11:33:18-debug: refresh db assets success
2025-9-1 11:33:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:33:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:33:18-debug: asset-db:refresh-all-database (157ms)
2025-9-1 11:33:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:33:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 11:41:16-debug: refresh db internal success
2025-9-1 11:41:16-debug: refresh db assets success
2025-9-1 11:41:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:41:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:41:16-debug: asset-db:refresh-all-database (149ms)
2025-9-1 11:41:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:41:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:41:45-debug: refresh db internal success
2025-9-1 11:41:45-debug: refresh db assets success
2025-9-1 11:41:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:41:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:41:45-debug: asset-db:refresh-all-database (107ms)
2025-9-1 11:42:35-debug: refresh db internal success
2025-9-1 11:42:35-debug: refresh db assets success
2025-9-1 11:42:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:42:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:42:35-debug: asset-db:refresh-all-database (109ms)
2025-9-1 11:42:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:48:46-debug: refresh db internal success
2025-9-1 11:48:46-debug: refresh db assets success
2025-9-1 11:48:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:48:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:48:46-debug: asset-db:refresh-all-database (110ms)
2025-9-1 11:48:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:48:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:49:14-debug: refresh db internal success
2025-9-1 11:49:14-debug: refresh db assets success
2025-9-1 11:49:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:49:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:49:14-debug: asset-db:refresh-all-database (133ms)
2025-9-1 11:49:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:49:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:49:20-debug: refresh db internal success
2025-9-1 11:49:20-debug: refresh db assets success
2025-9-1 11:49:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:49:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:49:20-debug: asset-db:refresh-all-database (100ms)
2025-9-1 11:49:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:49:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:50:19-debug: refresh db internal success
2025-9-1 11:50:19-debug: refresh db assets success
2025-9-1 11:50:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:50:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:50:19-debug: asset-db:refresh-all-database (106ms)
2025-9-1 11:50:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 11:50:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:51:01-debug: refresh db internal success
2025-9-1 11:51:01-debug: refresh db assets success
2025-9-1 11:51:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:51:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:51:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 11:51:01-debug: asset-db:refresh-all-database (103ms)
2025-9-1 11:52:03-debug: refresh db internal success
2025-9-1 11:52:03-debug: refresh db assets success
2025-9-1 11:52:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:52:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:52:03-debug: asset-db:refresh-all-database (131ms)
2025-9-1 11:53:16-debug: refresh db internal success
2025-9-1 11:53:16-debug: refresh db assets success
2025-9-1 11:53:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 11:53:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 11:53:16-debug: asset-db:refresh-all-database (121ms)
2025-9-1 12:10:41-debug: refresh db internal success
2025-9-1 12:10:41-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 12:10:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:10:41-debug: refresh db assets success
2025-9-1 12:10:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:10:41-debug: asset-db:refresh-all-database (161ms)
2025-9-1 12:10:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 12:10:42-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 12:10:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 12:10:42-debug: refresh db internal success
2025-9-1 12:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:10:43-debug: refresh db assets success
2025-9-1 12:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:10:43-debug: asset-db:refresh-all-database (143ms)
2025-9-1 12:11:00-debug: refresh db internal success
2025-9-1 12:11:00-debug: refresh db assets success
2025-9-1 12:11:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:11:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:11:00-debug: asset-db:refresh-all-database (116ms)
2025-9-1 12:11:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 12:11:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 12:11:27-debug: refresh db internal success
2025-9-1 12:11:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:11:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:11:27-debug: refresh db assets success
2025-9-1 12:11:27-debug: asset-db:refresh-all-database (104ms)
2025-9-1 12:11:31-debug: refresh db internal success
2025-9-1 12:11:32-debug: refresh db assets success
2025-9-1 12:11:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:11:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:11:32-debug: asset-db:refresh-all-database (104ms)
2025-9-1 12:11:43-debug: refresh db internal success
2025-9-1 12:11:43-debug: refresh db assets success
2025-9-1 12:11:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:11:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:11:43-debug: asset-db:refresh-all-database (106ms)
2025-9-1 12:11:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 12:11:52-debug: refresh db internal success
2025-9-1 12:11:53-debug: refresh db assets success
2025-9-1 12:11:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:11:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:11:53-debug: asset-db:refresh-all-database (101ms)
2025-9-1 12:18:31-debug: refresh db internal success
2025-9-1 12:18:31-debug: refresh db assets success
2025-9-1 12:18:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:18:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:18:31-debug: asset-db:refresh-all-database (135ms)
2025-9-1 12:18:31-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 12:18:31-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 12:28:40-debug: refresh db internal success
2025-9-1 12:28:40-debug: refresh db assets success
2025-9-1 12:28:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 12:28:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 12:28:40-debug: asset-db:refresh-all-database (126ms)
2025-9-1 12:28:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:31:38-debug: refresh db internal success
2025-9-1 14:31:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:31:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:31:38-debug: refresh db assets success
2025-9-1 14:31:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:31:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:31:38-debug: asset-db:refresh-all-database (152ms)
2025-9-1 14:31:44-debug: refresh db internal success
2025-9-1 14:31:44-debug: refresh db assets success
2025-9-1 14:31:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:31:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:31:44-debug: asset-db:refresh-all-database (110ms)
2025-9-1 14:31:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:31:45-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:31:45-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 14:31:45-debug: refresh db internal success
2025-9-1 14:31:45-debug: refresh db assets success
2025-9-1 14:31:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:31:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:31:45-debug: asset-db:refresh-all-database (102ms)
2025-9-1 14:31:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:32:51-debug: refresh db internal success
2025-9-1 14:32:51-debug: refresh db assets success
2025-9-1 14:32:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:32:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:32:51-debug: asset-db:refresh-all-database (124ms)
2025-9-1 14:32:51-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 14:32:51-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 14:35:43-debug: refresh db internal success
2025-9-1 14:35:44-debug: refresh db assets success
2025-9-1 14:35:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:35:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:35:44-debug: asset-db:refresh-all-database (132ms)
2025-9-1 14:36:07-debug: refresh db internal success
2025-9-1 14:36:07-debug: refresh db assets success
2025-9-1 14:36:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:36:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:36:07-debug: asset-db:refresh-all-database (126ms)
2025-9-1 14:36:11-debug: refresh db internal success
2025-9-1 14:36:11-debug: refresh db assets success
2025-9-1 14:36:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:36:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:36:11-debug: asset-db:refresh-all-database (118ms)
2025-9-1 14:36:34-debug: refresh db internal success
2025-9-1 14:36:34-debug: refresh db assets success
2025-9-1 14:36:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:36:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:36:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:36:34-debug: asset-db:refresh-all-database (123ms)
2025-9-1 14:36:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:40:41-debug: refresh db internal success
2025-9-1 14:40:41-debug: refresh db assets success
2025-9-1 14:40:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:40:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:40:41-debug: asset-db:refresh-all-database (105ms)
2025-9-1 14:40:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:40:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 14:40:47-debug: refresh db internal success
2025-9-1 14:40:47-debug: refresh db assets success
2025-9-1 14:40:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:40:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:40:47-debug: asset-db:refresh-all-database (110ms)
2025-9-1 14:40:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:40:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:41:38-debug: refresh db internal success
2025-9-1 14:41:38-debug: refresh db assets success
2025-9-1 14:41:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:41:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:41:38-debug: asset-db:refresh-all-database (122ms)
2025-9-1 14:41:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:41:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:45:32-debug: refresh db internal success
2025-9-1 14:45:32-debug: refresh db assets success
2025-9-1 14:45:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:45:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:45:32-debug: asset-db:refresh-all-database (139ms)
2025-9-1 14:45:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:45:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:45:34-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:45:34-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 14:45:34-debug: refresh db internal success
2025-9-1 14:45:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:45:35-debug: refresh db assets success
2025-9-1 14:45:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:45:35-debug: asset-db:refresh-all-database (145ms)
2025-9-1 14:45:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:45:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:45:52-debug: refresh db internal success
2025-9-1 14:45:52-debug: refresh db assets success
2025-9-1 14:45:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:45:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:45:52-debug: asset-db:refresh-all-database (105ms)
2025-9-1 14:45:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:45:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:49:08-debug: refresh db internal success
2025-9-1 14:49:08-debug: refresh db assets success
2025-9-1 14:49:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:49:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:49:08-debug: asset-db:refresh-all-database (128ms)
2025-9-1 14:49:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:49:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:49:14-debug: refresh db internal success
2025-9-1 14:49:14-debug: refresh db assets success
2025-9-1 14:49:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:49:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:49:14-debug: asset-db:refresh-all-database (103ms)
2025-9-1 14:49:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:49:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:49:43-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:49:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (11ms)
2025-9-1 14:50:07-debug: refresh db internal success
2025-9-1 14:50:07-debug: refresh db assets success
2025-9-1 14:50:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:50:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:50:07-debug: asset-db:refresh-all-database (120ms)
2025-9-1 14:50:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:50:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:51:21-debug: refresh db internal success
2025-9-1 14:51:21-debug: refresh db assets success
2025-9-1 14:51:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:51:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:51:21-debug: asset-db:refresh-all-database (132ms)
2025-9-1 14:51:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:51:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:51:33-debug: refresh db internal success
2025-9-1 14:51:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:51:33-debug: refresh db assets success
2025-9-1 14:51:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:51:33-debug: asset-db:refresh-all-database (100ms)
2025-9-1 14:51:57-debug: refresh db internal success
2025-9-1 14:51:58-debug: refresh db assets success
2025-9-1 14:51:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:51:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:51:58-debug: asset-db:refresh-all-database (121ms)
2025-9-1 14:52:49-debug: refresh db internal success
2025-9-1 14:52:49-debug: refresh db assets success
2025-9-1 14:52:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:52:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:52:49-debug: asset-db:refresh-all-database (135ms)
2025-9-1 14:52:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:52:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 14:52:58-debug: refresh db internal success
2025-9-1 14:52:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:52:59-debug: refresh db assets success
2025-9-1 14:52:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:52:59-debug: asset-db:refresh-all-database (104ms)
2025-9-1 14:52:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:52:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:53:02-debug: refresh db internal success
2025-9-1 14:53:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:53:02-debug: refresh db assets success
2025-9-1 14:53:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:53:02-debug: asset-db:refresh-all-database (109ms)
2025-9-1 14:53:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:53:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 14:53:23-debug: refresh db internal success
2025-9-1 14:53:23-debug: refresh db assets success
2025-9-1 14:53:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:53:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:53:23-debug: asset-db:refresh-all-database (122ms)
2025-9-1 14:53:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 14:53:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:53:32-debug: refresh db internal success
2025-9-1 14:53:32-debug: refresh db assets success
2025-9-1 14:53:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:53:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:53:32-debug: asset-db:refresh-all-database (108ms)
2025-9-1 14:53:34-debug: refresh db internal success
2025-9-1 14:53:34-debug: refresh db assets success
2025-9-1 14:53:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:53:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:53:34-debug: asset-db:refresh-all-database (106ms)
2025-9-1 14:53:34-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 14:53:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 14:55:04-debug: refresh db internal success
2025-9-1 14:55:04-log: 资源数据库已锁定，资源操作(bound _reimportAsset)将会延迟响应，请稍侯
2025-9-1 14:55:04-debug: refresh db assets success
2025-9-1 14:55:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:55:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:55:04-debug: asset-db:refresh-all-database (134ms)
2025-9-1 14:55:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 14:55:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 14:55:04-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-1 14:55:13-debug: refresh db internal success
2025-9-1 14:55:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:55:13-debug: refresh db assets success
2025-9-1 14:55:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:55:13-debug: asset-db:refresh-all-database (103ms)
2025-9-1 14:55:16-debug: refresh db internal success
2025-9-1 14:55:16-debug: refresh db assets success
2025-9-1 14:55:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 14:55:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 14:55:16-debug: asset-db:refresh-all-database (100ms)
2025-9-1 15:10:10-debug: refresh db internal success
2025-9-1 15:10:10-debug: refresh db assets success
2025-9-1 15:10:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:10:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:10:10-debug: asset-db:refresh-all-database (130ms)
2025-9-1 15:10:10-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 15:10:10-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 15:10:13-debug: refresh db internal success
2025-9-1 15:10:13-debug: refresh db assets success
2025-9-1 15:10:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:10:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:10:13-debug: asset-db:refresh-all-database (152ms)
2025-9-1 15:10:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 15:10:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 15:10:21-debug: refresh db internal success
2025-9-1 15:10:21-debug: refresh db assets success
2025-9-1 15:10:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:10:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:10:21-debug: asset-db:refresh-all-database (101ms)
2025-9-1 15:10:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:10:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:11:14-debug: refresh db internal success
2025-9-1 15:11:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:11:14-debug: refresh db assets success
2025-9-1 15:11:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:11:14-debug: asset-db:refresh-all-database (121ms)
2025-9-1 15:11:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:11:24-debug: refresh db internal success
2025-9-1 15:11:24-debug: refresh db assets success
2025-9-1 15:11:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:11:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:11:24-debug: asset-db:refresh-all-database (99ms)
2025-9-1 15:11:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:11:34-debug: refresh db internal success
2025-9-1 15:11:34-debug: refresh db assets success
2025-9-1 15:11:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:11:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:11:34-debug: asset-db:refresh-all-database (103ms)
2025-9-1 15:12:05-debug: refresh db internal success
2025-9-1 15:12:05-debug: refresh db assets success
2025-9-1 15:12:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:12:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:12:05-debug: asset-db:refresh-all-database (125ms)
2025-9-1 15:12:14-debug: refresh db internal success
2025-9-1 15:12:14-debug: refresh db assets success
2025-9-1 15:12:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:12:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:12:14-debug: asset-db:refresh-all-database (102ms)
2025-9-1 15:12:27-debug: refresh db internal success
2025-9-1 15:12:27-log: 资源数据库已锁定，资源操作(bound _reimportAsset)将会延迟响应，请稍侯
2025-9-1 15:12:27-debug: refresh db assets success
2025-9-1 15:12:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:12:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:12:27-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:12:27-debug: asset-db:refresh-all-database (121ms)
2025-9-1 15:12:27-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-1 15:12:27-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-1 15:12:27-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-1 15:12:37-debug: refresh db internal success
2025-9-1 15:12:37-debug: refresh db assets success
2025-9-1 15:12:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:12:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:12:37-debug: asset-db:refresh-all-database (101ms)
2025-9-1 15:12:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:13:28-debug: refresh db internal success
2025-9-1 15:13:28-debug: refresh db assets success
2025-9-1 15:13:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:13:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:13:28-debug: asset-db:refresh-all-database (134ms)
2025-9-1 15:13:28-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-1 15:13:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-1 15:13:34-debug: refresh db internal success
2025-9-1 15:13:34-debug: refresh db assets success
2025-9-1 15:13:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-1 15:13:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-1 15:13:34-debug: asset-db:refresh-all-database (105ms)
2025-9-1 15:13:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-1 15:13:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-1 15:13:34-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-1 15:13:34-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
