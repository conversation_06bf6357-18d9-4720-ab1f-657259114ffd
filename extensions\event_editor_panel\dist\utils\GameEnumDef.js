"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eBulletActionType = exports.eEmitterActionType = exports.eBulletCondition = exports.eEmitterCondition = void 0;
var eEmitterCondition;
(function (eEmitterCondition) {
    eEmitterCondition[eEmitterCondition["Level_Duration"] = 1] = "Level_Duration";
    eEmitterCondition[eEmitterCondition["Level_Distance"] = 2] = "Level_Distance";
    eEmitterCondition[eEmitterCondition["Level_InfLevel"] = 3] = "Level_InfLevel";
    eEmitterCondition[eEmitterCondition["Level_ChallengeLevel"] = 4] = "Level_ChallengeLevel";
    eEmitterCondition[eEmitterCondition["Player_ActLevel"] = 5] = "Player_ActLevel";
    eEmitterCondition[eEmitterCondition["Player_PosX"] = 6] = "Player_PosX";
    eEmitterCondition[eEmitterCondition["Player_PosY"] = 7] = "Player_PosY";
    eEmitterCondition[eEmitterCondition["Player_LifePercent"] = 8] = "Player_LifePercent";
    eEmitterCondition[eEmitterCondition["Player_GainBuff"] = 9] = "Player_GainBuff";
    eEmitterCondition[eEmitterCondition["Unit_Life"] = 10] = "Unit_Life";
    eEmitterCondition[eEmitterCondition["Unit_LifePercent"] = 11] = "Unit_LifePercent";
    eEmitterCondition[eEmitterCondition["Unit_Duration"] = 12] = "Unit_Duration";
    eEmitterCondition[eEmitterCondition["Unit_PosX"] = 13] = "Unit_PosX";
    eEmitterCondition[eEmitterCondition["Unit_PosY"] = 14] = "Unit_PosY";
    eEmitterCondition[eEmitterCondition["Unit_Speed"] = 15] = "Unit_Speed";
    eEmitterCondition[eEmitterCondition["Unit_SpeedAngle"] = 16] = "Unit_SpeedAngle";
    eEmitterCondition[eEmitterCondition["Unit_Acceleration"] = 17] = "Unit_Acceleration";
    eEmitterCondition[eEmitterCondition["Unit_AccelerationAngle"] = 18] = "Unit_AccelerationAngle";
    eEmitterCondition[eEmitterCondition["Unit_DistanceToPlayer"] = 19] = "Unit_DistanceToPlayer";
    eEmitterCondition[eEmitterCondition["Unit_AngleToPlayer"] = 20] = "Unit_AngleToPlayer";
    eEmitterCondition[eEmitterCondition["Emitter_Active"] = 21] = "Emitter_Active";
    eEmitterCondition[eEmitterCondition["Emitter_InitialDelay"] = 22] = "Emitter_InitialDelay";
    eEmitterCondition[eEmitterCondition["Emitter_Prewarm"] = 23] = "Emitter_Prewarm";
    eEmitterCondition[eEmitterCondition["Emitter_PrewarmDuration"] = 24] = "Emitter_PrewarmDuration";
    eEmitterCondition[eEmitterCondition["Emitter_Duration"] = 25] = "Emitter_Duration";
    eEmitterCondition[eEmitterCondition["Emitter_ElapsedTime"] = 26] = "Emitter_ElapsedTime";
    eEmitterCondition[eEmitterCondition["Emitter_Loop"] = 27] = "Emitter_Loop";
    eEmitterCondition[eEmitterCondition["Emitter_LoopInterval"] = 28] = "Emitter_LoopInterval";
    eEmitterCondition[eEmitterCondition["Emitter_EmitInterval"] = 29] = "Emitter_EmitInterval";
    eEmitterCondition[eEmitterCondition["Emitter_EmitCount"] = 30] = "Emitter_EmitCount";
    eEmitterCondition[eEmitterCondition["Emitter_EmitOffsetX"] = 31] = "Emitter_EmitOffsetX";
    eEmitterCondition[eEmitterCondition["Emitter_Angle"] = 32] = "Emitter_Angle";
    eEmitterCondition[eEmitterCondition["Emitter_Count"] = 33] = "Emitter_Count";
    eEmitterCondition[eEmitterCondition["Bullet_Sprite"] = 34] = "Bullet_Sprite";
    eEmitterCondition[eEmitterCondition["Bullet_Scale"] = 35] = "Bullet_Scale";
    eEmitterCondition[eEmitterCondition["Bullet_ColorR"] = 36] = "Bullet_ColorR";
    eEmitterCondition[eEmitterCondition["Bullet_ColorG"] = 37] = "Bullet_ColorG";
    eEmitterCondition[eEmitterCondition["Bullet_ColorB"] = 38] = "Bullet_ColorB";
    eEmitterCondition[eEmitterCondition["Bullet_Duration"] = 39] = "Bullet_Duration";
    eEmitterCondition[eEmitterCondition["Bullet_ElapsedTime"] = 40] = "Bullet_ElapsedTime";
    eEmitterCondition[eEmitterCondition["Bullet_Speed"] = 41] = "Bullet_Speed";
    eEmitterCondition[eEmitterCondition["Bullet_Acceleration"] = 42] = "Bullet_Acceleration";
    eEmitterCondition[eEmitterCondition["Bullet_AccelerationAngle"] = 43] = "Bullet_AccelerationAngle";
    eEmitterCondition[eEmitterCondition["Bullet_FacingMoveDir"] = 44] = "Bullet_FacingMoveDir";
    eEmitterCondition[eEmitterCondition["Bullet_TrackingTarget"] = 45] = "Bullet_TrackingTarget";
    eEmitterCondition[eEmitterCondition["Bullet_Destructive"] = 46] = "Bullet_Destructive";
    eEmitterCondition[eEmitterCondition["Bullet_DestructiveOnHit"] = 47] = "Bullet_DestructiveOnHit";
})(eEmitterCondition || (exports.eEmitterCondition = eEmitterCondition = {}));
var eBulletCondition;
(function (eBulletCondition) {
    eBulletCondition[eBulletCondition["Bullet_Duration"] = 100] = "Bullet_Duration";
    eBulletCondition[eBulletCondition["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
    eBulletCondition[eBulletCondition["Bullet_PosX"] = 102] = "Bullet_PosX";
    eBulletCondition[eBulletCondition["Bullet_PosY"] = 103] = "Bullet_PosY";
    eBulletCondition[eBulletCondition["Bullet_Damage"] = 104] = "Bullet_Damage";
    eBulletCondition[eBulletCondition["Bullet_Speed"] = 105] = "Bullet_Speed";
    eBulletCondition[eBulletCondition["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
    eBulletCondition[eBulletCondition["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
    eBulletCondition[eBulletCondition["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
    eBulletCondition[eBulletCondition["Bullet_Scale"] = 109] = "Bullet_Scale";
    eBulletCondition[eBulletCondition["Bullet_ColorR"] = 110] = "Bullet_ColorR";
    eBulletCondition[eBulletCondition["Bullet_ColorG"] = 111] = "Bullet_ColorG";
    eBulletCondition[eBulletCondition["Bullet_ColorB"] = 112] = "Bullet_ColorB";
    eBulletCondition[eBulletCondition["Bullet_FacingMoveDir"] = 113] = "Bullet_FacingMoveDir";
    eBulletCondition[eBulletCondition["Bullet_TrackingTarget"] = 114] = "Bullet_TrackingTarget";
    eBulletCondition[eBulletCondition["Bullet_Destructive"] = 115] = "Bullet_Destructive";
    eBulletCondition[eBulletCondition["Bullet_DestructiveOnHit"] = 116] = "Bullet_DestructiveOnHit";
})(eBulletCondition || (exports.eBulletCondition = eBulletCondition = {}));
var eEmitterActionType;
(function (eEmitterActionType) {
    eEmitterActionType[eEmitterActionType["Emitter_Active"] = 1] = "Emitter_Active";
    eEmitterActionType[eEmitterActionType["Emitter_InitialDelay"] = 2] = "Emitter_InitialDelay";
    eEmitterActionType[eEmitterActionType["Emitter_Prewarm"] = 3] = "Emitter_Prewarm";
    eEmitterActionType[eEmitterActionType["Emitter_PrewarmDuration"] = 4] = "Emitter_PrewarmDuration";
    eEmitterActionType[eEmitterActionType["Emitter_Duration"] = 5] = "Emitter_Duration";
    eEmitterActionType[eEmitterActionType["Emitter_ElapsedTime"] = 6] = "Emitter_ElapsedTime";
    eEmitterActionType[eEmitterActionType["Emitter_Loop"] = 7] = "Emitter_Loop";
    eEmitterActionType[eEmitterActionType["Emitter_LoopInterval"] = 8] = "Emitter_LoopInterval";
    eEmitterActionType[eEmitterActionType["Emitter_PerEmitCount"] = 9] = "Emitter_PerEmitCount";
    eEmitterActionType[eEmitterActionType["Emitter_PerEmitInterval"] = 10] = "Emitter_PerEmitInterval";
    eEmitterActionType[eEmitterActionType["Emitter_PerEmitOffsetX"] = 11] = "Emitter_PerEmitOffsetX";
    eEmitterActionType[eEmitterActionType["Emitter_Angle"] = 12] = "Emitter_Angle";
    eEmitterActionType[eEmitterActionType["Emitter_Count"] = 13] = "Emitter_Count";
    eEmitterActionType[eEmitterActionType["Bullet_Duration"] = 14] = "Bullet_Duration";
    eEmitterActionType[eEmitterActionType["Bullet_ElapsedTime"] = 15] = "Bullet_ElapsedTime";
    eEmitterActionType[eEmitterActionType["Bullet_PosX"] = 16] = "Bullet_PosX";
    eEmitterActionType[eEmitterActionType["Bullet_PosY"] = 17] = "Bullet_PosY";
    eEmitterActionType[eEmitterActionType["Bullet_Damage"] = 18] = "Bullet_Damage";
    eEmitterActionType[eEmitterActionType["Bullet_Speed"] = 19] = "Bullet_Speed";
    eEmitterActionType[eEmitterActionType["Bullet_SpeedAngle"] = 20] = "Bullet_SpeedAngle";
    eEmitterActionType[eEmitterActionType["Bullet_Acceleration"] = 21] = "Bullet_Acceleration";
    eEmitterActionType[eEmitterActionType["Bullet_AccelerationAngle"] = 22] = "Bullet_AccelerationAngle";
    eEmitterActionType[eEmitterActionType["Bullet_Scale"] = 23] = "Bullet_Scale";
    eEmitterActionType[eEmitterActionType["Bullet_ColorR"] = 24] = "Bullet_ColorR";
    eEmitterActionType[eEmitterActionType["Bullet_ColorG"] = 25] = "Bullet_ColorG";
    eEmitterActionType[eEmitterActionType["Bullet_ColorB"] = 26] = "Bullet_ColorB";
    eEmitterActionType[eEmitterActionType["Bullet_ColorA"] = 27] = "Bullet_ColorA";
    eEmitterActionType[eEmitterActionType["Bullet_FacingMoveDir"] = 28] = "Bullet_FacingMoveDir";
    eEmitterActionType[eEmitterActionType["Bullet_TrackingTarget"] = 29] = "Bullet_TrackingTarget";
    eEmitterActionType[eEmitterActionType["Bullet_Destructive"] = 30] = "Bullet_Destructive";
    eEmitterActionType[eEmitterActionType["Bullet_DestructiveOnHit"] = 31] = "Bullet_DestructiveOnHit";
    eEmitterActionType[eEmitterActionType["Unit_Life"] = 32] = "Unit_Life";
    eEmitterActionType[eEmitterActionType["Unit_LifePercent"] = 33] = "Unit_LifePercent";
    eEmitterActionType[eEmitterActionType["Unit_PosX"] = 34] = "Unit_PosX";
    eEmitterActionType[eEmitterActionType["Unit_PosY"] = 35] = "Unit_PosY";
    eEmitterActionType[eEmitterActionType["Unit_Speed"] = 36] = "Unit_Speed";
    eEmitterActionType[eEmitterActionType["Unit_SpeedAngle"] = 37] = "Unit_SpeedAngle";
    eEmitterActionType[eEmitterActionType["Unit_Acceleration"] = 38] = "Unit_Acceleration";
    eEmitterActionType[eEmitterActionType["Unit_AccelerationAngle"] = 39] = "Unit_AccelerationAngle";
})(eEmitterActionType || (exports.eEmitterActionType = eEmitterActionType = {}));
/**
 * ActionType对应要修改的属性
 * 以下是子弹的行为
 */
var eBulletActionType;
(function (eBulletActionType) {
    eBulletActionType[eBulletActionType["Bullet_Duration"] = 100] = "Bullet_Duration";
    eBulletActionType[eBulletActionType["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
    eBulletActionType[eBulletActionType["Bullet_PosX"] = 102] = "Bullet_PosX";
    eBulletActionType[eBulletActionType["Bullet_PosY"] = 103] = "Bullet_PosY";
    eBulletActionType[eBulletActionType["Bullet_Damage"] = 104] = "Bullet_Damage";
    eBulletActionType[eBulletActionType["Bullet_Speed"] = 105] = "Bullet_Speed";
    eBulletActionType[eBulletActionType["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
    eBulletActionType[eBulletActionType["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
    eBulletActionType[eBulletActionType["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
    eBulletActionType[eBulletActionType["Bullet_Scale"] = 109] = "Bullet_Scale";
    eBulletActionType[eBulletActionType["Bullet_ColorR"] = 110] = "Bullet_ColorR";
    eBulletActionType[eBulletActionType["Bullet_ColorG"] = 111] = "Bullet_ColorG";
    eBulletActionType[eBulletActionType["Bullet_ColorB"] = 112] = "Bullet_ColorB";
    eBulletActionType[eBulletActionType["Bullet_ColorA"] = 113] = "Bullet_ColorA";
    eBulletActionType[eBulletActionType["Bullet_FacingMoveDir"] = 114] = "Bullet_FacingMoveDir";
    eBulletActionType[eBulletActionType["Bullet_TrackingTarget"] = 115] = "Bullet_TrackingTarget";
    eBulletActionType[eBulletActionType["Bullet_Destructive"] = 116] = "Bullet_Destructive";
    eBulletActionType[eBulletActionType["Bullet_DestructiveOnHit"] = 117] = "Bullet_DestructiveOnHit";
})(eBulletActionType || (exports.eBulletActionType = eBulletActionType = {}));
//# sourceMappingURL=data:application/json;base64,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