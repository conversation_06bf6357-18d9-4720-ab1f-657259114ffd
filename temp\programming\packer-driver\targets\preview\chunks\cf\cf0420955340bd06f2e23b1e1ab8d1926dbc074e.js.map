{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts"], "names": ["EventGroupContext", "Condition<PERSON><PERSON><PERSON>", "EventGroup", "Comparer", "ConditionFactory", "ActionFactory", "eEmitterCondition", "eBulletActionType", "eConditionOp", "eCompareOp", "emitter_cond", "bullet_act", "BulletSystem", "emitter", "bullet", "reset", "conditions", "evaluate", "context", "length", "result", "i", "condition", "conditionResult", "data", "op", "And", "Or", "eEventGroupStatus", "status", "_status", "constructor", "ctx", "<PERSON><PERSON><PERSON><PERSON>", "actions", "_triggerCount", "Idle", "buildConditionChain", "map", "actionData", "action", "create", "onLoad", "changeStatus", "start", "Waiting", "stop", "Stopped", "canExecute", "tick", "dt", "Active", "tickActive", "newStatus", "onCreateEventGroup", "onDestroyEventGroup", "chain", "for<PERSON>ach", "condData", "index", "push", "isAllFinished", "isCompleted", "onExecute", "triggerCount", "compare", "a", "b", "Equal", "NotEqual", "Greater", "GreaterEqual", "Less", "LessEqual", "Error", "type", "Emitter_Active", "EmitterCondition_Active", "Emitter_InitialDelay", "EmitterCondition_InitialDelay", "Emitter_Prewarm", "EmitterCondition_Prewarm", "Bullet_Duration", "BulletAction_Duration", "Bullet_ElapsedTime", "BulletAction_ElapsedTime", "Bullet_PosX", "BulletAction_PosX", "Bullet_PosY", "BulletAction_PosY", "Bullet_Speed", "BulletAction_Speed", "Bullet_SpeedAngle", "BulletAction_SpeedAngle", "Bullet_Acceleration", "BulletAction_Acceleration", "Bullet_AccelerationAngle", "BulletAction_AccelerationAngle"], "mappings": ";;;sIAcaA,iB,EAYPC,c,EA+BOC,U,EAgHAC,Q,EAsBPC,gB,EAgBAC,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7MGC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;AAGAC,MAAAA,Y,iBAAAA,Y;AAAcC,MAAAA,U,iBAAAA,U;;AACXC,MAAAA,Y;;AAGAC,MAAAA,U;;AACHC,MAAAA,Y,iBAAAA,Y;;;;;;;AAET;mCACaZ,iB,GAAN,MAAMA,iBAAN,CAAwB;AAAA;AAAA,eAC3Ba,OAD2B,GACD,IADC;AAAA,eAE3BC,MAF2B,GAEO,IAFP;AAAA;;AAG3B;AAEAC,QAAAA,KAAK,GAAS;AACV,eAAKF,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACH;;AAR0B,O,GAW/B;;;AACMb,MAAAA,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjBe,UADiB,GACoB,EADpB;AAAA;;AAGjBC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AAC1C,cAAI,KAAKF,UAAL,CAAgBG,MAAhB,KAA2B,CAA/B,EAAkC,OAAO,IAAP;AAElC,cAAIC,MAAM,GAAG,KAAKJ,UAAL,CAAgB,CAAhB,EAAmBC,QAAnB,CAA4BC,OAA5B,CAAb;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKL,UAAL,CAAgBG,MAApC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,gBAAMC,SAAS,GAAG,KAAKN,UAAL,CAAgBK,CAAhB,CAAlB;AACA,gBAAME,eAAe,GAAGD,SAAS,CAACL,QAAV,CAAmBC,OAAnB,CAAxB;;AAEA,gBAAII,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaC,GAAvC,EAA4C;AACxCN,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH,aAFD,MAEO,IAAID,SAAS,CAACE,IAAV,CAAeC,EAAf,KAAsB;AAAA;AAAA,8CAAaE,EAAvC,EAA2C;AAC9CP,cAAAA,MAAM,GAAGA,MAAM,IAAIG,eAAnB;AACH;AACJ;;AAED,iBAAOH,MAAP;AACH;;AApBgB,O,EAuBrB;;mCACYQ,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;4BAOC1B,U,GAAN,MAAMA,UAAN,CAAiB;AASV,YAAN2B,MAAM,GAAsB;AAC5B,iBAAO,KAAKC,OAAZ;AACH;;AAEDC,QAAAA,WAAW,CAACC,GAAD,EAAyBR,IAAzB,EAA+C;AAAA,eAZjDA,IAYiD;AAAA,eAV1DN,OAU0D;AAAA,eAT1De,cAS0D;AAAA,eAR1DC,OAQ0D;AAAA,eANlDC,aAMkD,GAN1B,CAM0B;AAAA,eALlDL,OAKkD,GALrBF,iBAAiB,CAACQ,IAKG;AACtD,eAAKlB,OAAL,GAAec,GAAf;AACA,eAAKR,IAAL,GAAYA,IAAZ;AACA,eAAKS,cAAL,GAAsB,KAAKI,mBAAL,CAAyBb,IAAI,CAACR,UAA9B,CAAtB;AACA,eAAKkB,OAAL,GAAeV,IAAI,CAACU,OAAL,CAAaI,GAAb,CAAiBC,UAAU,IAAI;AAC1C,gBAAIC,MAAM,GAAGnC,aAAa,CAACoC,MAAd,CAAqBF,UAArB,CAAb;AACAC,YAAAA,MAAM,CAACE,MAAP,CAAc,KAAKxB,OAAnB;AACA,mBAAOsB,MAAP;AACH,WAJc,CAAf;AAMA,eAAKG,YAAL,CAAkBf,iBAAiB,CAACQ,IAApC;AACA,eAAKD,aAAL,GAAqB,CAArB;AACH;;AAEDS,QAAAA,KAAK,GAAS;AACV,eAAKD,YAAL,CAAkBf,iBAAiB,CAACiB,OAApC;AACH;;AAEDC,QAAAA,IAAI,GAAS;AACT;AACA,eAAKH,YAAL,CAAkBf,iBAAiB,CAACmB,OAApC;AACH;;AAEDC,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKf,cAAL,CAAoBhB,QAApB,CAA6B,KAAKC,OAAlC,CAAP;AACH;;AAED+B,QAAAA,IAAI,CAACC,EAAD,EAAmB;AACnB,kBAAQ,KAAKpB,OAAb;AACI,iBAAKF,iBAAiB,CAACQ,IAAvB;AACI;AACA;;AACJ,iBAAKR,iBAAiB,CAACiB,OAAvB;AACI;AACA,kBAAI,KAAKG,UAAL,EAAJ,EAAuB;AACnB;AACA,qBAAKlB,OAAL,GAAeF,iBAAiB,CAACuB,MAAjC;AACH;;AACD;;AACJ,iBAAKvB,iBAAiB,CAACuB,MAAvB;AACI;AACA,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAKtB,iBAAiB,CAACmB,OAAvB;AACI;AACA;AAjBR;AAmBH;;AAEOJ,QAAAA,YAAY,CAACU,SAAD,EAA+B;AAC/C,cAAI,KAAKvB,OAAL,KAAiBuB,SAArB,EAAgC;AAEhC,eAAKvB,OAAL,GAAeuB,SAAf;;AAEA,kBAAQ,KAAKvB,OAAb;AACI,iBAAKF,iBAAiB,CAACiB,OAAvB;AACI;AAAA;AAAA,gDAAaS,kBAAb,CAAgC,IAAhC;AACA;;AACJ,iBAAK1B,iBAAiB,CAACmB,OAAvB;AACI;AAAA;AAAA,gDAAaQ,mBAAb,CAAiC,IAAjC;AACA;;AACJ;AAAS;AAPb;AASH;;AAEOlB,QAAAA,mBAAmB,CAACrB,UAAD,EAAmD;AAC1E,cAAMwC,KAAK,GAAG,IAAIvD,cAAJ,EAAd;AACAe,UAAAA,UAAU,CAACyC,OAAX,CAAmB,CAACC,QAAD,EAAWC,KAAX,KAAqB;AACpC,gBAAMrC,SAAS,GAAGlB,gBAAgB,CAACqC,MAAjB,CAAwBiB,QAAxB,CAAlB;;AACA,gBAAIpC,SAAJ,EAAe;AACXkC,cAAAA,KAAK,CAACxC,UAAN,CAAiB4C,IAAjB,CAAsBtC,SAAtB;AACH;AACJ,WALD;AAMA,iBAAOkC,KAAP;AACH;;AAEOJ,QAAAA,UAAU,CAACF,EAAD,EAAmB;AACjC,cAAIW,aAAa,GAAG,IAApB;;AAEA,eAAK,IAAMrB,MAAX,IAAqB,KAAKN,OAA1B,EAAmC;AAC/B,gBAAIM,MAAM,CAACsB,WAAP,EAAJ,EAA0B;AAC1BtB,YAAAA,MAAM,CAACuB,SAAP,CAAiB,KAAK7C,OAAtB,EAA+BgC,EAA/B;AACAW,YAAAA,aAAa,GAAG,KAAhB;AACH;;AAED,cAAIA,aAAJ,EAAmB;AACf,gBAAI,KAAKrC,IAAL,CAAUwC,YAAV,GAAyB,CAAzB,IAA8B,KAAK7B,aAAL,GAAqB,KAAKX,IAAL,CAAUwC,YAAjE,EAA+E;AAC3E;AACA,mBAAK7B,aAAL;AACA,mBAAKQ,YAAL,CAAkBf,iBAAiB,CAACiB,OAApC;AACH,aAJD,MAKK;AACD,mBAAKF,YAAL,CAAkBf,iBAAiB,CAACmB,OAApC;AACH;AACJ;AACJ;;AA5GmB,O,GA+GxB;;;0BACa5C,Q,GAAN,MAAMA,QAAN,CAAe;AACJ,eAAP8D,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB1C,EAAvB,EAAgD;AAC1D,kBAAQA,EAAR;AACI,iBAAK;AAAA;AAAA,0CAAW2C,KAAhB;AACI,qBAAOF,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,QAAhB;AACI,qBAAOH,CAAC,KAAKC,CAAb;;AACJ,iBAAK;AAAA;AAAA,0CAAWG,OAAhB;AACI,qBAAOJ,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,YAAhB;AACI,qBAAOL,CAAC,IAAIC,CAAZ;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,IAAhB;AACI,qBAAON,CAAC,GAAGC,CAAX;;AACJ,iBAAK;AAAA;AAAA,0CAAWM,SAAhB;AACI,qBAAOP,CAAC,IAAIC,CAAZ;;AACJ;AACI,oBAAM,IAAIO,KAAJ,gCAAuCjD,EAAvC,CAAN;AAdR;AAgBH;;AAlBiB,O,GAqBtB;;;AACMrB,MAAAA,gB,GAAN,MAAMA,gBAAN,CAAuB;AACN,eAANqC,MAAM,CAACjB,IAAD,EAA4C;AACrD,kBAAQA,IAAI,CAACmD,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBC,cAAvB;AACI,qBAAO,IAAIlE,YAAY,CAACmE,uBAAjB,CAAyCrD,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsD,oBAAvB;AACI,qBAAO,IAAIpE,YAAY,CAACqE,6BAAjB,CAA+CvD,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwD,eAAvB;AACI,qBAAO,IAAItE,YAAY,CAACuE,wBAAjB,CAA0CzD,IAA1C,CAAP;AACJ;;AACA;AACI,oBAAM,IAAIkD,KAAJ,8BAAqClD,IAAI,CAACmD,IAA1C,CAAN;AATR;AAWH;;AAbkB,O;AAgBjBtE,MAAAA,a,GAAN,MAAMA,aAAN,CAAoB;AACH,eAANoC,MAAM,CAACjB,IAAD,EAAsC;AAC/C,kBAAQA,IAAI,CAACmD,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBO,eAAvB;AACI,qBAAO,IAAIvE,UAAU,CAACwE,qBAAf,CAAqC3D,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB4D,kBAAvB;AACI,qBAAO,IAAIzE,UAAU,CAAC0E,wBAAf,CAAwC7D,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkB8D,WAAvB;AACI,qBAAO,IAAI3E,UAAU,CAAC4E,iBAAf,CAAiC/D,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBgE,WAAvB;AACI,qBAAO,IAAI7E,UAAU,CAAC8E,iBAAf,CAAiCjE,IAAjC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBkE,YAAvB;AACI,qBAAO,IAAI/E,UAAU,CAACgF,kBAAf,CAAkCnE,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBoE,iBAAvB;AACI,qBAAO,IAAIjF,UAAU,CAACkF,uBAAf,CAAuCrE,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsE,mBAAvB;AACI,qBAAO,IAAInF,UAAU,CAACoF,yBAAf,CAAyCvE,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwE,wBAAvB;AACI,qBAAO,IAAIrF,UAAU,CAACsF,8BAAf,CAA8CzE,IAA9C,CAAP;AACJ;;AACA;AACI,oBAAM,IAAIkD,KAAJ,2BAAkClD,IAAI,CAACmD,IAAvC,CAAN;AAnBR;AAqBH;;AAvBe,O", "sourcesContent": ["import { Emitter } from \"./Emitter\";\r\nimport { <PERSON>etController } from \"./BulletController\";\r\nimport { eEmitterCondition } from \"../data/bullet/EventConditionType\";\r\nimport { eBulletActionType, eEmitterActionType } from \"../data/bullet/EventActionType\";\r\nimport { IEventCondition } from \"./conditions/IEventCondition\";\r\nimport { IEventAction } from \"./actions/IEventAction\";\r\nimport { eConditionOp, eCompareOp, EventGroupData, EventActionData, EventConditionData } from \"../data/bullet/EventGroupData\";\r\nimport * as emitter_cond from \"./conditions/EmitterEventConditions\";\r\nimport * as bullet_cond from \"./conditions/BulletEventConditions\";\r\nimport * as emitter_act from \"./actions/EmitterEventActions\";\r\nimport * as bullet_act from \"./actions/BulletEventActions\";\r\nimport { BulletSystem } from \"./BulletSystem\";\r\n\r\n// context for running condition & action\r\nexport class EventGroupContext {\r\n    emitter: Emitter | null = null;\r\n    bullet: BulletController | null = null;\r\n    // TODO: add level \r\n\r\n    reset(): void {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n    }\r\n}\r\n\r\n// Condition chain with operators\r\nclass ConditionChain {\r\n    conditions: Array<IEventCondition> = [];\r\n\r\n    evaluate(context: EventGroupContext): boolean {\r\n        if (this.conditions.length === 0) return true;\r\n        \r\n        let result = this.conditions[0].evaluate(context);\r\n        \r\n        for (let i = 1; i < this.conditions.length; i++) {\r\n            const condition = this.conditions[i];\r\n            const conditionResult = condition.evaluate(context);\r\n            \r\n            if (condition.data.op === eConditionOp.And) {\r\n                result = result && conditionResult;\r\n            } else if (condition.data.op === eConditionOp.Or) {\r\n                result = result || conditionResult;\r\n            }\r\n        }\r\n        \r\n        return result;\r\n    }\r\n}\r\n\r\n// Updated EventGroup\r\nexport enum eEventGroupStatus {\r\n    Idle,       // not active\r\n    Waiting,    // waiting for conditions to be met\r\n    Active,     // conditions are met, now ticking actions\r\n    Stopped     // stopped\r\n}\r\n\r\nexport class EventGroup {\r\n    readonly data: EventGroupData;\r\n\r\n    context: EventGroupContext;\r\n    conditionChain: ConditionChain;\r\n    actions: IEventAction[];\r\n\r\n    private _triggerCount: number = 0;\r\n    private _status: eEventGroupStatus = eEventGroupStatus.Idle;\r\n    get status(): eEventGroupStatus {\r\n        return this._status;\r\n    }\r\n    \r\n    constructor(ctx: EventGroupContext, data: EventGroupData) {\r\n        this.context = ctx;\r\n        this.data = data;\r\n        this.conditionChain = this.buildConditionChain(data.conditions);\r\n        this.actions = data.actions.map(actionData => {\r\n            let action = ActionFactory.create(actionData);\r\n            action.onLoad(this.context);\r\n            return action;\r\n        });\r\n\r\n        this.changeStatus(eEventGroupStatus.Idle);\r\n        this._triggerCount = 0;\r\n    }\r\n\r\n    start(): void {\r\n        this.changeStatus(eEventGroupStatus.Waiting);\r\n    }\r\n\r\n    stop(): void {\r\n        // both stop and idle will do the trick\r\n        this.changeStatus(eEventGroupStatus.Stopped); \r\n    }\r\n\r\n    canExecute(): boolean {\r\n        return this.conditionChain.evaluate(this.context);\r\n    }\r\n    \r\n    tick(dt: number): void {\r\n        switch (this._status) {\r\n            case eEventGroupStatus.Idle:\r\n                // not active\r\n                break;\r\n            case eEventGroupStatus.Waiting:\r\n                // waiting for conditions to be met\r\n                if (this.canExecute()) {\r\n                    // TODO: 考虑这里检测增加时间间隔来减少消耗\r\n                    this._status = eEventGroupStatus.Active;\r\n                }\r\n                break;\r\n            case eEventGroupStatus.Active:\r\n                // conditions are met, now ticking actions\r\n                this.tickActive(dt);\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                // stopped\r\n                break;\r\n        }\r\n    }\r\n\r\n    private changeStatus(newStatus: eEventGroupStatus) {\r\n        if (this._status === newStatus) return;\r\n\r\n        this._status = newStatus;\r\n    \r\n        switch (this._status) {\r\n            case eEventGroupStatus.Waiting:\r\n                BulletSystem.onCreateEventGroup(this);\r\n                break;\r\n            case eEventGroupStatus.Stopped:\r\n                BulletSystem.onDestroyEventGroup(this);\r\n                break;\r\n            default: break;\r\n        }\r\n    }\r\n\r\n    private buildConditionChain(conditions: EventConditionData[]): ConditionChain {\r\n        const chain = new ConditionChain();\r\n        conditions.forEach((condData, index) => {\r\n            const condition = ConditionFactory.create(condData);\r\n            if (condition) {\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n\r\n    private tickActive(dt: number): void {\r\n        let isAllFinished = true;\r\n\r\n        for (const action of this.actions) {\r\n            if (action.isCompleted()) continue;\r\n            action.onExecute(this.context, dt);\r\n            isAllFinished = false;\r\n        }\r\n\r\n        if (isAllFinished) {\r\n            if (this.data.triggerCount < 0 || this._triggerCount < this.data.triggerCount) {\r\n                // restart\r\n                this._triggerCount++;\r\n                this.changeStatus(eEventGroupStatus.Waiting);\r\n            }\r\n            else {\r\n                this.changeStatus(eEventGroupStatus.Stopped);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// 提供一个静态函数帮助比较value\r\nexport class Comparer {\r\n    static compare(a: number, b: number, op: eCompareOp): boolean {\r\n        switch (op) {\r\n            case eCompareOp.Equal:\r\n                return a === b;\r\n            case eCompareOp.NotEqual:\r\n                return a !== b;\r\n            case eCompareOp.Greater:\r\n                return a > b;\r\n            case eCompareOp.GreaterEqual:\r\n                return a >= b;\r\n            case eCompareOp.Less:\r\n                return a < b;\r\n            case eCompareOp.LessEqual:\r\n                return a <= b;\r\n            default:\r\n                throw new Error(`Unknown compare operator: ${op}`);\r\n        }\r\n    }\r\n}\r\n\r\n// Factory pattern for conditions & actions\r\nclass ConditionFactory {\r\n    static create(data: EventConditionData): IEventCondition {\r\n        switch (data.type) {\r\n            case eEmitterCondition.Emitter_Active:\r\n                return new emitter_cond.EmitterCondition_Active(data);\r\n            case eEmitterCondition.Emitter_InitialDelay:\r\n                return new emitter_cond.EmitterCondition_InitialDelay(data);\r\n            case eEmitterCondition.Emitter_Prewarm:\r\n                return new emitter_cond.EmitterCondition_Prewarm(data);\r\n            // ... other cases\r\n            default:\r\n                throw new Error(`Unknown condition type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\nclass ActionFactory {\r\n    static create(data: EventActionData): IEventAction {\r\n        switch (data.type) {\r\n            case eBulletActionType.Bullet_Duration:\r\n                return new bullet_act.BulletAction_Duration(data);\r\n            case eBulletActionType.Bullet_ElapsedTime:\r\n                return new bullet_act.BulletAction_ElapsedTime(data);\r\n            case eBulletActionType.Bullet_PosX:\r\n                return new bullet_act.BulletAction_PosX(data);\r\n            case eBulletActionType.Bullet_PosY:\r\n                return new bullet_act.BulletAction_PosY(data);\r\n            case eBulletActionType.Bullet_Speed:\r\n                return new bullet_act.BulletAction_Speed(data);\r\n            case eBulletActionType.Bullet_SpeedAngle:\r\n                return new bullet_act.BulletAction_SpeedAngle(data);\r\n            case eBulletActionType.Bullet_Acceleration:\r\n                return new bullet_act.BulletAction_Acceleration(data);\r\n            case eBulletActionType.Bullet_AccelerationAngle:\r\n                return new bullet_act.BulletAction_AccelerationAngle(data);\r\n            // ... other cases\r\n            default:\r\n                throw new Error(`Unknown action type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n"]}