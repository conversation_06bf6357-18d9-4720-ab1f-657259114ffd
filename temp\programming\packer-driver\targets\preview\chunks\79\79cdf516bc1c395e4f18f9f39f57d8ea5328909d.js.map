{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/EmitterEventConditions.ts"], "names": ["EmitterConditionBase", "EmitterCondition_Active", "EmitterCondition_InitialDelay", "EmitterCondition_Prewarm", "EmitterCondition_Duration", "EmitterCondition_ElapsedTime", "Comparer", "eCompareOp", "constructor", "data", "evaluate", "context", "emitter", "isActive", "value", "compare", "initialDelay", "targetValue", "compareOp", "Equal", "isPreWarm", "NotEqual", "emitDuration", "totalElapsedTime"], "mappings": ";;;oDAIaA,oB,EAaAC,uB,EAQAC,6B,EAMAC,wB,EAcAC,yB,EAOAC,4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnDeC,MAAAA,Q,iBAAAA,Q;;AACCC,MAAAA,U,iBAAAA,U;;;;;;;sCAEhBP,oB,GAAN,MAAMA,oBAAN,CAAsD;AAGzDQ,QAAAA,WAAW,CAACC,IAAD,EAA2B;AAAA,eAF7BA,IAE6B;AAClC,eAAKA,IAAL,GAAYA,IAAZ;AACH;;AAEMC,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO,IAAP;AACH;;AATwD,O,GAY7D;;;yCACaV,uB,GAAN,MAAMA,uBAAN,SAAsCD,oBAAtC,CAA2D;AACvDU,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD;AACA,iBAAOA,OAAO,CAACC,OAAR,CAAgBC,QAAhB,CAAyBC,KAAhC;AACH;;AAJ6D,O,GAOlE;;;+CACaZ,6B,GAAN,MAAMA,6BAAN,SAA4CF,oBAA5C,CAAiE;AAC7DU,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAgBI,YAAhB,CAA6BF,KAA9C,EAAqD,KAAKL,IAAL,CAAUQ,WAA/D,EAA4E,KAAKR,IAAL,CAAUS,SAAtF,CAAP;AACH;;AAHmE,O;;0CAM3Df,wB,GAAN,MAAMA,wBAAN,SAAuCH,oBAAvC,CAA4D;AACxDU,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,kBAAQ,KAAKF,IAAL,CAAUS,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOR,OAAO,CAACC,OAAR,CAAgBQ,SAAhB,CAA0BN,KAA1B,MAAqC,KAAKL,IAAL,CAAUQ,WAAV,KAA0B,CAA/D,IAAoE,IAApE,GAA2E,KAAlF;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,QAAhB;AACI,qBAAOV,OAAO,CAACC,OAAR,CAAgBQ,SAAhB,CAA0BN,KAA1B,MAAqC,KAAKL,IAAL,CAAUQ,WAAV,KAA0B,CAA/D,IAAoE,IAApE,GAA2E,KAAlF;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV8D,O,GAanE;;;2CACab,yB,GAAN,MAAMA,yBAAN,SAAwCJ,oBAAxC,CAA6D;AACzDU,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAgBU,YAAhB,CAA6BR,KAA9C,EAAqD,KAAKL,IAAL,CAAUQ,WAA/D,EAA4E,KAAKR,IAAL,CAAUS,SAAtF,CAAP;AACH;;AAH+D,O,GAMpE;;;8CACab,4B,GAAN,MAAMA,4BAAN,SAA2CL,oBAA3C,CAAgE;AAC5DU,QAAAA,QAAQ,CAACC,OAAD,EAAsC;AACjD,iBAAO;AAAA;AAAA,oCAASI,OAAT,CAAiBJ,OAAO,CAACC,OAAR,CAAgBW,gBAAhB,CAAiCT,KAAlD,EAAyD,KAAKL,IAAL,CAAUQ,WAAnE,EAAgF,KAAKR,IAAL,CAAUS,SAA1F,CAAP;AACH;;AAHkE,O", "sourcesContent": ["import { IEventCondition } from \"./IEventCondition\";\r\nimport { EventGroupContext, Comparer } from \"../EventGroup\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class EmitterConditionBase implements IEventCondition {\r\n    readonly data: EventConditionData;\r\n\r\n    constructor(data: EventConditionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return true;\r\n    }\r\n}\r\n\r\n// 发射器是否启用\r\nexport class EmitterCondition_Active extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        // Custom evaluation logic for active condition\r\n        return context.emitter.isActive.value;\r\n    }\r\n}\r\n\r\n// 发射器初始延迟时间\r\nexport class EmitterCondition_InitialDelay extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter.initialDelay.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Prewarm extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter.isPreWarm.value === (this.data.targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\n// 发射器持续时间\r\nexport class EmitterCondition_Duration extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter.emitDuration.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n// 发射器已运行时间\r\nexport class EmitterCondition_ElapsedTime extends EmitterConditionBase {\r\n    public evaluate(context: EventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter.totalElapsedTime.value, this.data.targetValue, this.data.compareOp);\r\n    }\r\n}"]}