"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventGroupDataManager = exports.ActionDefByCategory = exports.ConditionDefByCategory = exports.EventGroupCategory = exports.BulletActionDef = exports.EmitterActionDef = exports.BulletConditionDef = exports.EmitterConditionDef = exports.EasingEnum = exports.CompareOpEnum = exports.ConditionOpEnum = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const GameEnumDef_1 = require("./GameEnumDef");
;
// Enum definitions for UI display
exports.ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};
exports.CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};
// These should match your actual game enums defined in EventConditionType.ts
exports.EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};
exports.EmitterConditionDef = [
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_PrewarmDuration, label: "发射器预热时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Duration, label: "发射器持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_ElapsedTime, label: "发射器运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitCount, label: "发射器开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitInterval, label: "发射器开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_EmitOffsetX, label: "发射器开火X偏移", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Angle, label: "发射器弹道角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Emitter_Count, label: "发射器弹道数量", type: "number" },
    // 子弹
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Life, label: "单位生命", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_LifePercent, label: "单位生命百分比", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosX, label: "单位X坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_PosY, label: "单位Y坐标", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Speed, label: "单位速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_SpeedAngle, label: "单位速度角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_Acceleration, label: "单位加速度", type: "number" },
    { enum: GameEnumDef_1.eEmitterCondition.Unit_AccelerationAngle, label: "单位加速度角度", type: "number" },
];
exports.BulletConditionDef = [
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletCondition.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
exports.EmitterActionDef = [
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Active, label: "发射器启用", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_InitialDelay, label: "发射器初始延迟", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Prewarm, label: "发射器预热", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PrewarmDuration, label: "发射器预热持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Duration, label: "发射器持续时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_ElapsedTime, label: "发射器已运行时间", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Loop, label: "发射器循环", type: "boolean" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_LoopInterval, label: "发射器循环间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitCount, label: "发射器每次开火次数", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitInterval, label: "发射器每次开火间隔", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_PerEmitOffsetX, label: "发射器每次开火偏移X", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Angle, label: "发射器角度", type: "number" },
    { enum: GameEnumDef_1.eEmitterActionType.Emitter_Count, label: "发射器数量", type: "number" },
    // 子弹相关
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
    // 单位相关
];
exports.BulletActionDef = [
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Duration, label: "子弹持续时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ElapsedTime, label: "子弹运行时长", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosX, label: "子弹X坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_PosY, label: "子弹Y坐标", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Damage, label: "子弹伤害", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Speed, label: "子弹速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_SpeedAngle, label: "子弹速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Acceleration, label: "子弹加速度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_AccelerationAngle, label: "子弹加速度角度", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Scale, label: "子弹缩放", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorR, label: "子弹颜色R", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorG, label: "子弹颜色G", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_ColorB, label: "子弹颜色B", type: "number" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_FacingMoveDir, label: "子弹朝向移动方向", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_TrackingTarget, label: "子弹跟踪目标", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_Destructive, label: "子弹可破坏", type: "boolean" },
    { enum: GameEnumDef_1.eBulletActionType.Bullet_DestructiveOnHit, label: "子弹命中时破坏", type: "boolean" },
];
var EventGroupCategory;
(function (EventGroupCategory) {
    EventGroupCategory["Emitter"] = "Emitter";
    EventGroupCategory["Bullet"] = "Bullet";
})(EventGroupCategory || (exports.EventGroupCategory = EventGroupCategory = {}));
// New definition-based mappings using ValueDecorator
exports.ConditionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterConditionDef,
    [EventGroupCategory.Bullet]: exports.BulletConditionDef,
};
exports.ActionDefByCategory = {
    [EventGroupCategory.Emitter]: exports.EmitterActionDef,
    [EventGroupCategory.Bullet]: exports.BulletActionDef,
};
class EventGroupDataManager {
    constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = (0, path_1.join)(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }
    static getInstance() {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }
    /**
     * Get the full path for a category folder
     */
    getCategoryPath(category) {
        return (0, path_1.join)(this.eventsBasePath, category);
    }
    /**
     * Get the full file path for an EventGroupData
     */
    getFilePath(category, name) {
        return (0, path_1.join)(this.getCategoryPath(category), `${name}.json`);
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(dirPath) {
        if (!(0, fs_1.existsSync)(dirPath)) {
            (0, fs_1.mkdirSync)(dirPath, { recursive: true });
        }
    }
    /**
     * Load all EventGroupData files from a category
     */
    loadEventGroupsByCategory(category) {
        const categoryPath = this.getCategoryPath(category);
        if (!(0, fs_1.existsSync)(categoryPath)) {
            return [];
        }
        const files = (0, fs_1.readdirSync)(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups = [];
        for (const file of files) {
            try {
                const filePath = (0, path_1.join)(categoryPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
                const data = JSON.parse(content);
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            }
            catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }
        return eventGroups;
    }
    /**
     * Load a specific EventGroupData by name and category
     */
    loadEventGroup(category, name) {
        const filePath = this.getFilePath(category, name);
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        try {
            const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
            const data = JSON.parse(content);
            data.name = name; // Ensure name is correct
            return data;
        }
        catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }
    /**
     * Save an EventGroupData to file
     */
    saveEventGroup(category, eventGroup) {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            const filePath = this.getFilePath(category, eventGroup.name);
            // Create a copy without the isDirty property for serialization
            const { isDirty } = eventGroup, dataToSave = __rest(eventGroup, ["isDirty"]);
            const content = JSON.stringify(dataToSave, null, 2);
            (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
            // Clear dirty flag after successful save
            eventGroup.isDirty = false;
            return true;
        }
        catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }
    /**
     * Delete an EventGroupData file
     */
    deleteEventGroup(category, name) {
        try {
            const filePath = this.getFilePath(category, name);
            if ((0, fs_1.existsSync)(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }
    /**
     * Check if an EventGroupData file exists
     */
    eventGroupExists(category, name) {
        const filePath = this.getFilePath(category, name);
        return (0, fs_1.existsSync)(filePath);
    }
    /**
     * Find an EventGroupData by name across all categories
     */
    findEventGroup(name) {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }
    /**
     * Generate a unique name for a new EventGroupData
     */
    generateUniqueName(category, baseName = 'EventGroup') {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        return name;
    }
    /**
     * Create a new EventGroupData with default values
     */
    createNewEventGroup(category, name) {
        const finalName = name || this.generateUniqueName(category);
        var eventGroup = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: [],
            isDirty: true // New event groups are dirty by default
        };
        return eventGroup;
    }
    /**
     * Duplicate an existing EventGroupData
     */
    duplicateEventGroup(category, originalName, newName) {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }
        const finalName = newName || this.generateUniqueName(category, originalName);
        // Deep copy the original data
        const duplicate = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => (Object.assign({}, condition))),
            actions: original.actions.map(action => (Object.assign({}, action))),
            isDirty: true // Duplicated event groups are dirty by default
        };
        return duplicate;
    }
    /**
     * Get all EventGroupData names from all categories
     */
    getAllEventGroupNames() {
        const result = {};
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        return result;
    }
}
exports.EventGroupDataManager = EventGroupDataManager;
//# sourceMappingURL=data:application/json;base64,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