System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, Easing, BulletActionBase, BulletAction_Duration, BulletAction_ElapsedTime, BulletAction_PosX, BulletAction_PosY, BulletAction_Speed, BulletAction_SpeedAngle, BulletAction_Acceleration, BulletAction_AccelerationAngle, BulletAction_Scale, BulletAction_ColorR, BulletAction_ColorG, BulletAction_ColorB, BulletAction_FacingMoveDir, BulletAction_Destructive, BulletAction_DestructiveOnHit, _crd;

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "./IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEasing(extras) {
    _reporterNs.report("Easing", "../Easing", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionData(extras) {
    _reporterNs.report("EventActionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export({
    BulletActionBase: void 0,
    BulletAction_Duration: void 0,
    BulletAction_ElapsedTime: void 0,
    BulletAction_PosX: void 0,
    BulletAction_PosY: void 0,
    BulletAction_Speed: void 0,
    BulletAction_SpeedAngle: void 0,
    BulletAction_Acceleration: void 0,
    BulletAction_AccelerationAngle: void 0,
    BulletAction_Scale: void 0,
    BulletAction_ColorR: void 0,
    BulletAction_ColorG: void 0,
    BulletAction_ColorB: void 0,
    BulletAction_FacingMoveDir: void 0,
    BulletAction_Destructive: void 0,
    BulletAction_DestructiveOnHit: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      Easing = _unresolved_2.Easing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "29e6cZjbe5AO5Hs+Z9KZ2+L", "BulletEventActions", undefined);

      _export("BulletActionBase", BulletActionBase = class BulletActionBase {
        constructor(data) {
          this.data = void 0;
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._startValue = 0;
          this.data = data;
        }

        isCompleted() {
          return this._isCompleted;
        }

        canLerp() {
          return true;
        }

        onLoad(context) {
          this._isCompleted = false;
          this._elapsedTime = 0; // override this to get the correct start value

          this._startValue = 0;
        }

        onExecute(context, dt) {
          this._elapsedTime += dt;

          if (this._elapsedTime >= this.data.duration) {
            this.executeInternal(context, this.data.targetValue);
            this._isCompleted = true;
          } else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));
          }
        }

        lerpValue(startValue, targetValue) {
          return (_crd && Easing === void 0 ? (_reportPossibleCrUseOfEasing({
            error: Error()
          }), Easing) : Easing).lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));
        }

        executeInternal(context, value) {// Default implementation does nothing
        }

      });

      _export("BulletAction_Duration", BulletAction_Duration = class BulletAction_Duration extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.duration.value = value;
        }

      });

      _export("BulletAction_ElapsedTime", BulletAction_ElapsedTime = class BulletAction_ElapsedTime extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.elapsedTime = value;
        }

      });

      _export("BulletAction_PosX", BulletAction_PosX = class BulletAction_PosX extends BulletActionBase {
        executeInternal(context, value) {
          const position = context.bullet.node.position;
          context.bullet.node.setPosition(value, position.y);
        }

      });

      _export("BulletAction_PosY", BulletAction_PosY = class BulletAction_PosY extends BulletActionBase {
        executeInternal(context, value) {
          const position = context.bullet.node.position;
          context.bullet.node.setPosition(position.x, value);
        }

      });

      _export("BulletAction_Speed", BulletAction_Speed = class BulletAction_Speed extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.speed.value = value;
        }

      });

      _export("BulletAction_SpeedAngle", BulletAction_SpeedAngle = class BulletAction_SpeedAngle extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.speedAngle.value = value;
        }

      });

      _export("BulletAction_Acceleration", BulletAction_Acceleration = class BulletAction_Acceleration extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.acceleration.value = value;
        }

      });

      _export("BulletAction_AccelerationAngle", BulletAction_AccelerationAngle = class BulletAction_AccelerationAngle extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.accelerationAngle.value = value;
        }

      });

      _export("BulletAction_Scale", BulletAction_Scale = class BulletAction_Scale extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.node.scale.x = value;
        }

      });

      _export("BulletAction_ColorR", BulletAction_ColorR = class BulletAction_ColorR extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.bulletSprite.color.r = value;
        }

      });

      _export("BulletAction_ColorG", BulletAction_ColorG = class BulletAction_ColorG extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.bulletSprite.color.g = value;
        }

      });

      _export("BulletAction_ColorB", BulletAction_ColorB = class BulletAction_ColorB extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.bulletSprite.color.b = value;
        }

      });

      _export("BulletAction_FacingMoveDir", BulletAction_FacingMoveDir = class BulletAction_FacingMoveDir extends BulletActionBase {
        executeInternal(context, value) {
          context.bullet.isFacingMoveDir.value = value === 1;
        }

      });

      _export("BulletAction_Destructive", BulletAction_Destructive = class BulletAction_Destructive extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.isDestructive.value = (value === 1);
        }

      });

      _export("BulletAction_DestructiveOnHit", BulletAction_DestructiveOnHit = class BulletAction_DestructiveOnHit extends BulletActionBase {
        executeInternal(context, value) {// context.bullet!.isPreWarm.value = (value === 1);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4257a30f19efefe2a2d126e30f9765139b1a4208.js.map