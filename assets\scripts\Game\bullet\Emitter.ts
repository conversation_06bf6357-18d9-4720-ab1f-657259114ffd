import { _decorator, misc, instantiate, resources, Node, Prefab, Component, Vec3, Quat } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletController } from './BulletController';
import { EmitterData } from '../data/bullet/EmitterData';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from "./EventGroup";
import { Property, PropertyContainerComponent } from './PropertyContainer';

const { ccclass, executeInEditMode, property, playOnFocus, disallowMultiple, menu  } = _decorator;
const { degreesToRadians, radiansToDegrees } = misc;

export enum eEmitterStatus {
    None, Prewarm, Emitting, LoopEndReached, Completed
}

// 用枚举定义属性
export enum eEmitterProp {
    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, 
    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,
    PerEmitCount, PerEmitInterval, PerEmitOffsetX, 
    Angle, Count, Arc, Radius,
    TotalElapsedTime, 
}

@ccclass('Emitter')
// @inspector('editor/inspector/components/emitter')
@menu('子弹系统/发射器')
@executeInEditMode
@playOnFocus
@disallowMultiple(true)
export class Emitter extends PropertyContainerComponent<eEmitterProp> {

    static kBulletNameInEditor:string = "_bullet_";

    @property({displayName: "子弹ID"})
    public bulletID: number = 0;
    @property({type: EmitterData, displayName: "发射器属性"})
    readonly emitterData: EmitterData = null;
    @property({type: BulletData, displayName: "子弹属性"})
    readonly bulletData: BulletData = null;
    
    protected _bulletPrefab: Prefab = null;
    protected _prewarmEffectPrefab: Prefab = null;
    protected _emitEffectPrefab: Prefab = null;

    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
    public isActive!: Property<boolean>;
    public isOnlyInScreen!: Property<boolean>;
    public isPreWarm!: Property<boolean>;
    public isLoop!: Property<boolean>;
    public initialDelay!: Property<number>;
    public preWarmDuration!: Property<number>;
    public emitBulletID!: Property<number>;
    public emitDuration!: Property<number>;
    public emitInterval!: Property<number>;
    public emitPower!: Property<number>;
    public loopInterval!: Property<number>;
    public perEmitCount!: Property<number>;
    public perEmitInterval!: Property<number>;
    public perEmitOffsetX!: Property<number>;
    public angle!: Property<number>;
    public count!: Property<number>;
    public arc!: Property<number>;
    public radius!: Property<number>;
    public totalElapsedTime!: Property<number>;

    public updateInEditor : boolean = false;  // 是否在编辑器中更新
    protected _status: eEmitterStatus = eEmitterStatus.None;
    protected _statusElapsedTime: number = 0;
    protected _isEmitting: boolean = false;
    protected _nextEmitTime: number = 0;
    protected _timeAccumulator:number = 0; // Accumulate delta time here
    protected _fixedDelta:number = 0.0167; // Fixed time step (e.g., 60 FPS)

    // Per-emit timing tracking
    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];
    protected _eventGroups: EventGroup[] = [];

    get isEmitting(): boolean { return this._isEmitting; }
    get status(): eEmitterStatus { return this._status; }
    get statusElapsedTime(): number { return this._statusElapsedTime; }

    protected start() : void {
        BulletSystem.onCreateEmitter(this);
        this.resetProperties();
        this.createEventGroups();
    }

    protected update(dt : number): void {
        if (EDITOR && this.updateInEditor) {
            // this._timeAccumulator += dt;

            // while (this._timeAccumulator >= this._fixedDelta) {
            //     this._timeAccumulator -= this._fixedDelta; 
                
                this.tick(this._fixedDelta);
                BulletSystem.tickBullets(this._fixedDelta);
                //BulletSystem.tickActionRunners(this._fixedDelta);
            // }
        }
    }

    public resetInEditor() {
        this.updateInEditor = true;
    }

    public onFocusInEditor() {
        this.updateInEditor = true;
        this.resetProperties();
    }

    public onLostFocusInEditor() {
        this.updateInEditor = false;
        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {
            BulletSystem.destroyAllBullets()
        }
    }

    // reset properties from emitterData
    protected resetProperties() {
        if (!this.emitterData) return;

        this.clear();
        this.isActive = this.addProperty(eEmitterProp.IsActive, true);
        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, this.emitterData.isOnlyInScreen);
        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, this.emitterData.isPreWarm);
        this.isLoop = this.addProperty(eEmitterProp.IsLoop, this.emitterData.isLoop);
        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, this.emitterData.initialDelay);
        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, this.emitterData.preWarmDuration);
        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);
        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, this.emitterData.emitDuration);
        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, this.emitterData.emitInterval);
        this.emitPower = this.addProperty(eEmitterProp.EmitPower, this.emitterData.emitPower);
        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, this.emitterData.loopInterval);
        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, this.emitterData.perEmitCount);
        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, this.emitterData.perEmitInterval);
        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, this.emitterData.perEmitOffsetX);
        this.angle = this.addProperty(eEmitterProp.Angle, this.emitterData.angle);
        this.count = this.addProperty(eEmitterProp.Count, this.emitterData.count);
        this.arc = this.addProperty(eEmitterProp.Arc, this.emitterData.arc);
        this.radius = this.addProperty(eEmitterProp.Radius, this.emitterData.radius);
        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);

        this.emitBulletID.on((value) => {
            // TODO: reload bullet prefab
            this._bulletPrefab = null;
        });
        this.isActive.on((value) => {
            if (value) {
                
            } else {
                
            }
        });

        this.notifyAll(true);
    }

    protected createEventGroups(): void {
        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;

        let ctx = new EventGroupContext();
        ctx.emitter = this;
        for (const dataName of this.emitterData.eventGroupData) {
            const eventGroup = new EventGroup(ctx, BulletSystem.loadEmitterEventGroup(dataName));
            this._eventGroups.push(eventGroup);
        }
    }

    /**
     * public apis
     */
    changeStatus(status: eEmitterStatus) {
        this._status = status;
        this._statusElapsedTime = 0;
        this._nextEmitTime = 0;
        // Clear per-emit queue when changing status
        this._perEmitBulletQueue = [];
        if (status === eEmitterStatus.Emitting || status === eEmitterStatus.Prewarm) {
            if (this._eventGroups.length > 0) {
                this._eventGroups.forEach(group => group.start());
            }
        }
        else {
            if (this._eventGroups.length > 0) {
                this._eventGroups.forEach(group => group.stop());
            }
        }
    }

    protected scheduleNextEmit() {
        // Schedule the next emit after emitInterval
        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;
    }

    protected startEmitting() {
        this._isEmitting = true;
        // 下一次update时触发发射
        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
    }
    
    protected stopEmitting() {
        this._isEmitting = false;
        // Clear any scheduled per-emit bullets
        this.unscheduleAllCallbacks();
        // Clear the per-emit bullet queue
        this._perEmitBulletQueue = [];
    }

    protected canEmit(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    protected emit(): void {
        if (this.perEmitInterval.value > 0) {
            // Queue all bullets with their target emission times
            for (let i = 0; i < this.count.value; i++) {
                for (let j = 0; j < this.perEmitCount.value; j++) {
                    const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);
                    this._perEmitBulletQueue.push({
                        index: i,
                        perEmitIndex: j,
                        targetTime: targetTime
                    });
                }
            }

            // Sort by target time to ensure proper order
            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);
        }
        else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count.value; i++) {
                for (let j = 0; j < this.perEmitCount.value; j++) {
                    this.emitSingle(i, j);
                }
            }
        }
    }

    protected processPerEmitQueue(): void {
        // Process bullets that should be emitted based on current time
        while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0];

            // Check if it's time to emit this bullet
            if (this._statusElapsedTime >= nextBullet.targetTime) {
                // Remove from queue and emit
                this._perEmitBulletQueue.shift();
                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
                // No more bullets ready to emit yet
                break;
            }
        }
    }

    protected tryEmit(): boolean {
        if (this.canEmit()) {
            this.emit();
            return true;
        }
        return false;
    }

    protected emitSingle(index:number, perEmitIndex: number) {
        const direction = this.getSpawnDirection(index);
        const position = this.getSpawnPosition(index, perEmitIndex);
        this.createBullet(direction, position);
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;
        const radian = degreesToRadians(this.angle.value + angleOffset);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {
        // add perEmitOffsetX by perEmitIndex
        const perEmitOffsetX = (this.perEmitCount.value > 1 ? (this.perEmitOffsetX.value / (this.perEmitCount.value - 1)) * perEmitIndex - this.perEmitOffsetX.value / 2 : 0);
        if (this.radius.value <= 0) {
            return { x: perEmitOffsetX, y: 0 };
        }

        const direction = this.getSpawnDirection(index);
        return {
            x: direction.x * this.radius.value + perEmitOffsetX,
            y: direction.y * this.radius.value
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {
        if (!this._bulletPrefab) {
            console.warn("Emitter: No bullet prefab assigned");
            if (EDITOR) {
                this.createBulletInEditor(direction, position);
            }
            return;
        }
        
        const bullet = this.instantiateBullet();
        if (!bullet) return;

        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bullet.node.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );

        BulletSystem.onCreateBullet(this, bullet);
        
        // Post set bullet properties
        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));
        bullet.mover.speed *= this.emitPower.value;
        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));
    }

    protected createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {
        // use a default bullet prefab
        const defaultBullet = 'Game/prefabs/Bullet_New';
        // load using resource
        resources.load(defaultBullet, Prefab, (err, prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            this._bulletPrefab = prefab;
            const bullet = this.instantiateBullet();
            if (!bullet) return;

            // Set bullet position relative to emitter
            const emitterPos = this.node.getWorldPosition();
            bullet.node.setWorldPosition(
                emitterPos.x + position.x,
                emitterPos.y + position.y,
                emitterPos.z
            );

            BulletSystem.onCreateBullet(this, bullet);

            // Post set bullet properties
            bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));
            bullet.mover.speed *= this.emitPower.value;
        });
    }

    protected instantiateBullet(): BulletController | null {
        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab);
        if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        const bullet = bulletNode.getComponent(BulletController);
        if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
        }

        if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
        }

        return bullet;
    }

    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {
        if (!prefab) return;

        const effectNode = ObjectPool.getNode(this.node, prefab);
        if (!effectNode) return;

        effectNode.setWorldPosition(position);
        effectNode.setWorldRotation(rotation);
        // Play the effect and destroy it after duration
        // effectNode.getComponent(ParticleSystem)?.play();
        this.scheduleOnce(() => {
            ObjectPool.returnNode(effectNode);
        }, duration);
    }

    /**
     * Return true if this.node is in screen
     */
    protected isInScreen() : boolean {
        // TODO: Get mainCamera.containsNode(this.node)
        return true;
    }

    public tick(deltaTime: number): void {
        if (!this.isActive || !this.isActive.value) {
            return;
        }

        this._statusElapsedTime += deltaTime;
        this.totalElapsedTime.value += deltaTime;

        switch (this._status)
        {
            case eEmitterStatus.None:
                this.updateStatusNone();
                break;
            case eEmitterStatus.Prewarm:
                this.updateStatusPrewarm();
                break;
            case eEmitterStatus.Emitting:
                this.updateStatusEmitting();
                break;
            case eEmitterStatus.LoopEndReached:
                this.updateStatusLoopEndReached();
                break;
            case eEmitterStatus.Completed:
                this.updateStatusCompleted();
                break;
            default:
                break;
        }
    }

    protected updateStatusNone() {
        if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusPrewarm() {
        if (!this.isPreWarm.value)
            this.changeStatus(eEmitterStatus.Emitting);
        else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
                this.changeStatus(eEmitterStatus.Emitting);
            }
        }
    }

    protected updateStatusEmitting() {
        if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop)
                this.changeStatus(eEmitterStatus.LoopEndReached);
            else
                this.changeStatus(eEmitterStatus.Completed);
            return;
        }
        
        // Start emitting if not already started
        if (!this._isEmitting) {
            this.startEmitting();
        }
        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {
            // Check if it's time for the next emit
            this.tryEmit();
            this.scheduleNextEmit();
        }

        // Process per-emit bullet queue based on precise timing
        this.processPerEmitQueue();
    }

    protected updateStatusLoopEndReached() {
        if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Emitting);
        }
    }

    protected updateStatusCompleted() {
        // Do nothing or cleanup if needed
        this.isActive.value = false;
        this.isActive.notify();
    }
}
